import { getDocs, collection, setDoc, doc, deleteDoc, addDoc } from 'firebase/firestore';
import React, { useEffect, useState, useMemo } from 'react';
import { db, api } from './firebase';
import {
  Modal,
  Button,
  Typography,
  message,
  Layout,
  Input,
  Tabs,
  Select,
  Card,
  Row,
  Col,
  Tag,
  Tooltip,
  Space,
  Divider,
  Checkbox,
  Form,
  Popconfirm,
  Badge
} from 'antd';
import { Content } from 'antd/es/layout/layout';
import { AgGridReact } from 'ag-grid-react';
import { themeBalham } from 'ag-grid-community';
import {
  UserAddOutlined,
  TeamOutlined,
  SettingOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SaveOutlined,
  PlusOutlined
} from '@ant-design/icons';
import KPIGoalsTab from '../components/settings/KPIGoalsTab';
import GorgiasTagMappingTab from '../components/settings/GorgiasTagMappingTab';
import UserEditModal from '../components/settings/UserEditModal';

const { Title, Text } = Typography;
const { Option } = Select;

// Permission categories for better organization
const PERMISSION_CATEGORIES = {
  inventory: {
    label: 'Inventory Management',
    color: 'blue',
    permissions: [
      { technicalName: 'read:inventoryreport', label: 'View Inventory Report', description: 'Access to inventory reports' },
      { technicalName: 'read:items', label: 'View Items', description: 'Access to items data' },
      { technicalName: 'read:inventoryExceptions', label: 'View Inventory Exceptions', description: 'Access to inventory exceptions' },
    ]
  },
  products: {
    label: 'Product Management',
    color: 'green',
    permissions: [
      { technicalName: 'read:products', label: 'View Products', description: 'Access to product data' },
      { technicalName: 'edit:products', label: 'Edit Products', description: 'Modify product information' },
      { technicalName: 'read:variants', label: 'View Variants', description: 'Access to variant data' },
      { technicalName: 'edit:variants', label: 'Edit Variants', description: 'Modify variant information' },
    ]
  },
  orders: {
    label: 'Order Management',
    color: 'orange',
    permissions: [
      { technicalName: 'read:wholesale', label: 'View Wholesale', description: 'Access to wholesale orders' },
      { technicalName: 'read:shippingExceptions', label: 'View Shipping Exceptions', description: 'Access to shipping exceptions' },
      { technicalName: 'edit:shippingExceptions', label: 'Edit Shipping Exceptions', description: 'Modify shipping exceptions' },
      { technicalName: 'read:orderAllocation', label: 'View Order Allocation', description: 'Access to order allocation' },
    ]
  },
  forecasting: {
    label: 'Forecasting',
    color: 'purple',
    permissions: [
      { technicalName: 'read:forecastBeta', label: 'View Forecast Beta', description: 'Access to forecast beta features' },
    ]
  },
  analytics: {
    label: 'Analytics & Reports',
    color: 'cyan',
    permissions: [
      { technicalName: 'read:dashboard', label: 'View KPI Dashboard', description: 'Access to KPI dashboard' },
      { technicalName: 'read:data', label: 'View Data', description: 'Access to data management' },
      { technicalName: 'read:upload', label: 'Upload Data', description: 'Upload data files' },
      { technicalName: 'read:utilities', label: 'Access Utilities', description: 'Access to utility tools' },
      { technicalName: 'read:historicalSales', label: 'View Historical Sales', description: 'Access to historical sales' },
    ]
  },
  system: {
    label: 'System Administration',
    color: 'red',
    permissions: [
      { technicalName: 'read:settings', label: 'View Settings', description: 'Access to system settings' },
    ]
  }
};

// Predefined roles with permission bundles
const PREDEFINED_ROLES = {
  admin: {
    label: 'Administrator',
    description: 'Full access to all features and settings',
    color: 'red',
    permissions: Object.values(PERMISSION_CATEGORIES).flatMap(cat => cat.permissions.map(p => p.technicalName))
  },
  operations_manager: {
    label: 'Operations Manager',
    description: 'Access to inventory, orders, and operational data',
    color: 'blue',
    permissions: [
      ...PERMISSION_CATEGORIES.inventory.permissions.map(p => p.technicalName),
      ...PERMISSION_CATEGORIES.orders.permissions.map(p => p.technicalName),
      ...PERMISSION_CATEGORIES.forecasting.permissions.map(p => p.technicalName),
      'read:dashboard'
    ]
  },
  marketing: {
    label: 'Marketing',
    description: 'Access to products, variants, and marketing data',
    color: 'green',
    permissions: [
      ...PERMISSION_CATEGORIES.products.permissions.map(p => p.technicalName),
      'read:dashboard',
      'read:forecastBeta'
    ]
  },
  data_analyst: {
    label: 'Data Analyst',
    description: 'Access to reports, data, and analytics',
    color: 'cyan',
    permissions: [
      ...PERMISSION_CATEGORIES.analytics.permissions.map(p => p.technicalName),
      ...PERMISSION_CATEGORIES.inventory.permissions.map(p => p.technicalName),
      'read:forecastBeta'
    ]
  },
  viewer: {
    label: 'Viewer',
    description: 'Read-only access to assigned areas',
    color: 'default',
    permissions: [
      'read:inventoryreport',
      'read:items',
      'read:products',
      'read:variants',
      'read:dashboard'
    ]
  }
};

const Settings = () => {
  const [users, setUsers] = useState([]);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showEditUserModal, setShowEditUserModal] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [editingUser, setEditingUser] = useState(null);
  const [emailSearch, setEmailSearch] = useState('');
  const [loading, setLoading] = useState(true);
  const [newUserForm] = Form.useForm();
  const [roleForm] = Form.useForm();

  // AG Grid configuration
  const gridOptions = useMemo(() => ({
    getRowId: (params) => params.data.id,
    columnDefs: [
      {
        headerName: '',
        field: 'checkbox',
        sortable: false,
        filter: false,
        width: 50,
        pinned: 'left'
      },
      {
        headerName: 'User',
        field: 'email',
        sortable: true,
        filter: 'agTextColumnFilter',
        filterParams: {
          filterOptions: ['contains', 'equals', 'startsWith', 'endsWith'],
          defaultOption: 'contains'
        },
        width: 250,
        cellRenderer: (params) => (
          <div>
            <div style={{ fontWeight: 'bold' }}>{params.value}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {params.data.role ? `Role: ${PREDEFINED_ROLES[params.data.role]?.label || params.data.role}` : 'No role assigned'}
            </div>
          </div>
        )
      },
      {
        headerName: 'Role',
        field: 'role',
        sortable: true,
        filter: 'agSetColumnFilter',
        filterParams: {
          valueFormatter: (params) => {
            const role = PREDEFINED_ROLES[params.value];
            return role ? role.label : params.value || 'Custom';
          }
        },
        width: 150,
        cellRenderer: (params) => {
          const role = PREDEFINED_ROLES[params.value];
          return role ? (
            <Tag color={role.color}>{role.label}</Tag>
          ) : (
            <Tag color="default">{params.value || 'Custom'}</Tag>
          );
        }
      },
      {
        headerName: 'Permissions',
        field: 'userPermissions',
        sortable: false,
        filter: 'agTextColumnFilter',
        filterParams: {
          filterOptions: ['contains', 'equals', 'startsWith', 'endsWith'],
          textCustomComparator: (filter, value, filterText) => {
            if (!value || !Array.isArray(value)) return false;
            const activePermissions = value.filter(p => p.hasAccess);
            const permissionLabels = activePermissions.map(p => p.label).join(' ');
            return permissionLabels.toLowerCase().includes(filterText.toLowerCase());
          }
        },
        width: 400,
        autoHeight: true,
        cellRenderer: (params) => {
          const permissions = params.data.userPermissions || [];
          const activePermissions = permissions.filter(p => p.hasAccess);

          return (
            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: '4px',
              padding: '8px 0',
              minHeight: '32px',
              alignItems: 'flex-start'
            }}>
              {activePermissions.map((permission) => {
                // Find which category this permission belongs to
                const category = Object.entries(PERMISSION_CATEGORIES).find(([key, cat]) =>
                  cat.permissions.some(p => p.technicalName === permission.technicalName)
                );

                if (!category) return null;

                const [categoryKey, categoryData] = category;
                const permissionData = categoryData.permissions.find(p => p.technicalName === permission.technicalName);

                return (
                  <Tooltip key={permission.technicalName} title={permissionData?.description || permission.label}>
                    <Tag
                      color={categoryData.color}
                      size="small"
                      style={{ margin: '2px 0' }}
                    >
                      {permission.label}
                    </Tag>
                  </Tooltip>
                );
              })}
            </div>
          );
        }
      },
      {
        headerName: 'Actions',
        field: 'actions',
        sortable: false,
        filter: false,
        width: 120,
        cellRenderer: (params) => (
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleEditUser(params.data);
            }}
          >
            Edit
          </Button>
        )
      }
    ],
    rowSelection: {
      mode: 'multiRow',
      checkboxes: true,
      headerCheckbox: true
    },
    pagination: true,
    paginationPageSize: 10,
    paginationPageSizeSelector: [10, 20, 50, 100],
    domLayout: 'autoHeight',
    defaultColDef: {
      resizable: true,
      sortable: true,
      filter: true
    }
  }), []);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const tempDocs = await getDocs(collection(db, 'users'));
      const tempUsers = [];
      for (const t of tempDocs.docs) {
        const userData = t.data();
        tempUsers.push({
          ...userData,
          id: t.id,
        });
      }
      setUsers(tempUsers);
    } catch (error) {
      message.error('Failed to fetch users');
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const removeUser = async (id) => {
    try {
      await deleteDoc(doc(db, 'users', id));
      setUsers(users.filter((user) => user.id !== id));
      message.success('User removed successfully');
    } catch (error) {
      message.error('Failed to remove user');
      console.error('Error removing user:', error);
    }
  };

  const handleAddUser = async (values) => {
    try {
      const { email, role } = values;

      // Create user document with role-based permissions
      const rolePermissions = PREDEFINED_ROLES[role]?.permissions || [];
      const userPermissions = Object.values(PERMISSION_CATEGORIES)
        .flatMap(cat => cat.permissions)
        .map(permission => ({
          ...permission,
          hasAccess: rolePermissions.includes(permission.technicalName)
        }));

      const newUser = {
        email,
        role,
        userPermissions,
        createdAt: new Date().toISOString()
      };

      await addDoc(collection(db, 'users'), newUser);
      message.success('User added successfully');
      setShowAddUserModal(false);
      newUserForm.resetFields();
      fetchUsers();
    } catch (error) {
      message.error('Failed to add user');
      console.error('Error adding user:', error);
    }
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    setShowEditUserModal(true);
  };

  const handleSaveUser = async (updatedUser) => {
    try {
      await setDoc(doc(db, 'users', updatedUser.id), updatedUser, { merge: true });
      message.success('User updated successfully');
      setShowEditUserModal(false);
      setEditingUser(null);
      fetchUsers();
    } catch (error) {
      message.error('Failed to update user');
      console.error('Error updating user:', error);
    }
  };

  const handleBulkRoleAssignment = async (values) => {
    try {
      const { role } = values;
      const rolePermissions = PREDEFINED_ROLES[role]?.permissions || [];

      const updatePromises = selectedUsers.map(userId => {
        const user = users.find(u => u.id === userId);
        if (!user) return Promise.resolve();

        const updatedUser = {
          ...user,
          role,
          userPermissions: Object.values(PERMISSION_CATEGORIES)
            .flatMap(cat => cat.permissions)
            .map(permission => ({
              ...permission,
              hasAccess: rolePermissions.includes(permission.technicalName)
            }))
        };

        return setDoc(doc(db, 'users', userId), updatedUser, { merge: true });
      });

      await Promise.all(updatePromises);
      message.success(`Role assigned to ${selectedUsers.length} users`);
      setShowRoleModal(false);
      roleForm.resetFields();
      setSelectedUsers([]);
      fetchUsers();
    } catch (error) {
      message.error('Failed to assign role');
      console.error('Error assigning role:', error);
    }
  };

  const onSelectionChanged = (event) => {
    const selectedRows = event.api.getSelectedRows();
    console.log('Selection changed:', selectedRows.length, 'rows selected');
    setSelectedUsers(selectedRows);
  };

  const filteredUsers = useMemo(() =>
    users.filter(user =>
      user.email.toLowerCase().includes(emailSearch.toLowerCase())
    ), [users, emailSearch]
  );

  const tabs = [
    {
      key: '1',
      label: (
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <TeamOutlined style={{ marginRight: '8px' }} />
          User Management
        </span>
      ),
      children: (
        <Content style={{ marginTop: '16px' }}>
          <Card>
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <Input.Search
                  placeholder="Search users by email"
                  value={emailSearch}
                  onChange={(e) => setEmailSearch(e.target.value)}
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={12}>
                <Space>
                  <Button
                    type="primary"
                    icon={<UserAddOutlined />}
                    onClick={() => setShowAddUserModal(true)}
                  >
                    Add User
                  </Button>
                  {console.log('Rendering buttons, selectedUsers:', selectedUsers.length)}
                  {selectedUsers.length > 0 && (
                    <>
                      <Button
                        icon={<CopyOutlined />}
                        onClick={() => setShowRoleModal(true)}
                      >
                        Assign Role ({selectedUsers.length})
                      </Button>
                      <Popconfirm
                        title={`Remove ${selectedUsers.length} selected user${selectedUsers.length > 1 ? 's' : ''}?`}
                        description="This action cannot be undone."
                        onConfirm={() => {
                          selectedUsers.forEach(user => removeUser(user.id));
                          setSelectedUsers([]);
                        }}
                        okText="Yes"
                        cancelText="No"
                      >
                        <Button
                          danger
                          icon={<DeleteOutlined />}
                        >
                          Remove ({selectedUsers.length})
                        </Button>
                      </Popconfirm>
                    </>
                  )}
                  {/* <Button 
                    icon={<SaveOutlined />}
                    onClick={() => api.getNsInventoryOnCall()}
                  >
                    Refresh Items
                  </Button> */}
                </Space>
              </Col>
            </Row>

            <div style={{ height: '600px', width: '100%' }}>
              <AgGridReact
                key="users-grid"
                {...gridOptions}
                rowData={filteredUsers}
                onSelectionChanged={onSelectionChanged}
                loading={loading}
                theme={themeBalham}
              />
            </div>
          </Card>
        </Content>
      ),
    },
    {
      key: '2',
      label: (
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <SettingOutlined style={{ marginRight: '8px' }} />
          KPI Goals
        </span>
      ),
      children: <KPIGoalsTab />,
    },
    {
      key: '3',
      label: (
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <SettingOutlined style={{ marginRight: '8px' }} />
          Gorgias Tag Mapping
        </span>
      ),
      children: <GorgiasTagMappingTab />,
    },
  ];

  return (
    <div style={{ background: '#f0f2f5' }}>
      <Layout style={{ background: '#f0f2f5', padding: '0 24px' }}>
        <Title level={2} style={{ margin: '16px 0', display: 'flex', alignItems: 'center' }}>
          <SettingOutlined style={{ marginRight: '12px' }} /> Settings
        </Title>
        <Content>
          <div style={{ background: '#fff', padding: '24px', borderRadius: '8px' }}>
            <Tabs defaultActiveKey="1" items={tabs} />
          </div>
        </Content>
      </Layout>

      {/* Add User Modal */}
      <Modal
        title="Add New User"
        open={showAddUserModal}
        onCancel={() => {
          setShowAddUserModal(false);
          newUserForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={newUserForm}
          layout="vertical"
          onFinish={handleAddUser}
        >
          <Form.Item
            name="email"
            label="Email Address"
            rules={[
              { required: true, message: 'Please enter email address' },
              { type: 'email', message: 'Please enter a valid email' }
            ]}
          >
            <Input placeholder="<EMAIL>" />
          </Form.Item>

          <Form.Item
            name="role"
            label="Role"
            rules={[{ required: true, message: 'Please select a role' }]}
          >
            <Select placeholder="Select a role">
              {Object.entries(PREDEFINED_ROLES).map(([key, role]) => (
                <Option key={key} value={key}>
                  <div>
                    <Tag color={role.color}>{role.label}</Tag>
                    <Text type="secondary" style={{ marginLeft: 8 }}>
                      {role.description}
                    </Text>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Add User
              </Button>
              <Button onClick={() => {
                setShowAddUserModal(false);
                newUserForm.resetFields();
              }}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Bulk Role Assignment Modal */}
      <Modal
        title={`Assign Role to ${selectedUsers.length} Users`}
        open={showRoleModal}
        onCancel={() => {
          setShowRoleModal(false);
          roleForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={roleForm}
          layout="vertical"
          onFinish={handleBulkRoleAssignment}
        >
          <Form.Item
            name="role"
            label="Role"
            rules={[{ required: true, message: 'Please select a role' }]}
          >
            <Select placeholder="Select a role">
              {Object.entries(PREDEFINED_ROLES).map(([key, role]) => (
                <Option key={key} value={key}>
                  <div>
                    <Tag color={role.color}>{role.label}</Tag>
                    <Text type="secondary" style={{ marginLeft: 8 }}>
                      {role.description}
                    </Text>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Assign Role
              </Button>
              <Button onClick={() => {
                setShowRoleModal(false);
                roleForm.resetFields();
              }}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit User Modal */}
      <UserEditModal
        visible={showEditUserModal}
        user={editingUser}
        onCancel={() => {
          setShowEditUserModal(false);
          setEditingUser(null);
        }}
        onSave={handleSaveUser}
        loading={loading}
      />
    </div>
  );
};

export default Settings;