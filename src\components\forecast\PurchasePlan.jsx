import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Spin } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import { api } from '../../pages/firebase';
import { themeBalham } from 'ag-grid-community';

const PurchasePlan = () => {
  const gridRef = useRef();
  const [rowData, setRowData] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const demandQuery = `
        SELECT
          upc,
          SUM(qty) AS forecast_qty
        FROM \`hj-reporting.forecast.demand_plan\`
        WHERE date BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL 30 DAY)
        GROUP BY upc
      `;
      const itemQuery = `
        SELECT
          upc,
          producttype,
          color,
          size,
          unit_per_case
        FROM \`hj-reporting.items.items_netsuite\`
      `;
      const classificationQuery = `
        SELECT upc, safety_stock, min_stock, max_stock
        FROM \`hj-reporting.forecast.item_classification\`
      `;
      const inventoryQuery = `
        SELECT upc, on_hand, available
        FROM \`hj-reporting.inventory.live_inventory\`
      `;
      const [demandRes, itemRes, classRes, invRes] = await Promise.all([
        api.bigQueryRunQueryOnCall({ options: { query: demandQuery } }),
        api.bigQueryRunQueryOnCall({ options: { query: itemQuery } }),
        api.bigQueryRunQueryOnCall({ options: { query: classificationQuery } }),
        api.bigQueryRunQueryOnCall({ options: { query: inventoryQuery } }),
      ]);

      // Handle new structured response format
      if (!demandRes.success) {
        throw new Error(demandRes.error || 'Failed to fetch demand data');
      }
      if (!itemRes.success) {
        throw new Error(itemRes.error || 'Failed to fetch item data');
      }
      if (!classRes.success) {
        throw new Error(classRes.error || 'Failed to fetch classification data');
      }
      if (!invRes.success) {
        throw new Error(invRes.error || 'Failed to fetch inventory data');
      }

      const demandMap = {};
      demandRes.data?.forEach(row => {
        demandMap[row.upc] = Number(row.forecast_qty) || 0;
      });

      const classMap = {};
      classRes.data?.forEach(row => {
        classMap[row.upc] = row;
      });

      const inventoryMap = {};
      invRes.data?.forEach(row => {
        inventoryMap[row.upc] = row;
      });

      const rows = itemRes.data?.map((item, idx) => {
        const demand30 = demandMap[item.upc] || 0;
        const classInfo = classMap[item.upc] || {};
        const invInfo = inventoryMap[item.upc] || {};
        const avgDailyDemand = demand30 / 30;
        const daysOnHand = avgDailyDemand > 0 ? (invInfo.available || 0) / avgDailyDemand : null;
        const masterCase = item.unit_per_case || 1;
        const reco = Math.max(0, (classInfo.safety_stock || 0) + demand30 - (invInfo.available || 0));
        const rounded = Math.ceil(reco / masterCase) * masterCase;
        const nextDate = (() => {
          const d = new Date();
          const freq = 30;
          d.setDate(d.getDate() + freq);
          return d.toISOString().split('T')[0];
        })();
        const productionLead = 5 + idx * 2; // simple placeholder
        return {
          upc: item.upc,
          name: `${item.producttype} ${item.color || ''} ${item.size || ''}`.trim(),
          unit_per_case: item.unit_per_case,
          on_hand: invInfo.on_hand || 0,
          available: invInfo.available || 0,
          safety_stock: classInfo.safety_stock || 0,
          min_stock: classInfo.min_stock || 0,
          max_stock: classInfo.max_stock || 0,
          lead_time: 0,
          production_lead_time: productionLead,
          transit_lead_time: 0,
          order_frequency_days: 30,
          next_order_date: nextDate,
          forecast_30_day: demand30,
          recommended_order: rounded,
          ship_method: '',
          days_on_hand: daysOnHand ? daysOnHand.toFixed(1) : null,
        };
      }) || [];

      setRowData(rows);
    } catch (err) {
      console.error('Failed to load purchase plan data', err);
      setRowData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const columnDefs = useMemo(() => [
    { field: 'upc', headerName: 'UPC', filter: 'agTextColumnFilter', minWidth: 120 },
    { field: 'name', headerName: 'Product', minWidth: 200 },
    { field: 'on_hand', headerName: 'On Hand' },
    { field: 'available', headerName: 'Available' },
    { field: 'forecast_30_day', headerName: '30 Day Demand' },
    { field: 'safety_stock', headerName: 'Safety Stock', editable: true },
    { field: 'min_stock', headerName: 'Min Qty', editable: true },
    { field: 'lead_time', headerName: 'Lead Time', editable: true },
    { field: 'production_lead_time', headerName: 'Prod Lead' },
    { field: 'transit_lead_time', headerName: 'Transit Lead', editable: true },
    { field: 'order_frequency_days', headerName: 'Order Freq', editable: true },
    { field: 'next_order_date', headerName: 'Next Order' },
    { field: 'recommended_order', headerName: 'Recommended PO' },
    { field: 'ship_method', headerName: 'Ship Method', editable: true },
    { field: 'days_on_hand', headerName: 'Days on Hand' },
  ], []);

  const defaultColDef = useMemo(() => ({
    flex: 1,
    minWidth: 110,
    sortable: true,
    resizable: true,
    filter: true,
  }), []);

  const rowClassRules = useMemo(() => ({
    'stockout-row': params => {
      const doh = parseFloat(params.data.days_on_hand);
      return doh !== null && !isNaN(doh) && doh < params.data.lead_time;
    }
  }), []);

  const handleCellChange = params => {
    setRowData(prev => prev.map(r => r.upc === params.data.upc ? { ...params.data } : r));
  };

  return (
    <div style={{ width: '100%', height: '600px' }}>
      {loading ? (
        <Spin style={{ marginTop: 100 }} />
      ) : (
        <AgGridReact
          ref={gridRef}
          rowData={rowData}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
            rowSelection={{
              mode: 'multiple'
            }}
          rowClassRules={rowClassRules}
          onCellValueChanged={handleCellChange}
          stopEditingWhenCellsLoseFocus
          theme={themeBalham}
        />
      )}
    </div>
  );
};

export default PurchasePlan;
