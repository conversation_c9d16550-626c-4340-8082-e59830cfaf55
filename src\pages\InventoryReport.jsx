/* eslint-disable new-cap */
/* eslint-disable react/prop-types */
/* eslint-disable require-jsdoc */
/* eslint-disable guard-for-in */

// todo move inventory report to this page
// import logo from './logo.svg';
import '../App.css';
import { useEffect, useState, useRef } from 'react';
import React from 'react';
import { Tooltip, Button, Input, Select, Modal, Row, Col, Typography, Popconfirm, message, Tag, Space, Switch } from 'antd';
import dayjs from 'dayjs';
const { Title, Text } = Typography;
import { AgGridReact } from 'ag-grid-react';
import { api } from './firebase';
import { collection, onSnapshot, addDoc, doc, setDoc, deleteDoc, query, where, or, getDoc } from 'firebase/firestore';
import { db as firestoreDb } from './firebase';
import { DeleteOutlined, ReloadOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { ShareOutlined } from '@mui/icons-material';
import { useUser } from '../contexts/UserContext';
import SavedViews from '../components/SavedViews';
const { lifeStatusColors, PRODUCT_SPECIFICATION_COLORS } = require('../constants');

function InventoryReport() {
  const { userData } = useUser();
  const [data, setData] = useState([]);
  const [loadingData, setLoadingData] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isCondensedView, setIsCondensedView] = useState(false);
  const [showDetailPopup, setShowDetailPopup] = useState(false);
  const [detailSku, setDetailSku] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const hasFetchedData = useRef(false);
  const gridRef = useRef(null);

  // Search States
  const [omniSearch, setOmniSearch] = useState({
    upc: '',
  });

  const formatAsCurrency = (val) => {
    if (val === null || val === undefined || val === '') return '$0.00';
    const num = parseFloat(val);
    return isNaN(num) ? '$0.00' : `$${num.toFixed(2)}`;
  };
  
  const formatAsLargeInt = (val) => {
    if (val === null || val === undefined || val === '') return '0';
    const num = parseInt(val);
    return isNaN(num) ? '0' : num.toLocaleString();
  };

  const fetchData = async (locs, retryCount = 0) => {
    const maxRetries = 3;
    const retryDelay = Math.min(2000 * Math.pow(2, retryCount), 15000); // Exponential backoff, max 15s

    setLoadingData(true);
    try {
      // Show loading message on first attempt
      if (retryCount === 0) {
        message.loading('Loading inventory data...', 3);
      }

      // pull item data from bigquery
      const itemProm = api.bigQueryRunQueryOnCall({
        options: {
          query: `select 
                    upc, 
                    externalid,
                    producttype, 
                    color, 
                    size, 
                    image, 
                    baseprice, 
                    productspecification,
                    productdivision,
                    productcategory,
                    productform,
                    lifestatus,
                    launchdate,
                    enddate,
                    obsoletedate
                  FROM \`hj-reporting.items.items_netsuite\`
                  WHERE upc IS NOT NULL
                  and inactive!=true
        `,
        },
      });
      const inventoryProm = api.bigQueryRunQueryOnCall({
        options: {
          query: `
            SELECT
              locationId,
              locationName,
              upc, 
              promiseDate, 
              invPulledAt, 
              presaleQty, 
              onOrderQty, 
              inTransitQty,
              nsQty, 
              onHandQty,
              availableQty, 
              backorderedQty, 
              qty, 
              committedQty, 
              reserveQty, 
              presaleQty, 
              promiseDate, 
              unallocatedBackorderQty, 
              purchaseOrders, 
              inboundShipments, 
              syncZero, 
              doNotSync, 
              onlyDown,
              pendingApprovalQty
            FROM \`hj-reporting.inventory.live_inventory_detail\`
            WHERE upc IS NOT NULL
          `,
        },
      });
      const [itemResults, inventoryResults] = await Promise.all([itemProm, inventoryProm]);

      // Handle new structured response format
      if (!itemResults.success) {
        throw new Error(itemResults.error || 'Failed to fetch item data');
      }
      if (!inventoryResults.success) {
        throw new Error(inventoryResults.error || 'Failed to fetch inventory data');
      }

      const { data: itemData } = itemResults;
      const { data: inventoryData } = inventoryResults;
      const inventoryMap = {};
      inventoryData.forEach(row => {
        inventoryMap[row.upc] = inventoryMap[row.upc] || {};
        inventoryMap[row.upc][row.locationId] = row;
      });
      const finalData = [];
      // pull data from netsuite restlet
      for (let upc in inventoryMap) {
        for (let locationId in inventoryMap[upc]) {
          const inventoryRow = inventoryMap[upc][locationId];
          const inboundShipments = inventoryRow.inboundShipments || [];
          const purchaseOrders = inventoryRow.purchaseOrders || [];
          let inboundDetails = [];
          // Format inbound shipments and purchase orders to match {type, tranId, expectedQty, expectedDate, containerNumber}

          // loop through inboundShipments and add them to inboundDetails and get po line ids
          const poLineIds = new Set();
          if (inboundShipments.length > 0) {
            // Format inbound shipments and purchase orders to match {type, tranId, expectedQty, expectedDate, containerNumber}
            for (const i of inboundShipments) {
              try {
                const s = JSON.parse(i);
                poLineIds.add(s.uniqueKey);
                inboundDetails.push({
                  type: 'Inbound',
                  uniqueKey: s.uniqueKey,
                  internalId: s.inboundShipmentId || '',
                  tranId: s.inboundShipmentNumber || '',
                  expectedQty: s.expectedQty,
                  expectedDate: s.expectedDate ? (new Date(s.expectedDate).getTime() === 0 ? null : new Date(s.expectedDate)) : null,
                  containerNumber: s.containerNumber || ''
                });
              } catch (error) {
                console.error('Error parsing inbound shipment:', error, i);
              }
            }
          }
          // loop through purchaseOrders and add them to inboundDetails if the po line id is not in poLineIds
          if (purchaseOrders.length > 0) {
            for (const i of purchaseOrders) {
              try {
                const po = JSON.parse(i);
                if (!poLineIds.has(po.uniqueKey)) {
                  inboundDetails.push({
                    type: 'Purchase Order',
                    uniqueKey: po.uniqueKey,
                    internalId: po.internalId || '',
                    tranId: po.tranid || '',
                    expectedQty: po.qtyOpen,
                    expectedDate: po.expectedDate ? (new Date(po.expectedDate).getTime() === 0 ? null : new Date(po.expectedDate)) : null,
                    containerNumber: '' // purchase orders don't have containerNumber
                  });
                }
              } catch (error) {
                console.error('Error parsing purchase order:', error, i);
              }
            }
          }
          // Sort inboundDetails by expectedDate, placing empty dates at the end
          let nextExpectedDate = null;
          if (inboundDetails.length > 0) {
            inboundDetails = inboundDetails.sort((a, b) => {
              if (!a.expectedDate && !b.expectedDate) return 0;
              if (!a.expectedDate) return 1;
              if (!b.expectedDate) return -1;
              return a.expectedDate - b.expectedDate;
            });
            nextExpectedDate = inboundDetails[0]?.expectedDate || null;
          }
          const item = itemData.find(item => item.upc === upc);
          if (!item) {
            continue;
          }
          // console.log('inventoryRow', inventoryRow);
          const row = {
            invPulledAt: inventoryRow.invPulledAt,
            imageUrl: item.image ? `https://6810379.app.netsuite.com${item.image}` : null,
            locationId: locationId,
            locationName: inventoryRow.locationName,
            id: item.upc,
            desc: item.producttype,
            lifeStatus: item.lifestatus,
            color: item.color,
            size: item.size,
            productType: item.producttype,
            sku: item.externalid,
            upc: item.upc,
            reserveQty: inventoryRow.reserveQty || 0,
            qtyOnHand: inventoryRow.onHandQty || 0,
            qtyShopify: inventoryRow.nsQty || 0,
            qtyAvail: inventoryRow.availableQty || 0,
            qtyBackordered: (inventoryRow.backorderedQty || 0),
            qtyCommitted: inventoryRow.committedQty || 0,
            qtyPresold: inventoryRow.presoldQty || 0,
            qtyUnallocated: inventoryRow.unallocatedBackorderQty || 0,
            qtyAllocated: inventoryRow.committedQty || 0,
            qtyPendingApproval: inventoryRow.pendingApprovalQty || 0,
            qtyOnOrder: inventoryRow.onOrderQty || 0,
            qtyInTransit: inventoryRow.inTransitQty || 0,
            qtyCommittedToOrderReservation: inventoryRow.committedQty || 0,
            qtyAllocatedToOrderReservation: 0, // TODO
            qtyDemandPlan: 0, // TODO Maybe
            inboundDetails: inboundDetails.length > 0 ? inboundDetails : null,
            nextExpectedDate,
            launchDate: item.launchdate?.value || '',
            endDate: item.enddate?.value || '',
            obsoleteDate: item.obsoletedate?.value || '',
            basePrice: item.baseprice || '',
            prodSpec: item.productspecification,
            productForm: item.productform,
            productCategory: item.productcategory,
            productDivision: item.productdivision,
          };
          finalData.push(row);
        }
      }

      console.log('finalData', finalData.length, finalData.slice(0, 10));
      // TODO remove Phasing Out items that have no incoming or on hand
      setData(finalData);

      if (retryCount > 0) {
        message.success('Inventory data loaded successfully!');
      }

      setLoadingData(false); // Success case
    } catch (error) {
      console.error('Error fetching inventory data:', error);

      // Check if it's a timeout/deadline error and we haven't exceeded max retries
      const isTimeoutError = error.message?.includes('deadline-exceeded') ||
        error.message?.includes('timeout') ||
        error.code === 'deadline-exceeded';

      if (isTimeoutError && retryCount < maxRetries) {
        console.log(`Retrying fetchData (attempt ${retryCount + 1}/${maxRetries}) in ${retryDelay}ms...`);
        message.warning(`Data loading timeout, retrying... (${retryCount + 1}/${maxRetries})`, 4);

        setLoadingData(false); // Reset loading state before retry

        setTimeout(() => {
          fetchData(locs, retryCount + 1);
        }, retryDelay);
        return; // Exit early for retry
      } else {
        const errorMsg = retryCount >= maxRetries
          ? `Failed to load inventory data after ${maxRetries} attempts. The dataset is large - please try again later.`
          : 'Failed to fetch inventory data';
        message.error(errorMsg, 6);
        setLoadingData(false);
      }
    }
  };
  useEffect(() => {
    if (data.length > 0) {
      const lastUpdatedRow = data.find(row => row.invPulledAt);
      setLastUpdated(new Date(lastUpdatedRow?.invPulledAt));
    } else {
      setLastUpdated(null);
    }
  }, [data]);


  const DetailPopup = ({ onClose }) => {
    return (
      <Modal
        style={{ minWidth: 'calc(100vw - 100px)' }}
        maskClosable={false}
        open={showDetailPopup}
        onCancel={() => setShowDetailPopup(false)}
        onClose={() => setShowDetailPopup(false)}
        footer={null}
      >
        <h3>
          {detailSku?.productType} {detailSku?.color} {detailSku?.size}
        </h3>
        <div style={{ height: '500px', width: '100%' }}>
          <AgGridReact
            autoSizeStrategy={{ type: 'fitGridWidth' }}
            columnDefs={[
              { headerName: 'Type', field: 'type' },
              {
                headerName: 'Doc #', field: 'tranId',
                cellRenderer: (params) => {
                  if (params.data.type === 'Inbound') {
                    const internalId = params.data.internalId;
                    if (internalId && /^\d+$/.test(internalId)) {
                      return <a href={`https://6810379.app.netsuite.com/app/accounting/transactions/shipping/inboundshipment/inboundshipment.nl?id=${internalId}`} target="_blank" rel="noopener noreferrer">{params.data.tranId}</a>;
                    }
                  }
                  if (params.data.type === 'Purchase Order') {
                    const internalId = params.data.internalId;
                    if (internalId && /^\d+$/.test(internalId)) {
                      return <a href={`https://6810379.app.netsuite.com/app/accounting/transactions/purchord.nl?id=${internalId}&whence=`} target="_blank" rel="noopener noreferrer">{params.data.tranId}</a>;
                    }
                  }
                  return params.data.tranId;
                }
              },
              {
                headerName: 'Qty',
                field: 'expectedQty',
                valueFormatter: (params) => formatAsLargeInt(params.value),
                cellStyle: (params) => ({ fontWeight: params.data.type === 'Inbound' ? 'bold' : 'normal' })
              },
              {
                headerName: 'Expected Date',
                field: 'expectedDate',
                cellRenderer: (params) => params.value ? params.value.toLocaleDateString() : 'null',
                comparator: (a, b) => {
                  // Handle null values - put them at the bottom
                  if (!a && !b) return 0;
                  if (!a) return 1;  // null goes to bottom
                  if (!b) return -1; // null goes to bottom
                  return a - b; // normal date comparison
                }
              },
              { headerName: 'Container #', field: 'containerNumber' },
            ]}
            rowData={detailSku?.inboundDetails || []}
            rowKey="tranId"
            pagination={false}
          />
        </div>
      </Modal>
    );
  };


  const onClearFilters = () => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.setFilterModel(null);
    }
  };

  useEffect(() => {
    // Only fetch data once when component mounts
    if (!hasFetchedData.current) {
      hasFetchedData.current = true;
      fetchData();
    }

    // Cleanup function to reset the flag if component unmounts
    return () => {
      hasFetchedData.current = false;
    };
  }, []);

  // Set user page
  if (userData) {
    userData.page = 'inventory_report';
  }

  // AG Grid column definitions replacing Ant Design columns
  const columnDefs = [
  {
    headerName: '',
    field: 'imageUrl',
    width: isCondensedView ? 50 : 100,
    suppressHeaderMenuButton: true,
    suppressHeaderFilterButton: true,
    filter: false,
    cellStyle: {
      // padding: '2px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    },
    cellRenderer: params => {
      if (params.value === null || params.value === undefined || params.value === '') {
        return <div />;
      }
      return (
        <img
          loading="lazy"
          src={params.value}
          style={{
            maxHeight: isCondensedView ? '27px' : '48px',
            maxWidth: isCondensedView ? '27px' : '48px',
            objectFit: 'contain',
            display: 'block'
          }}
          alt="Product"
        />
      );
    },
    headerTooltip: 'Image',
    tooltipComponent: params => {
      if (!params.value) return null;
      return (
        <div style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 9999,
          backgroundColor: '#ffffff',
          border: '3px solid #e0e0e0',
          borderRadius: '12px',
          boxShadow: '0 8px 24px rgba(0,0,0,0.15), 0 4px 8px rgba(0,0,0,0.1)',
          padding: '15px',
          backdropFilter: 'blur(2px)'
        }}>
          <img
            src={params.value}
            style={{
              maxWidth: '800px',
              maxHeight: '800px',
              objectFit: 'contain',
              display: 'block'
            }}
            alt="Product Preview"
          />
        </div>
      );
    },
    suppressHeaderMenuButton: true,
    sortable: false,
    filter: false,
    resizable: false,
  },
  {
    headerName: 'Division',
    field: 'productDivision',
    sortable: true,
  },

  {
    headerName: 'Product',
    field: 'productType',
    sortable: true,
  },
  {
    headerName: 'Color',
    field: 'color',
    sortable: true,
    comparator: (a, b) => (a || '').localeCompare(b || ''),
  },
  {
    headerName: 'Size',
    field: 'size',
    sortable: true,
    comparator: (a, b) => (a || '').localeCompare(b || ''),
  },
  {
    headerName: 'SKU',
    field: 'sku',
    sortable: true,
    filter: 'agTextColumnFilter',
    filterParams: {
      filterOptions: [
        'contains',
        'equals',
        'startsWith',
        {
          displayKey: 'multiValue',
          displayName: 'Multiple SKUs',
          predicate: (filterValues, cellValue) => {
            if (!filterValues || filterValues.length === 0) return true;
            if (!cellValue) return false;

            // Split the filter value by common separators
            const terms = filterValues[0].split(/[\s,]+/).map(t => t.trim()).filter(Boolean);
            return terms.some(term =>
              cellValue.toString().toLowerCase().includes(term.toLowerCase())
            );
          },
          numberOfInputs: 1
        }
      ],
      defaultOption: 'multiValue'
    },
    comparator: (a, b) => (a || '').localeCompare(b || ''),
  },
  {
    headerName: 'UPC',
    field: 'upc',
    sortable: true,
    filter: 'agTextColumnFilter',
    filterParams: {
      filterOptions: [
        'contains',
        'equals',
        'startsWith',
        {
          displayKey: 'multiValue',
          displayName: 'Multiple UPCs',
          predicate: (filterValues, cellValue) => {
            if (!filterValues || filterValues.length === 0) return true;
            if (!cellValue) return false;

            // Split the filter value by common separators
            const terms = filterValues[0].split(/[\s,]+/).map(t => t.trim()).filter(Boolean);
            return terms.some(term =>
              cellValue.toString().toLowerCase().includes(term.toLowerCase())
            );
          },
          numberOfInputs: 1
        }
      ],
      defaultOption: 'multiValue'
    },
    comparator: (a, b) => (a || '').localeCompare(b || ''),
  },
  {
    headerName: 'Base Price',
    field: 'basePrice',
    sortable: true,
    valueFormatter: params => formatAsCurrency(params.value),
    comparator: (a, b) => parseFloat(a) - parseFloat(b),
  },
  {
    headerName: 'Life Status',
    field: 'lifeStatus',
    sortable: true,
    comparator: (a, b) => (a || '').localeCompare(b || ''),
    cellRenderer: params => {
      return <Tag color={lifeStatusColors[params.value] || '#000'}>{params.value}</Tag>;
      // return <span style={{ color: lifeStatusColors[params.value] || '#000' }}>{params.value}</span>;
    },
  },
  {
    headerName: 'Reserve Qty',
    field: 'reserveQty',
    sortable: true,
    hide: false,
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
  },
  {
    headerName: 'Specification',
    field: 'prodSpec',
    sortable: true,
    comparator: (a, b) => (a || '').localeCompare(b || ''),
    cellRenderer: params => {
      if (!params.value) return <span style={{ color: '#000' }}>{params.value}</span>;
      const value = typeof params.value === 'string' ? params.value.toLowerCase() : '';
      if (value.includes('seasonal')) {
        return <Tag color={PRODUCT_SPECIFICATION_COLORS['seasonal']}>{params.value}</Tag>;
      }
      if (value.includes('exclusive')) {
        return <Tag color={PRODUCT_SPECIFICATION_COLORS['exclusive']}>{params.value}</Tag>;
      }
      if (value.includes('limited')) {
        return <Tag color={PRODUCT_SPECIFICATION_COLORS['limited']}>{params.value}</Tag>;
      }
      if (value.includes('core')) {
        return <Tag color={PRODUCT_SPECIFICATION_COLORS['core']}>{params.value}</Tag>;
      }
      return <Tag color="#000">{params.value}</Tag>;
    },
  },
  {
    headerName: 'Launch Date',
    field: 'launchDate',
    sortable: true,
    comparator: (a, b) => {
      const dateA = a ? new Date(a) : null;
      const dateB = b ? new Date(b) : null;
      if (dateA && dateB) return dateA - dateB;
      if (dateA) return -1;
      if (dateB) return 1;
      return 0;
    },
  },
  {
    headerName: 'End Date',
    field: 'endDate',
    sortable: true,
  },
  {
    headerName: 'Location',
    field: 'locationName',
    sortable: true,
    filter: 'agSetColumnFilter',
    filterParams: {
      values: [...new Set(data.map(d => d.locationName).filter(Boolean))]
    },
  },
    {
    headerName: 'Shopify',
    field: 'qtyShopify',
    sortable: true,
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
  },

  {
    headerName: 'Incoming',
    field: 'totalIncoming',
    sortable: true,
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
    valueGetter: params => params.data?.inboundDetails?.reduce((acc, detail) => acc + (parseInt(detail.expectedQty) || 0), 0),
  },
  {
    headerName: 'Next Expected Date',
    field: 'nextExpectedDate',
    sortable: true,
    comparator: (a, b) => {
      const dateA = a ? new Date(a) : null;
      const dateB = b ? new Date(b) : null;
      if (dateA && dateB) return dateA - dateB;
      if (dateA) return -1;
      if (dateB) return 1;
      return 0;
    },
    cellRenderer: params => {
      return params.value ? dayjs(params.value).format('YYYY-MM-DD') : '';
    },
    cellStyle: params => {
      if (params.value && params.data.nextExpectedDate < new Date()) {
        return {
          color: '#a8071a',
          fontWeight: 'bold'
        };
      }
      if (
        params.value &&
        dayjs(params.data.nextExpectedDate).isAfter(dayjs(), 'day') &&
        dayjs(params.data.nextExpectedDate).isBefore(dayjs().add(8, 'day'), 'day')
      ) {
        return {
          color: '#d35400', // dark orange
          fontWeight: 'bold'
        };
      }

      return null;
    },
  },
  {
    headerName: 'On Hand',
    field: 'qtyOnHand',
    tooltip: 'Qty On Hand, total units that are on hand',
    sortable: true,
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
  },
  {
    headerName: 'Committed',
    tooltip: 'Qty Committed, total amount where allocated and reserved for that line',
    field: 'qtyCommitted',
    sortable: true,
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
  },
  {
    headerName: 'Available',
    field: 'qtyAvail',
    sortable: true,
    tooltip: 'Qty Available = Qty On Hand - Qty Committed',
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
  },
  {
    headerName: 'Backordered',
    tooltip: 'Qty Backordered, sum of unallocated and allocated but not committed',
    field: 'qtyBackordered',
    sortable: true,
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
  },
  {
    headerName: 'Presold',
    tooltip: 'Qty Presold, total units that are presold and unshipped',
    field: 'qtyPresold',
    sortable: true,
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
  },
  {
    headerName: 'Unallocated',
    field: 'qtyUnallocated',
    tooltip: 'Qty Unallocated, total units that are set to Do Not Allocate or have a strategy that hasnt allocated them yet',
    sortable: true,
    hide: false,
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
  },
  {
    headerName: 'Pending Approval',
    field: 'qtyPendingApproval',
    tooltip: 'Qty Pending Approval, total units that are pending approval',
    sortable: true,
    hide: false,
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
  },
  {
    headerName: 'On Order',
    field: 'qtyOnOrder',
    tooltip: 'Qty On Order, total units that are on open purchase orders',
    sortable: true,
    hide: true,
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
  },
  {
    headerName: 'In Transit',
    field: 'qtyInTransit',
    tooltip: 'Qty In Transit, total units that are in transit',
    sortable: true,
    hide: true,
    comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
    valueFormatter: params => formatAsLargeInt(params.value),
  },
  // {
  //   headerName: 'Committed to Order Reservation',
  //   field: 'qtyCommittedToOrderReservation',
  //   tooltip: 'Qty Committed to Order Reservation, total units that are committed to order reservation',
  //   sortable: true,
  //   hide: true,
  //   comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
  //   valueFormatter: params => formatAsLargeInt(params.value),
  // },
  // {
  //   headerName: 'Qty Allocated to Order Reservation',
  //   field: 'qtyAllocatedToOrderReservation',
  //   tooltip: 'Qty Allocated to Order Reservation, total units that are allocated to order reservation',
  //   hide: true,
  //   sortable: true,
  //   comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
  //   valueFormatter: params => formatAsLargeInt(params.value),
  // },
  // {
  //   headerName: 'Qty Demand Plan',
  //   field: 'qtyDemandPlan',
  //   sortable: true,
  //   comparator: (a, b) => parseFloat(a || 0) - parseFloat(b || 0),
  //   valueFormatter: params => formatAsLargeInt(params.value),
  // },
  {
    headerName: 'Obsolete Date',
    field: 'obsoleteDate',
    sortable: true,
    hide: true,
    comparator: (a, b) => {
      const dateA = a ? new Date(a) : null;
      const dateB = b ? new Date(b) : null;
      if (dateA && dateB) return dateA - dateB;
      if (dateA) return -1;
      if (dateB) return 1;
      return 0;
    },
  },

  {
    headerName: '',
    field: 'action',
    cellRenderer: params => (
      <>
        {/* <Button onClick={() => downloadExcel(data.filter(x => filterList(x)))}>Download</Button> */}
        <Button
          type="primary"
          style={{ marginLeft: 8 }}
          onClick={() => {
            setShowDetailPopup(true);
            setDetailSku(params.data);
          }}
        >
          Details
        </Button>
      </>
    ),
    suppressHeaderMenuButton: true,
    sortable: false,
    filter: false,
    width: 180,
  },
  ];

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      message.info('Refreshing inventory data...', 2);
      await api.updateItemMapOnCall();
      await fetchData();
      message.success('Inventory data refreshed successfully!', 3);
    } catch (error) {
      console.error('Error refreshing inventory data:', error);
      message.error('Failed to refresh inventory data. Please try again.', 4);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (!gridRef.current?.api) return;

    if (omniSearch.upc) {
      // Use the custom multi-value filter on the UPC column
      // Get current filters and only modify the UPC filter to preserve other saved filters
      const currentFilters = gridRef.current.api.getFilterModel() || {};
      const newFilters = {
        ...currentFilters,
        upc: {
          filterType: 'text',
          type: 'multiValue',
          filter: omniSearch.upc
        }
      };
      gridRef.current.api.setFilterModel(newFilters);
    } else {
      // Clear UPC filter when search is empty, but preserve other filters
      const currentFilters = gridRef.current.api.getFilterModel();
      if (currentFilters && currentFilters.upc) {
        const newFilters = { ...currentFilters };
        delete newFilters.upc;
        gridRef.current.api.setFilterModel(newFilters);
      }
    }
  }, [omniSearch]);

return (
  <div style={{ height: 'calc(100vh - 150px)', width: '100%' }}>
    <div style={{ 
      background: '#fff', 
      padding: '20px 24px', 
      borderRadius: '8px',
      marginBottom: '16px',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)'
    }}>
      {/* Header Section */}
      <Row justify="space-between" align="top" style={{ marginBottom: '16px' }}>
        <Col>
          <Title level={3} style={{ margin: 0, color: '#1976d2' }}>
            Inventory Report
          </Title>
          {lastUpdated && (
            <Text
              type={((new Date() - lastUpdated) / 60000) > 30 ? undefined : "secondary"}
              style={{
                fontSize: '13px',
                color: ((new Date() - lastUpdated) / 60000) > 30 ? '#d32f2f' : '#616161',
                fontWeight: ((new Date() - lastUpdated) / 60000) > 30 ? '500' : '400',
                display: 'block',
                marginTop: '4px'
              }}
            >
              Last synced: {lastUpdated.toLocaleString()}
            </Text>
          )}
        </Col>
      </Row>

      {/* Controls Section */}
      <Row justify="space-between" align="middle">
        <Col>
          <Space size="middle">
            <Button
              icon={<ReloadOutlined spin={refreshing} />}
              onClick={handleRefresh}
              type="primary"
              ghost
              loading={refreshing}
              disabled={refreshing}
            >
              {refreshing ? 'Refreshing...' : 'Refresh Data'}
            </Button>
            <Tooltip title="Clear all active filters">
              <Button
                icon={<CloseCircleOutlined />}
                onClick={onClearFilters}
                disabled={!gridRef.current?.api?.getFilterModel()}
              >
                Clear Filters
              </Button>
            </Tooltip>
          </Space>
        </Col>
        <Col>
          {/* Empty column for spacing */}
        </Col>
      </Row>

      {/* Search and View Selection Section */}
      <Row justify="space-between" align="middle" style={{ marginTop: '16px' }}>
        <Col>
          <Input
            placeholder="Search by UPCs (comma-separated)"
            value={omniSearch.upc}
            onChange={(e) => (setOmniSearch({ ...omniSearch, upc: e.target.value }))}
            style={{ width: '280px' }}
            allowClear
            size="middle"
          />
        </Col>
        <Col>
          <Space size="middle">
            <SavedViews
              gridRef={gridRef}
              collectionName="inventoryReportSavedViews"
              userPreferenceKey="inventoryReportView"
              selectWidth="400px"
              saveButtonText="Save View"
            />
          </Space>
        </Col>
      </Row>
    </div>

    <AgGridReact
      ref={gridRef}
      columnDefs={columnDefs}
      loading={loadingData}
      rowData={data}
      suppressPaginationPanel={true}
      pagination={false}
      rowHeight={isCondensedView ? 35 : 50}
      suppressCellFocus={true}
      enableCellTextSelection={true}
      suppressAggFuncInHeader={true}
      onGridReady={(params) => {
        // Grid is ready
      }}
      onFirstDataRendered={(params) => {
        // Grid data is rendered - apply default HQ location filter
        const defaultFilterModel = {
          locationName: {
            filterType: 'set',
            values: ['HQ']
          }
        };
        params.api.setFilterModel(defaultFilterModel);
      }}
      sideBar={{
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel',
          }
        ],
      }}
      defaultColDef={{
        resizable: true,
        sortable: true,
        filter: true,
      }}
    />
    {showDetailPopup && <DetailPopup />}

  </div>
);
}
export default InventoryReport;
