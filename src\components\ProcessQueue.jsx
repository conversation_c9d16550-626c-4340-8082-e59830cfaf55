import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  Badge,
  Button,
  Space,
  Typography,
  Progress,
  Spin,
  Tag,
  Tooltip,
  Drawer,
  Divider,
  Empty,
  Dropdown,
  Menu,
  Collapse,
  Timeline,
  Statistic,
  Input,
  Popconfirm,
  Modal
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  DownloadOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  InfoCircleOutlined,
  ClearOutlined,
  MoreOutlined,
  HistoryOutlined,
  WarningOutlined
} from '@ant-design/icons';

const { Text, Title, Paragraph } = Typography;
const { Panel } = Collapse;

// Unified task status types for order allocation process queue
// This is the SINGLE source of truth for all task status communication
export const TASK_STATUS = {
  PENDING: 'pending',
  QUEUED: 'queued',
  PROCESSING: 'processing',
  WAITING_CONFIRMATION: 'waiting_confirmation',
  COMPLETED: 'completed',
  FAILED: 'failed',
  TIMEOUT: 'timeout',
  CANCELLED: 'cancelled',
  RETRYING: 'retrying'
};

// Legacy status mapping for backward compatibility during transition
export const LEGACY_STATUS_MAP = {
  'api_call': TASK_STATUS.PROCESSING,
  'confirmed': TASK_STATUS.COMPLETED,
  'error': TASK_STATUS.FAILED
};

// Utility function to normalize status values to unified TASK_STATUS
export const normalizeTaskStatus = (status) => {
  if (!status) return TASK_STATUS.PENDING;

  // Return as-is if already a valid TASK_STATUS
  if (Object.values(TASK_STATUS).includes(status)) {
    return status;
  }

  // Map legacy status values
  return LEGACY_STATUS_MAP[status] || status;
};

// Utility function to get the single authoritative status for process queue communication
export const getTaskStatus = (task) => {
  // The 'status' field is the single source of truth for process queue communication
  return normalizeTaskStatus(task?.status);
};

// Task step definitions for detailed progress tracking
export const TASK_STEPS = {
  CREATED: 'created',
  API_CALL_STARTED: 'api_call_started',
  API_CALL_SUCCESS: 'api_call_success',
  API_CALL_FAILED: 'api_call_failed',
  WAITING_NETSUITE_CONFIRMATION: 'waiting_netsuite_confirmation',
  NETSUITE_CONFIRMED: 'netsuite_confirmed',
  CONFIRMATION_TIMEOUT: 'confirmation_timeout',
  COMPLETED: 'completed',
  FAILED: 'failed',
  RETRY_INITIATED: 'retry_initiated'
};

// Error categories for better error handling
export const ERROR_CATEGORIES = {
  NETWORK: 'network',
  NETSUITE_API: 'netsuite_api',
  NETSUITE_BUSINESS: 'netsuite_business',
  TIMEOUT: 'timeout',
  SYSTEM: 'system',
  PERMISSION: 'permission'
};

// Status colors and icons with user-friendly labels
const getStatusConfig = (status) => {
  const configs = {
    [TASK_STATUS.PENDING]: {
      color: '#d9d9d9',
      icon: <ClockCircleOutlined />,
      text: 'Pending',
      badgeStatus: 'default'
    },
    [TASK_STATUS.QUEUED]: {
      color: '#faad14',
      icon: <ClockCircleOutlined />,
      text: 'Queued',
      badgeStatus: 'warning'
    },
    [TASK_STATUS.PROCESSING]: {
      color: '#1890ff',
      icon: <LoadingOutlined spin />,
      text: 'Updating',
      badgeStatus: 'processing'
    },
    [TASK_STATUS.WAITING_CONFIRMATION]: {
      color: '#722ed1',
      icon: <LoadingOutlined spin />,
      text: 'Confirming',
      badgeStatus: 'processing'
    },
    [TASK_STATUS.RETRYING]: {
      color: '#fa8c16',
      icon: <LoadingOutlined spin />,
      text: 'Retrying',
      badgeStatus: 'processing'
    },
    [TASK_STATUS.COMPLETED]: {
      color: '#52c41a',
      icon: <CheckCircleOutlined />,
      text: 'Success',
      badgeStatus: 'success'
    },
    [TASK_STATUS.FAILED]: {
      color: '#ff4d4f',
      icon: <CloseCircleOutlined />,
      text: 'Failed',
      badgeStatus: 'error'
    },
    [TASK_STATUS.TIMEOUT]: {
      color: '#ff7a45',
      icon: <ExclamationCircleOutlined />,
      text: 'Timeout',
      badgeStatus: 'error'
    },
    [TASK_STATUS.CANCELLED]: {
      color: '#d9d9d9',
      icon: <ExclamationCircleOutlined />,
      text: 'Cancelled',
      badgeStatus: 'default'
    }
  };
  return configs[status] || configs[TASK_STATUS.QUEUED];
};

// Enhanced Chrome Downloads Manager-style Task Item Component for Order Allocation
const TaskItem = ({ task, onRetry, onRemove, onViewDetails, onCancel }) => {
  const [expanded, setExpanded] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const statusConfig = getStatusConfig(task.status);

  // Enhanced time calculations
  const getTimeElapsed = () => {
    if (!task.createdAt) return '';
    const elapsed = Date.now() - task.createdAt;
    const hours = Math.floor(elapsed / 3600000);
    const minutes = Math.floor((elapsed % 3600000) / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);

    if (hours > 0) {
      return `${hours}h ${minutes}m ago`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s ago`;
    }
    return `${seconds}s ago`;
  };

  // Get estimated completion time for NetSuite operations
  const getEstimatedCompletion = () => {
    if (task.status !== TASK_STATUS.PROCESSING && task.status !== TASK_STATUS.WAITING_CONFIRMATION) {
      return null;
    }

    const elapsed = Date.now() - (task.startedAt || task.createdAt);
    const isNetSuiteOperation = task.type?.includes('order_allocation');

    if (isNetSuiteOperation) {
      if (task.status === TASK_STATUS.PROCESSING) {
        return 'Usually completes in 1-5 minutes';
      } else if (task.status === TASK_STATUS.WAITING_CONFIRMATION) {
        const remaining = Math.max(0, (task.netsuiteTimeout || 7200000) - elapsed); // 2 hours default
        const remainingMinutes = Math.floor(remaining / 60000);
        if (remainingMinutes > 60) {
          return `Up to ${Math.floor(remainingMinutes / 60)}h ${remainingMinutes % 60}m remaining`;
        }
        return `Up to ${remainingMinutes}m remaining`;
      }
    }

    return null;
  };

  // Get progress color based on status
  const getProgressColor = () => {
    switch (task.status) {
      case TASK_STATUS.COMPLETED:
        return '#52c41a';
      case TASK_STATUS.FAILED:
      case TASK_STATUS.TIMEOUT:
        return '#ff4d4f';
      case TASK_STATUS.WAITING_CONFIRMATION:
        return '#722ed1';
      case TASK_STATUS.RETRYING:
        return '#fa8c16';
      default:
        return '#1890ff';
    }
  };

  // Enhanced task description formatting with PO and customer info
  const formatDescription = () => {
    // For order allocation batch tasks, format with PO information
    if (task.type === 'order_allocation_batch') {
      const docNumber = task.data?.docNumber || 'Unknown SO';
      const customerName = task.data?.customerName || 'Unknown Customer';
      const originalData = task.data?.originalData || [];

      // Get PO number from first change
      const poNumber = originalData[0]?.ponumber;

      // Primary: SO number and line info
      let primary = docNumber;
      if (originalData.length === 1) {
        primary += ` Line ${originalData[0].lineid}`;
      } else if (originalData.length > 1) {
        primary += ` (${originalData.length} lines)`;
      }

      // Secondary: Customer and PO
      let secondary = customerName;
      if (poNumber) {
        secondary += ` • PO: ${poNumber}`;
      }

      return { primary, secondary };
    }

    // Fallback to original logic for other task types
    const parts = task.description.split(':');
    if (parts.length >= 2) {
      return {
        primary: parts[0].trim(), // "SO #12345 Line 2"
        secondary: parts.slice(1).join(':').trim() // "Customer Name"
      };
    }
    return {
      primary: task.description,
      secondary: task.data?.customerName || task.data?.docNumber || ''
    };
  };

  const { primary, secondary } = formatDescription();

  // Get detailed status message with user-friendly language
  const getDetailedStatus = () => {
    const lastStep = task.steps ? Object.values(task.steps).pop() : null;
    const stepMessage = lastStep?.message;

    // Use step message if available, otherwise generate user-friendly message
    if (stepMessage) {
      return stepMessage;
    }

    // Enhanced user-friendly messages based on status and progress
    const statusMessages = {
      [TASK_STATUS.QUEUED]: task.cloudTaskPath
        ? 'Queued in Google Cloud Tasks - waiting to be processed...'
        : 'Preparing to send changes to NetSuite...',
      [TASK_STATUS.PROCESSING]: getProcessingMessage(),
      [TASK_STATUS.WAITING_CONFIRMATION]: getConfirmationMessage(),
      [TASK_STATUS.COMPLETED]: getCompletionMessage(),
      [TASK_STATUS.FAILED]: getFailureMessage(),
      [TASK_STATUS.TIMEOUT]: 'NetSuite is taking longer than expected - your changes may still be processing',
      [TASK_STATUS.CANCELLED]: 'Task was cancelled by user',
      [TASK_STATUS.RETRYING]: `Retrying... (attempt ${task.retryCount || 1})`
    };

    return statusMessages[task.status] || 'Processing your changes...';
  };

  const getProcessingMessage = () => {
    const progress = task.progress || 0;
    if (progress < 30) {
      return 'Connecting to NetSuite...';
    } else if (progress < 70) {
      return 'Sending your changes to NetSuite...';
    } else {
      return 'NetSuite is processing your changes...';
    }
  };

  const getConfirmationMessage = () => {
    const elapsed = Date.now() - (task.startedAt || task.createdAt);
    const minutes = Math.floor(elapsed / 60000);

    if (minutes < 1) {
      return 'NetSuite is applying your changes...';
    } else if (minutes < 5) {
      return `Confirming changes in NetSuite (${minutes}m elapsed)...`;
    } else {
      return 'NetSuite is still processing - complex changes can take several minutes...';
    }
  };

  const getCompletionMessage = () => {
    const changeCount = task.data?.batchSize || task.data?.updateData?.length || 1;
    return `Successfully updated ${changeCount} order line${changeCount > 1 ? 's' : ''}!`;
  };

  const getFailureMessage = () => {
    if (task.canRetry) {
      return `${task.error || 'Update failed'} - Click retry to try again`;
    }
    return task.error || 'Update failed - please check the details and try again';
  };

  // Action menu items
  const getActionMenu = () => (
    <Menu>
      {/* Retry option - available for all task statuses */}
      <Menu.Item key="retry" icon={<ReloadOutlined />} onClick={() => onRetry?.(task)}>
        Retry
      </Menu.Item>

      {/* Cancel option - available for queued tasks or tasks with cloud task paths (once processing starts, cannot be cancelled) */}
      {((task.status === TASK_STATUS.QUEUED || task.status === TASK_STATUS.PENDING || task.status === 'queued' || task.status === 'pending') ||
        (task.cloudTaskPath && task.status !== TASK_STATUS.PROCESSING && task.status !== 'processing')) && (
        <Popconfirm
          title="Cancel task?"
          description={task.cloudTaskPath
            ? "This will cancel the task from Google Cloud Tasks before it starts processing. Once processing begins, it cannot be cancelled."
            : "This will cancel the task before it starts processing. Once a task begins processing, it cannot be cancelled."
          }
          onConfirm={() => onCancel?.(task)}
          okText="Cancel Task"
          cancelText="Keep Task"
          okType="danger"
          placement="left"
        >
          <Menu.Item key="cancel" icon={<PauseCircleOutlined />}>
            {task.cloudTaskPath ? 'Cancel from Queue' : 'Cancel (while queued)'}
          </Menu.Item>
        </Popconfirm>
      )}

      <Menu.Item key="details" icon={<InfoCircleOutlined />} onClick={() => onViewDetails?.(task)}>
        View Details
      </Menu.Item>

      {(task.status === TASK_STATUS.COMPLETED || task.status === TASK_STATUS.FAILED || task.status === TASK_STATUS.CANCELLED) && (
        <Popconfirm
          title="Remove task?"
          description="This will permanently delete this task from the queue."
          onConfirm={() => onRemove?.(task)}
          okText="Remove"
          cancelText="Cancel"
          okType="danger"
          placement="left"
        >
          <Menu.Item key="remove" icon={<DeleteOutlined />}>
            Remove
          </Menu.Item>
        </Popconfirm>
      )}
    </Menu>
  );

  return (
    <div
      style={{
        marginBottom: 8,
        border: `1px solid ${statusConfig.color}20`,
        borderLeft: `4px solid ${statusConfig.color}`,
        borderRadius: '8px',
        backgroundColor: '#fff',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        transition: 'all 0.2s ease',
        cursor: 'pointer'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
      }}
    >
      <div style={{ padding: '12px 16px' }}>
        <div style={{ display: 'flex', alignItems: 'flex-start', gap: 12 }}>
          {/* Status Icon */}
          <div style={{ marginTop: 2, fontSize: '16px' }}>
            {statusConfig.icon}
          </div>

          {/* Main Content */}
          <div style={{ flex: 1, minWidth: 0 }}>
            {/* Primary Description with Order Allocation context */}
            <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 4 }}>
              <Text strong style={{ fontSize: '14px', color: '#1f1f1f' }}>
                {primary}
              </Text>
              <Tag
                color={statusConfig.badgeStatus}
                size="small"
                style={{
                  borderRadius: '12px',
                  fontSize: '10px',
                  fontWeight: 500,
                  padding: '2px 8px'
                }}
              >
                {statusConfig.text}
              </Tag>
            </div>

            {/* Secondary Description */}
            {secondary && (
              <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginBottom: 6 }}>
                {secondary}
              </Text>
            )}

            {/* Progress Bar - Order Allocation Progress */}
            <div style={{ marginBottom: 6 }}>
              <Progress
                percent={task.progress || 0}
                size="small"
                strokeColor={getProgressColor()}
                showInfo={false}
                strokeWidth={4}
                style={{
                  '.ant-progress-bg': {
                    borderRadius: '2px'
                  }
                }}
              />
            </div>

            {/* Status Message and Time */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 }}>
              <Text type="secondary" style={{ fontSize: '11px' }}>
                {getDetailedStatus()}
              </Text>
              <Text type="secondary" style={{ fontSize: '11px' }}>
                {getTimeElapsed()}
              </Text>
            </div>

            {/* Estimated completion time for NetSuite operations */}
            {getEstimatedCompletion() && (
              <Text type="secondary" style={{ fontSize: '10px', display: 'block', fontStyle: 'italic' }}>
                {getEstimatedCompletion()}
              </Text>
            )}

            {/* Retry Count */}
            {task.retryCount > 0 && (
              <Text type="warning" style={{ fontSize: '11px', display: 'block', marginTop: 2 }}>
                Retry attempt {task.retryCount}/{task.maxRetries || 4}
              </Text>
            )}
          </div>

          {/* Actions - Task Management Controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
            {/* Quick action buttons */}
            {(task.status === TASK_STATUS.FAILED || task.status === TASK_STATUS.TIMEOUT) && (
              <Tooltip title="Retry">
                <Button
                  type="text"
                  size="small"
                  icon={<ReloadOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onRetry?.(task);
                  }}
                  style={{ color: '#1890ff' }}
                />
              </Tooltip>
            )}

            {task.status === TASK_STATUS.WAITING_CONFIRMATION && (() => {
              const hasExpectedChanges = !!(task.netsuiteSync?.expectedChanges || task.expectedChanges);
              return (
                <Tooltip title={hasExpectedChanges ? "Check NetSuite Status" : "Repair Task (Missing Data)"}>
                  <Button
                    type="text"
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();

                      if (hasExpectedChanges) {
                        // Normal NetSuite confirmation check
                        if (window.checkNetSuiteConfirmation) {
                          console.log(`🔍 Manual NetSuite check for task ${task.id}`);
                          const expectedChanges = task.netsuiteSync?.expectedChanges || task.expectedChanges;
                          window.checkNetSuiteConfirmation(task.id, expectedChanges);
                        }
                      } else {
                        // Task is missing expected changes - try to repair
                        console.log(`🔧 Attempting to repair task ${task.id} (missing expected changes)`);
                        if (window.triggerRefreshOpenOrders) {
                          window.triggerRefreshOpenOrders(false);
                        }
                        console.warn(`Task ${task.id} is missing expected changes data. Triggered data refresh.`);
                      }
                    }}
                    style={{ color: hasExpectedChanges ? '#1890ff' : '#ff7875' }}
                  />
                </Tooltip>
              );
            })()}

            {task.status === TASK_STATUS.QUEUED && (
              <Tooltip title="Cancel task (only available while queued)">
                <Button
                  type="text"
                  size="small"
                  icon={<PauseCircleOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onCancel?.(task);
                  }}
                  style={{ color: '#ff4d4f' }}
                />
              </Tooltip>
            )}

            <Dropdown overlay={getActionMenu()} trigger={['click']} placement="bottomRight">
              <Button
                type="text"
                size="small"
                icon={<MoreOutlined />}
                onClick={(e) => e.stopPropagation()}
              />
            </Dropdown>
          </div>
        </div>

        {/* Expandable Details */}
        {expanded && (
          <div style={{ marginTop: 12, paddingTop: 12, borderTop: '1px solid #f0f0f0' }}>
            <Timeline size="small">
              {task.steps && Object.entries(task.steps)
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([key, step]) => (
                  <Timeline.Item key={key} color={step.step === 'failed' ? 'red' : 'blue'}>
                    <Text style={{ fontSize: '11px' }}>
                      {step.message}
                      {step.timestamp && (
                        <Text type="secondary"> - {new Date(step.timestamp.seconds * 1000).toLocaleTimeString()}</Text>
                      )}
                    </Text>
                  </Timeline.Item>
                ))}
            </Timeline>
          </div>
        )}
      </div>
    </div>
  );
};

const ProcessQueue = ({
  tasks = [],
  onTaskUpdate,
  onTaskRemove,
  onTaskRetry,
  onTaskCancel,
  onClearCompleted,
  onRetryAllFailed,
  visible = false,
  onClose,
  connectionStatus = 'connected'
}) => {
  const [activeTab, setActiveTab] = useState('all');
  const [selectedTask, setSelectedTask] = useState(null);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(50); // Show 50 tasks per page for optimal performance

  // Enhanced task filtering with search including PO numbers
  const filteredTasks = tasks.filter(task => {
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();

      // Search in basic task info
      if (task.description?.toLowerCase().includes(searchLower) ||
          task.data?.docNumber?.toLowerCase().includes(searchLower) ||
          task.data?.customerName?.toLowerCase().includes(searchLower) ||
        task.id?.toLowerCase().includes(searchLower) ||
        task.message?.toLowerCase().includes(searchLower) ||
        task.error?.toLowerCase().includes(searchLower) ||
        task.errorMessage?.toLowerCase().includes(searchLower)) {
        return true;
      }

      // Search in PO numbers from original data
      if (task.data?.originalData) {
        const hasMatchingPO = task.data.originalData.some(change =>
          change.ponumber?.toLowerCase().includes(searchLower)
        );
        if (hasMatchingPO) return true;
      }

      // Search in individual change PO (for legacy tasks)
      if (task.data?.change?.ponumber?.toLowerCase().includes(searchLower)) {
        return true;
      }

      return false;
    }
    return true;
  });

  // Categorize tasks for Order Allocation process queue display
  const queuedTasks = filteredTasks.filter(t => t.status === TASK_STATUS.QUEUED);
  const processingTasks = filteredTasks.filter(t => t.status === TASK_STATUS.PROCESSING);
  const retryingTasks = filteredTasks.filter(t => t.status === TASK_STATUS.RETRYING);
  const waitingTasks = filteredTasks.filter(t =>
    t.status === TASK_STATUS.WAITING_CONFIRMATION ||
    t.status === 'waiting_confirmation'
  );
  const completedTasks = filteredTasks.filter(t => t.status === TASK_STATUS.COMPLETED);
  const failedTasks = filteredTasks.filter(t =>
    t.status === TASK_STATUS.FAILED ||
    t.status === TASK_STATUS.TIMEOUT ||
    t.status === TASK_STATUS.CANCELLED
  );

  const activeTasks = [...queuedTasks, ...processingTasks, ...retryingTasks, ...waitingTasks];
  const finishedTasks = [...completedTasks, ...failedTasks];

  // Get tasks to display based on active tab
  const getDisplayTasks = () => {
    switch (activeTab) {
      case 'active':
        return activeTasks;
      case 'completed':
        return completedTasks;
      case 'failed':
        return failedTasks;
      case 'all':
      default:
        return filteredTasks;
    }
  };

  const allDisplayTasks = getDisplayTasks();

  // Reset current page when changing tabs or search
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, searchTerm]);

  // Paginate tasks for better performance with large datasets
  const totalPages = Math.ceil(allDisplayTasks.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const displayTasks = allDisplayTasks.slice(startIndex, endIndex);

  // Get overall progress
  const getOverallProgress = () => {
    if (tasks.length === 0) return 0;
    const totalProgress = tasks.reduce((sum, task) => sum + (task.progress || 0), 0);
    return Math.round(totalProgress / tasks.length);
  };

  // Handle task actions
  const handleRetry = (task) => {
    onTaskRetry?.(task);
  };

  const handleRemove = (task) => {
    onTaskRemove?.(task);
  };

  const handleViewDetails = (task) => {
    setSelectedTask(task);
    setDetailsVisible(true);
  };

  const handleRetryAllFailed = () => {
    onRetryAllFailed?.();
  };

  // Calculate progress
  const totalTasks = tasks.length;
  const completedCount = completedTasks.length;
  const failedCount = failedTasks.length;
  const activeCount = activeTasks.length;

  const getTaskIcon = (task) => {
    const config = getStatusConfig(task.status);
    return <span style={{ color: config.color }}>{config.icon}</span>;
  };

  const getTaskDescription = (task) => {
    // For order allocation batch tasks, show SO number and customer
    if (task.type === 'order_allocation_batch') {
      const customerName = task.data?.customerName || 'Unknown Customer';
      const docNumber = task.data?.docNumber || 'Unknown SO';
      return `${docNumber}: ${customerName}`;
    }

    if (task.type === 'order_allocation_save') {
      // Individual task
      return task.description || 'Processing order allocation change';
    }
    if (task.type === 'order_allocation_save_bulk') {
      const changes = task.data?.changes || [];
      if (changes.length === 1) {
        const change = changes[0];
        return `Saving changes for SO #${change.docnumber}`;
      }
      const changeCount = changes.length;
      return `Saving ${changeCount} order allocation change${changeCount !== 1 ? 's' : ''}`;
    }
    return task.description || 'Processing task';
  };

  const getTaskDetails = (task) => {
    // For order allocation batch tasks, show PO and line details
    if (task.type === 'order_allocation_batch' && task.data?.originalData) {
      const originalData = task.data.originalData;
      const details = [];

      // Get PO number from the first change (all changes in a batch should have same PO)
      const firstChange = originalData[0];
      if (firstChange?.ponumber) {
        details.push(`PO: ${firstChange.ponumber}`);
      }

      // Show line count and line IDs
      if (originalData.length === 1) {
        details.push(`Line ${firstChange.lineid}`);
      } else {
        const lineIds = originalData.map(c => c.lineid).join(', ');
        details.push(`${originalData.length} lines: ${lineIds}`);
      }

      // Show what's being changed
      const changeTypes = new Set();
      originalData.forEach(change => {
        if (change.allocationStrategy !== undefined) changeTypes.add('Allocation Strategy');
        if (change.startDate !== undefined) changeTypes.add('Start Date');
        if (change.canceldate !== undefined) changeTypes.add('Cancel Date');
        if (change.prepdate !== undefined) changeTypes.add('Prep Date');
        if (change.isclosed !== undefined) changeTypes.add('Closed Status');
        if (change.newItemId !== undefined) changeTypes.add('Item Change');
      });

      if (changeTypes.size > 0) {
        details.push(`Changes: ${Array.from(changeTypes).join(', ')}`);
      }

      return details.join(' | ');
    }

    if (task.type === 'order_allocation_save' && task.data?.change) {
      // Individual task - single change
      const change = task.data.change;
      const details = [];

      // Show specific item info
      if (change.upc && change.itemdesc) {
        details.push(`Item: ${change.upc} - ${change.itemdesc}`);
      }

      if (change.customername) {
        details.push(`Customer: ${change.customername}`);
      }

      if (change.ponumber) {
        details.push(`PO: ${change.ponumber}`);
      }

      // Show what's being changed
      const changeTypes = [];
      if (change.allocationStrategy !== undefined) {
        changeTypes.push(`Strategy: ${change.oldStrategy} → ${change.newStrategy}`);
      }
      if (change.startDate !== undefined) {
        changeTypes.push(`Start: ${change.oldStartDate} → ${change.newStartDate}`);
      }
      if (change.canceldate !== undefined) {
        changeTypes.push(`Cancel: ${change.oldCancelDate} → ${change.newCancelDate}`);
      }
      if (change.prepdate !== undefined) {
        changeTypes.push(`Prep: ${change.oldPrepDate} → ${change.newPrepDate}`);
      }
      if (change.isclosed !== undefined) {
        changeTypes.push(`Closed: ${change.oldIsClosed} → ${change.newIsClosed}`);
      }
      if (change.newItemId !== undefined) {
        changeTypes.push(`Item: ${change.oldUPC} → ${change.newUPC}`);
      }

      if (changeTypes.length > 0) {
        details.push(`Changes: ${changeTypes.join(', ')}`);
      }

      return details.join(' | ');
    }
    if (task.type === 'order_allocation_save_bulk' && task.data?.changes) {
      const changes = task.data.changes;
      
      if (changes.length === 1) {
        const change = changes[0];
        const details = [];
        
        // Show specific item info
        if (change.upc && change.itemdesc) {
          details.push(`Item: ${change.upc} - ${change.itemdesc}`);
        }
        
        if (change.customername) {
          details.push(`Customer: ${change.customername}`);
        }
        
        if (change.ponumber) {
          details.push(`PO: ${change.ponumber}`);
        }
        
        // Show what's being changed
        const changeTypes = [];
        if (change.allocationStrategy !== undefined) {
          changeTypes.push(`Strategy: ${change.oldStrategy} → ${change.newStrategy}`);
        }
        if (change.startDate !== undefined) {
          changeTypes.push(`Start: ${change.oldStartDate} → ${change.newStartDate}`);
        }
        if (change.canceldate !== undefined) {
          changeTypes.push(`Cancel: ${change.oldCancelDate} → ${change.newCancelDate}`);
        }
        if (change.prepdate !== undefined) {
          changeTypes.push(`Prep: ${change.oldPrepDate} → ${change.newPrepDate}`);
        }
        if (change.isclosed !== undefined) {
          changeTypes.push(`Closed: ${change.oldIsClosed} → ${change.newIsClosed}`);
        }
        if (change.newItemId !== undefined) {
          changeTypes.push(`Item: ${change.oldUPC} → ${change.newUPC}`);
        }
        
        if (changeTypes.length > 0) {
          details.push(`Changes: ${changeTypes.join(', ')}`);
        }
        
        return details.join(' | ');
      } else {
        // Multiple changes - show summary with SO numbers
        const soNumbers = [...new Set(changes.map(c => c.docnumber))].slice(0, 3);
        const soDisplay = soNumbers.length === changes.length ? 
          `SO #${soNumbers.join(', #')}` : 
          `SO #${soNumbers.join(', #')}${soNumbers.length < changes.length ? ` +${changes.length - soNumbers.length} more` : ''}`;
        
        const summaryTypes = {};
        changes.forEach(change => {
          if (change.allocationStrategy !== undefined) {
            summaryTypes.strategy = (summaryTypes.strategy || 0) + 1;
          }
          if (change.startDate !== undefined) {
            summaryTypes.startDate = (summaryTypes.startDate || 0) + 1;
          }
          if (change.canceldate !== undefined) {
            summaryTypes.cancelDate = (summaryTypes.cancelDate || 0) + 1;
          }
          if (change.prepdate !== undefined) {
            summaryTypes.prepDate = (summaryTypes.prepDate || 0) + 1;
          }
          if (change.isclosed !== undefined) {
            summaryTypes.isClosed = (summaryTypes.isClosed || 0) + 1;
          }
          if (change.newItemId !== undefined) {
            summaryTypes.item = (summaryTypes.item || 0) + 1;
          }
        });

        const summaryParts = [];
        if (summaryTypes.strategy) summaryParts.push(`${summaryTypes.strategy} allocation strategy`);
        if (summaryTypes.startDate) summaryParts.push(`${summaryTypes.startDate} start date`);
        if (summaryTypes.cancelDate) summaryParts.push(`${summaryTypes.cancelDate} cancel date`);
        if (summaryTypes.prepDate) summaryParts.push(`${summaryTypes.prepDate} prep date`);
        if (summaryTypes.isClosed) summaryParts.push(`${summaryTypes.isClosed} is closed`);
        if (summaryTypes.item) summaryParts.push(`${summaryTypes.item} item change`);

        return `${soDisplay} | Changes: ${summaryParts.join(', ')}`;
      }
    }
    return '';
  };

  const formatDuration = (startTime, endTime) => {
    if (!startTime) return '';
    const duration = (endTime || Date.now()) - startTime;
    if (duration < 1000) return `${duration}ms`;
    if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`;
    return `${Math.floor(duration / 60000)}m ${Math.floor((duration % 60000) / 1000)}s`;
  };

  const formatErrorMessage = (error) => {
    if (!error) return '';

    // Clean up common error patterns
    let cleanError = error;

    // Remove common prefixes
    cleanError = cleanError.replace(/^(Error:|Failed:|Exception:)/i, '');

    // Truncate very long messages
    if (cleanError.length > 60) {
      cleanError = cleanError.substring(0, 57) + '...';
    }

    return cleanError.trim();
  };

  if (!visible) return null;

  return (
    <div
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        width: '480px',
        maxHeight: '700px',
        zIndex: 1000,
        boxShadow: '0 16px 32px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1)',
        borderRadius: '16px',
        backgroundColor: '#fff',
        border: '1px solid #e8e8e8',
        overflow: 'hidden',
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
      }}
    >
      {/* Order Allocation Process Queue Header */}
      <div style={{
        padding: '16px 20px 12px',
        borderBottom: '1px solid #f0f0f0',
        backgroundColor: '#fafafa'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 12 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
            <Title level={5} style={{ margin: 0, color: '#1f1f1f', fontWeight: 600, fontSize: '16px' }}>
              Order Allocation Tasks
            </Title>
            {connectionStatus !== 'connected' && (
              <Tag color={connectionStatus === 'error' ? 'error' : 'warning'} size="small">
                {connectionStatus}
              </Tag>
            )}
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {tasks.length > 0 && (
              <Popconfirm
                title="Clear all tasks?"
                description={`This will remove all ${tasks.length} task${tasks.length !== 1 ? 's' : ''} from the queue. This action cannot be undone.`}
                onConfirm={onClearCompleted}
                okText="Clear All"
                cancelText="Cancel"
                okType="danger"
              >
                <Button
                  size="small"
                  icon={<ClearOutlined />}
                  style={{
                    fontSize: '11px',
                    height: '28px',
                    borderRadius: '6px',
                    border: '1px solid #d9d9d9',
                    color: '#595959'
                  }}
                >
                  Clear All
                </Button>
              </Popconfirm>
            )}
            <Button
              size="small"
              onClick={onClose}
              style={{
                fontSize: '11px',
                height: '28px',
                borderRadius: '6px',
                border: '1px solid #d9d9d9',
                color: '#595959'
              }}
            >
              ✕
            </Button>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <div style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: 8 }}>
          <Input
            placeholder="Search by SO, customer, PO, ID, or message..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            size="small"
            style={{ flex: 1, borderRadius: '6px' }}
            prefix={<EyeOutlined style={{ color: '#8c8c8c' }} />}
          />
        </div>

        {/* Tab Navigation */}
        <div style={{ display: 'flex', gap: 4 }}>
          {[
            { key: 'all', label: 'All', count: filteredTasks.length },
            { key: 'active', label: 'Active', count: activeTasks.length },
            { key: 'completed', label: 'Completed', count: completedTasks.length },
            { key: 'failed', label: 'Failed', count: failedTasks.length }
          ].map(tab => (
            <Button
              key={tab.key}
              size="small"
              type={activeTab === tab.key ? 'primary' : 'text'}
              onClick={() => setActiveTab(tab.key)}
              style={{
                fontSize: '11px',
                height: '24px',
                borderRadius: '4px',
                padding: '0 8px'
              }}
            >
              {tab.label} {tab.count > 0 && `(${tab.count})`}
            </Button>
          ))}
        </div>
      </div>

      {/* Task List Container */}
      <div style={{
        maxHeight: '500px',
        overflowY: 'auto',
        padding: displayTasks.length === 0 ? '40px 20px' : '8px'
      }}>
        {displayTasks.length === 0 ? (
          <div style={{ textAlign: 'center', color: '#8c8c8c', padding: '40px 20px' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px', opacity: 0.3 }}>
              Received
            </div>
            <Text style={{ fontSize: '14px', color: '#8c8c8c' }}>
              {searchTerm ? 'No tasks match your search' : 'No tasks yet'}
            </Text>
            {searchTerm && (
              <div style={{ marginTop: '8px' }}>
                <Button size="small" onClick={() => setSearchTerm('')}>
                  Clear search
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div style={{ padding: '0 8px' }}>
            {/* Order Allocation Task List */}
            {displayTasks.map(task => (
              <TaskItem
                key={task.id}
                task={task}
                onRetry={onTaskRetry}
                onRemove={onTaskRemove}
                onCancel={onTaskCancel}
                onViewDetails={handleViewDetails}
              />
            ))}

              {/* Pagination Controls */}
              {allDisplayTasks.length > pageSize && (
                <div style={{
                  textAlign: 'center',
                  padding: '16px',
                  borderTop: '1px solid #f0f0f0',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                    Showing {startIndex + 1}-{Math.min(endIndex, allDisplayTasks.length)} of {allDisplayTasks.length} tasks
                  </Text>
                  <div style={{ display: 'flex', gap: 8 }}>
                    <Button
                      size="small"
                      disabled={currentPage === 1}
                      onClick={() => setCurrentPage(currentPage - 1)}
                    >
                      Previous
                    </Button>
                    <Text style={{ fontSize: '12px', padding: '4px 8px' }}>
                      Page {currentPage} of {totalPages}
                    </Text>
                    <Button
                      size="small"
                      disabled={currentPage === totalPages}
                      onClick={() => setCurrentPage(currentPage + 1)}
                    >
                      Next
                    </Button>
                  </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Task Details Modal */}
      <Modal
        title={`Task Details - ${selectedTask?.description || 'Unknown Task'}`}
        open={detailsVisible}
        onCancel={() => {
          setDetailsVisible(false);
          setSelectedTask(null);
        }}
        footer={[
          <Button key="close" onClick={() => {
            setDetailsVisible(false);
            setSelectedTask(null);
          }}>
            Close
          </Button>
        ]}
        width={800}
        style={{ top: 20 }}
      >
        {selectedTask && (
          <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
            {/* Basic Task Info */}
            <div style={{ marginBottom: '24px' }}>
              <h4>Basic Information</h4>
              <div style={{ display: 'grid', gridTemplateColumns: '150px 1fr', gap: '8px', fontSize: '14px' }}>
                <strong>Task ID:</strong> <span>{selectedTask.id}</span>
                <strong>Type:</strong> <span>{selectedTask.type}</span>
                <strong>Status:</strong> <span style={{ color: getStatusConfig(selectedTask.status).color }}>
                  {getStatusConfig(selectedTask.status).text}
                </span>
                <strong>Progress:</strong> <span>{selectedTask.progress || 0}%</span>
                <strong>Created:</strong> <span>{selectedTask.createdAt ? new Date(selectedTask.createdAt).toLocaleString() : 'Unknown'}</span>
                {selectedTask.completedAt && (
                  <>
                    <strong>Completed:</strong> <span>{new Date(selectedTask.completedAt).toLocaleString()}</span>
                  </>
                )}
                {selectedTask.retryCount > 0 && (
                  <>
                    <strong>Retry Count:</strong> <span>{selectedTask.retryCount}/{selectedTask.maxRetries || 4}</span>
                  </>
                )}
              </div>
            </div>

            {/* Task Data */}
            {selectedTask.data && (
              <div style={{ marginBottom: '24px' }}>
                <h4>Task Data</h4>
                <div style={{ background: '#f5f5f5', padding: '12px', borderRadius: '6px', fontSize: '12px' }}>
                  <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                    {JSON.stringify(selectedTask.data, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {/* Error Information */}
            {selectedTask.error && (
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ color: '#ff4d4f' }}>Error Details</h4>
                <div style={{ background: '#fff2f0', border: '1px solid #ffccc7', padding: '12px', borderRadius: '6px' }}>
                  <div style={{ fontSize: '14px', color: '#cf1322' }}>
                    {selectedTask.error}
                  </div>
                  {selectedTask.errorCategory && (
                    <div style={{ fontSize: '12px', color: '#8c8c8c', marginTop: '8px' }}>
                      Category: {selectedTask.errorCategory}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Task Steps */}
            {selectedTask.steps && Object.keys(selectedTask.steps).length > 0 && (
              <div style={{ marginBottom: '24px' }}>
                <h4>Task Steps</h4>
                <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                  {Object.entries(selectedTask.steps)
                    .sort(([a], [b]) => b.localeCompare(a)) // Sort by step key (newest first)
                    .map(([stepKey, step]) => (
                      <div key={stepKey} style={{
                        padding: '8px',
                        borderBottom: '1px solid #f0f0f0',
                        fontSize: '12px'
                      }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <strong>{step.step || stepKey}</strong>
                          <span style={{ color: '#8c8c8c' }}>
                            {step.timestamp ? new Date(step.timestamp.seconds * 1000).toLocaleTimeString() : ''}
                          </span>
                        </div>
                        {step.message && (
                          <div style={{ marginTop: '4px', color: '#595959' }}>
                            {step.message}
                          </div>
                        )}
                        {step.progress !== undefined && (
                          <div style={{ marginTop: '4px' }}>
                            Progress: {step.progress}%
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* NetSuite Result */}
            {selectedTask.netsuiteResult && (
              <div style={{ marginBottom: '24px' }}>
                <h4>NetSuite Response</h4>
                <div style={{ background: '#f6ffed', border: '1px solid #b7eb8f', padding: '12px', borderRadius: '6px', fontSize: '12px' }}>
                  <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                    {JSON.stringify(selectedTask.netsuiteResult, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ProcessQueue;
