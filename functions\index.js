/* eslint-disable camelcase */
/* eslint-disable no-case-declarations */
/* eslint-disable guard-for-in */
/* eslint-disable max-len */
/**
 * Import function triggers from their respective submodules:
 *
 * const {onCall} = require("firebase-functions/v2/https");
 * const {onDocumentWritten} = require("firebase-functions/v2/firestore");
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

const Papa = require("papaparse");

const { onRequest, onCall } = require("firebase-functions/v2/https");
const { onSchedule } = require("firebase-functions/v2/scheduler");
const { onDocumentWritten } = require("firebase-functions/v2/firestore");
const admin = require("firebase-admin");
const functions = require("firebase-functions");
const { netsuiteSuiteQlQuery, getNsInventory } = require("./helpers/netsuite");
const { SYSTEMS } = require("./helpers/systems");
// const dayjs = require("dayjs");

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

// const logger = require("firebase-functions/logger");
const { createDoc, createDocs, modifyDocs, modifyDoc, deleteDoc, queryDocs, getDocData, deleteDocs } = require("./helpers/firestore");
const { amazonGetOrders, amazonGetLiveInventory } = require("./helpers/amazon");
const { tiktokGetOrders } = require("./helpers/tiktok");
const { queryNetSuite, updateNetSuiteRecord, createNetSuiteRecord, getNetSuiteSavedSearch, refreshOpenOrders, updateSalesOrderTask } = require("./helpers/netsuite");
const {
  bigQuerySanitizeFieldName,
  upsertRows,
  runQuery,
  addColumn, editColumn, deleteColumn, updateBigQueryReports, bigQueryGetDatasetsAndTables, bigQueryRunQueries, bigQueryAppendRows, bigQueryReplaceTable,
  bigQueryCreateTable,
  bigQueryAppendRowsWithLoadJob,
  bigQueryDeleteRows,
  bigQueryUploadCsvFiles,
  bigQueryLoadToTable,
  upsert,
  bigQueryGetItems,
} = require("./helpers/bigQuery");
const { default: axios } = require("axios");

const kpiSettings = require("./kpi/kpiSettings");
const financialKpi = require("./kpi/financialKpi");
const inventoryKpi = require("./kpi/inventoryKpi");
const orderMgmtKpi = require("./kpi/orderMgmtKpi");
const productDevKpi = require("./kpi/productDevKpi");
const supplierMgmtKpi = require("./kpi/supplierMgmtKpi");
const gorgiasTagSettings = require("./kpi/gorgiasTagSettings");
const { getShopifyOrders, getShopifyDiscounts } = require("./helpers/shopify");
const { createTask, deleteTask, storageGetCsvFiles, saveToCloudStorage, sendGChatMessage, getFromCloudStorage } = require("./helpers/google");
const { Storage } = require("@google-cloud/storage");

// Demand Plan and Item Node Matrix helpers
const {
  migrateBigQueryToFirestore,
} = require("./helpers/firestore");

const {
  syncUpcList,
  updateUpcForecastNodes,
  bulkUpdateUpcForecastNodes,
  getAllUpcNodes,
  getForecastNodesList,
} = require("./helpers/itemNodeMatrix");
const storage = new Storage();


// Create and deploy your first functions
// https://firebase.google.com/docs/functions/get-started
const getText = (val) => {
  if (typeof val === "string") {
    return val;
  } else if (typeof val === "object") {
    if (val.length === 0) return "";
    return val[0] ? val[0].text : "";
  } else {
    return "";
  }
};
const formatAsCurrency = (val) => {
  return `$${parseFloat(val).toFixed(2)}`;
};
const formatAsLargeInt = (val) => {
  return parseInt(val) ? parseInt(val).toLocaleString() : val;
};
const toSnakeCase = (str) => {
  return str
    .replace(/[^a-zA-Z0-9 ]/g, "") // Remove special characters
    .trim()
    .toLowerCase()
    .replace(/\s+/g, "_"); // Replace spaces with underscores
};
const getAdjustedDate = () => {
  const now = new Date();
  const hour = now.getHours(); // 0 = 12AM, 1 = 1AM, ..., 23 = 11PM

  // If the time is NOT between 12:00 AM and 1:00 AM → return 15 days ago
  if (hour !== 0) {
    return null;
  }

  // If time is between 12:00 AM and 1:00 AM, apply conditional logic
  const dayOfMonth = now.getDate();
  const dayOfWeek = now.getDay(); // 1 = Monday

  const past = new Date(now);

  if (dayOfMonth === 1 || dayOfMonth === 15) {
    past.setDate(now.getDate() - (365 * 2));
  } else if (dayOfWeek === 1) {
    past.setDate(now.getDate() - 365);
  } else {
    past.setDate(now.getDate() - 30);
  }

  return past;
};

exports.initializeKPIGoals = kpiSettings.initializeKPIGoals;
exports.getKPIGoals = kpiSettings.getKPIGoals;
exports.updateKPIGoals = kpiSettings.updateKPIGoals;
exports.resetKPIGoals = kpiSettings.resetKPIGoals;
exports.getKPIGoalsForReport = kpiSettings.getKPIGoalsForReport;

const updateOrderTable = async (daysBack) => {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - daysBack);
  // const endDate = new Date();
  const salesQuery = `
    SELECT id, otherrefnum, tranid,  trandate, saleschannel, cseg_division, foreignTotal
    FROM transaction
    WHERE type IN ('SalesOrd','CashSale') AND trandate > DATE '${startDate.toISOString().split("T")[0]}'
  `;
  const orderData = await queryNetSuite(salesQuery, false);
  await upsertRows("sales", "orders", "id", orderData);
};
exports.updateOrderTableOnCall = onCall(async (data, context) => {
  const daysBack = data.data.daysBack || 1;
  await updateOrderTable(daysBack);
  return { success: true };
});
// exports.updateOrderTableOnSchedule = onSchedule("every 2 hours").timeZone("America/New_York").onRun(async (context) => {
//   updateOrderTable(1);
//   return null;
// });

exports.getCogsPercentage = financialKpi.getCogsPercentage;
exports.getDIOData = financialKpi.getDIOData;
exports.getInboundTransportationCost = financialKpi.getInboundTransportationCost;
exports.getOutboundTransportationCost = financialKpi.getOutboundTransportationCost;

exports.getInventoryTurnoverRatio = inventoryKpi.getInventoryTurnoverRatio;
exports.getBackorderRate = inventoryKpi.getBackorderRate;

exports.getOrderFulfillmentCycleTime = orderMgmtKpi.getOrderFulfillmentCycleTime;
exports.getReturnRateV2 = orderMgmtKpi.getReturnRateV2;
exports.getUnfulfilledOrderRate = orderMgmtKpi.getUnfulfilledOrderRate;
exports.getOrderAccuracyRate = orderMgmtKpi.getOrderAccuracyRate;
exports.getPerfectOrderRate = orderMgmtKpi.getPerfectOrderRate;

exports.getOnTimeDeliveryV2 = supplierMgmtKpi.getOnTimeDeliveryV2;
exports.getProcurementCycleTime = supplierMgmtKpi.getProcurementCycleTime;
exports.getSupplierLeadTime = supplierMgmtKpi.getSupplierLeadTime;
exports.getSupplierQualityScore = supplierMgmtKpi.getSupplierQualityScore;
exports.getSupplierDefectRate = supplierMgmtKpi.getSupplierDefectRate;

exports.getProductLifecycleScore = productDevKpi.getProductLifecycleScore;
exports.getProductPerformanceMetrics = productDevKpi.getProductPerformanceMetrics;
exports.getTimelineHitScore = productDevKpi.getTimelineHitScore;

exports.getGorgiasTagMappings = gorgiasTagSettings.getGorgiasTagMappings;
exports.updateGorgiasTagMappings = gorgiasTagSettings.updateGorgiasTagMappings;
exports.resetGorgiasTagMappings = gorgiasTagSettings.resetGorgiasTagMappings;


exports.updateSafetyStock = onRequest((request, response) => {
  const { items } = request.body;
  if (!items) {
    response.status(400).send("Items not provided");
    return;
  }
  const itemList = [];
  for (const item of items) {
    const { itemId, safetyStock, locationId } = item;

    if (!itemId || !safetyStock) {
      response.status(400).send("Item ID or safety stock not provided");
      return;
    }
    itemList.push({ itemId, safetyStock, locationId });
  }
  createDocs("safetyStock", itemList);
  response.send("Success");
});
exports.makeSuiteQlQuery = onCall(
  {
    timeoutseconds: 540,
    memory: "2GiB",
  },
  async (data, context) => {
    const { query, paginate } = data.data;
    // console.log("query", query);
    try {
      const results = await queryNetSuite({ q: query, paginate });
      return results;
    } catch (error) {
      console.error("Error:", error.message.data);
      return { error: error.message.data };
    }
  },
);
const getNsItems = async (skus = []) => {
  // eslint-disable-next-line max-len
  let inventoryQuery = `SELECT 
    nvl(p.price,0) basePrice, 
    pt.name productType, 
    b.name brand, 
    i.custitem2 prodSpec, 
    i.custitem20 lifeStatus, 
    i.upccode upc, 
    i.itemId sku, 
    i.custitem24 launchDate,
    i.custitem25 endDate,
    nvl(i.custitem_reserve_inv_qty, 0) qtyreserved, 
    i.displayname description, 
    i.id netsuiteId 
    FROM 
      item i 
      LEFT JOIN itemprice p on p.item = i.id 
      LEFT JOIN customrecord_product_type pt ON pt.id = i.custitem_product_type 
      LEFT JOIN CUSTOMLIST_BRANDS b on b.id = i.custitem_brand 
    WHERE 
      i.upccode IS NOT NULL 
      AND p.pricelevel = 1 
      AND p.currencypage = 1
  `;
  if (skus.length > 0) {
    inventoryQuery += ` AND i.itemId IN (${skus.map((x) => `'${x}'`).join(",")})`;
  }
  // eslint-disable-next-line max-len
  // const nonInventoryQuery = `SELECT i.custitem20, i.upccode, i.itemId, i.displayname, i.id netsuiteId, FROM item i WHERE itemtype='NonInvtPart'`;
  // const nonInventoryProm = queryNetSuite(nonInventoryQuery);
  const inventoryProm = queryNetSuite(inventoryQuery);
  const [
    // nonInventoryResults,
    inventoryResults] = await Promise.all([
      // nonInventoryProm,
      inventoryProm]);
  const itemResults = [
    // nonInventoryResults,
    inventoryResults].flat();
  // const invPulledAt = new Date().toISOString();
  const skuList = [];
  const lifeStatuses = new Set();
  const productTypes = new Set();
  const productSpecifications = new Set();
  // const locations = new Set();
  const brands = new Set();
  if (!itemResults || !itemResults.length) return skuList;
  for (const item of itemResults) {
    lifeStatuses.add(item.lifestatus);
    productTypes.add(item.producttype);
    productSpecifications.add(item.prodSpec);
    // locations.add(item.locationid);
    brands.add(item.brand);
    // const location = item.location;
    let skuListItem = skuList.find((x) => x.upc === item.upc);
    // if (!skuListItem) {
    skuListItem = {
      sku: item.sku,
      upc: item.upc.toString(),
      netsuiteId: item.netsuiteid.toString(),
      brand: item.brand,
      productType: item.producttype,
      description: item.description,
      lifeStatus: item.lifestatus,
      productSpecification: item.prodspec,
      basePrice: parseFloat(item.baseprice ? item.baseprice.replace("$", "") : 0),
      launchDate: item.launchdate ? new Date(item.launchdate) : null,
      endDate: item.enddate ? new Date(item.enddate) : null,
      // invPulledAt,
      // inventory: {},
    };
    // }
    // skuListItem.inventory[item.locationid] = {
    //   onHandQty: item.qtyonhand ? parseFloat(item.qtyonhand) : 0,
    //   availableQty: item.qtyavailable ? parseFloat(item.qtyavailable) : 0,
    //   backorderedQty: item.qtybackordered ? parseFloat(item.qtybackordered) : 0,
    //   reserveQty: item.qtyreserved ? parseFloat(item.qtyreserved) : 0,
    // }
    skuList.push(skuListItem);
  }
  // TODO FIX so it doesn't NS doesn't duplicate
  const uniqueSkuList = [];
  const seenUpcs = new Set();
  for (let i = skuList.length - 1; i >= 0; i--) {
    const item = skuList[i];
    if (!seenUpcs.has(item.upc)) {
      uniqueSkuList.push(item);
      seenUpcs.add(item.upc);
    }
  }

  await upsertRows("items", "items", "upc", uniqueSkuList);// TODO remove slice
  // const basePrices = [...new Set(skuList.map((x) => x.basePrice))];
  // const proms = [];
  // for (const basePrice of basePrices) {
  //   console.log("basePrice", basePrice);
  //   const itemBaseList = skuList.filter((x) => x.basePrice === basePrice);
  // }
  // await Promise.all(proms);
  // await modifyDocs("items", skuList.map((x) => ({ id: x.id, data: x })));
  await modifyDocs("lists", [
    { id: "lifeStatuses", data: { items: Array.from(lifeStatuses).filter((x) => x) } },
    { id: "productTypes", data: { items: Array.from(productTypes).filter((x) => x && x !== "- None -") } },
    { id: "brands", data: { items: Array.from(brands).filter((x) => x) } },
    { id: "productSpecifications", data: { items: Array.from(productSpecifications).filter((x) => x) } },
  ]);
  return true;
};
// const getNsHistoricalSales = async () => {
//   const salesQuery = `SELECT
//     trandate as soDate,

//   `;
// };
exports.getNsInventoryOnCall = onCall(async (data, context) => {
  await getNsItems();
  return true;
});

exports.updateItemMapOnSchedule = onSchedule(
  {
    schedule: "every 30 minutes",
    timeoutSeconds: 540,
    memory: "1GiB",
    maxInstances: 1,
    maxConcurrentRequests: 1,
  },
  async (context) => {
    console.log("Starting Update Item Map Scheduled Job");
    return await updateItemMap();
  },
);

exports.updateItemMapOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async (request) => {
  console.log("Starting Update Item Map On Call", request);
  return await updateItemMap();
});

/**
 * Updates the item map by merging NetSuite inventory data with existing BigQuery data,
 * normalizing and merging inbound shipments and purchase orders, and writing the result
 * back to BigQuery. Used for keeping live inventory details up to date.
 */
const updateItemMap = async () => {
  let upcMap = {};
  try {
    // Fetch latest inventory data from NetSuite
    upcMap = await getNsInventory();
    if (!upcMap || !Object.keys(upcMap).length) {
      throw new Error("No NS inventory found");
    }
  } catch (error) {
    // Log and exit if NetSuite data fetch fails
    console.error("Error getting NS items", error.message);
    return false;
  }

  const existingBigQueryData = {};
  try {
    // Fetch current inventory details from BigQuery for merging
    const query = `
        SELECT upc, inboundShipments, purchaseOrders, promiseDate, presaleQty
        FROM \`hj-reporting.inventory.live_inventory_detail\`
      `;
    const existingData = await runQuery({ query });
    existingData.forEach((row) => {
      existingBigQueryData[row.upc] = {
        inboundShipments: row.inboundShipments || [],
        purchaseOrders: row.purchaseOrders || [],
        promiseDate: row.promiseDate?.value || null,
        presaleQty: row.presaleQty || 0,
      };
    });
  } catch (error) {
    // Log and exit if BigQuery fetch fails
    console.error("Could not fetch existing BigQuery data:", error.message);
    return false;
  }

  // Helper: Normalize a date to UTC YYYY-MM-DD string
  const normalizeDate = (input) => {
    if (!input) return null;
    if (input instanceof Date) {
      const y = input.getUTCFullYear();
      const m = String(input.getUTCMonth() + 1).padStart(2, "0");
      const d = String(input.getUTCDate()).padStart(2, "0");
      return `${y}-${m}-${d}`;
    }
    if (typeof input === "string") {
      const m = input.match(/^(\d{4}-\d{2}-\d{2})/);
      if (m) return m[1];
      const dt = new Date(input);
      if (isNaN(dt)) return null;
      const y = dt.getUTCFullYear();
      const mo = String(dt.getUTCMonth() + 1).padStart(2, "0");
      const da = String(dt.getUTCDate()).padStart(2, "0");
      return `${y}-${mo}-${da}`;
    }
    return null;
  };

  /**
   * Calculates the promise date for a UPC based on inbound shipments and backordered quantity.
   * @param {Array} shipments - Array of shipment objects with presaleQty and promiseDate
   * @param {number} backorderedQty - The quantity that needs to be fulfilled
   * @return {string|null} The earliest date that can fulfill the backorder, or null if not possible
   */
  const calculatePromiseDate = (shipments, backorderedQty) => {
    if (!shipments || !shipments.length) return null;
    const totalPresale = shipments.reduce((sum, s) => sum + (s.presaleQty || 0), 0);
    if (totalPresale === 0) return null;
    // Only consider shipments with presaleQty > 0 and a valid promiseDate
    const sorted = shipments
      .filter((s) => (s.presaleQty || 0) > 0 && s.promiseDate)
      .map((s) => ({ ...s, _pd: normalizeDate(s.promiseDate) }))
      .filter((s) => s._pd)
      .sort((a, b) => a._pd.localeCompare(b._pd));
    if (!sorted.length) return null;
    let remaining = backorderedQty || 0;
    for (const s of sorted) {
      if (remaining <= s.presaleQty) return s._pd;
      remaining -= s.presaleQty;
    }
    // If not enough presaleQty, return the latest promise date
    return sorted[sorted.length - 1]._pd;
  };

  // Merge NetSuite and BigQuery data for each UPC
  for (const upc in upcMap) {
    // Get existing and new inbound shipments and purchase orders
    const existingShipments = existingBigQueryData[upc]?.inboundShipments || [];
    const newShipments = upcMap[upc].inboundShipments || [];
    const existingPurchaseOrders = existingBigQueryData[upc]?.purchaseOrders || [];
    const newPurchaseOrders = upcMap[upc].purchaseOrders || [];

    // Build maps for quick lookup by ID
    const existingShipmentMap = {};
    existingShipments.forEach((shipment) => {
      const parsedShipment = JSON.parse(shipment);
      existingShipmentMap[parsedShipment.inboundShipmentId] = parsedShipment;
    });
    const existingPurchaseOrderMap = {};
    existingPurchaseOrders.forEach((purchaseOrder) => {
      const parsedPurchaseOrder = JSON.parse(purchaseOrder);
      existingPurchaseOrderMap[parsedPurchaseOrder.purchaseOrderId] = parsedPurchaseOrder;
    });

    // Merge inbound shipments: update existing, add new, skip removed
    const mergedShipments = [];
    const mergedPurchaseOrders = [];
    let presaleQty = 0;

    newShipments.forEach((newShipment) => {
      const existing = existingShipmentMap[newShipment.inboundShipmentId];
      if (existing) {
        // Update fields from new shipment, keep other fields from existing
        mergedShipments.push({
          ...existing,
          expectedQty: newShipment.expectedQty,
          receivedQty: newShipment.receivedQty,
          qtyOpen: newShipment.qtyOpen,
          status: newShipment.status,
          containerNumber: newShipment.containerNumber,
          expectedDate: newShipment.expectedDate,
        });
        presaleQty += existing.presaleQty;
      } else {
        // Add new shipment as is
        mergedShipments.push(newShipment);
      }
      // Note: Shipments not present in NetSuite are not re-added (assumed closed)
    });

    // Merge purchase orders: update existing, add new
    newPurchaseOrders.forEach((newPurchaseOrder) => {
      const existing = existingPurchaseOrderMap[newPurchaseOrder.purchaseOrderId];
      if (existing) {
        mergedPurchaseOrders.push({ ...existing, ...newPurchaseOrder });
      } else {
        mergedPurchaseOrders.push(newPurchaseOrder);
      }
    });

    // Update upcMap with merged data and calculated fields
    upcMap[upc].inboundShipments = mergedShipments;
    upcMap[upc].purchaseOrders = mergedPurchaseOrders;
    upcMap[upc].promiseDate = calculatePromiseDate(mergedShipments, upcMap[upc].backorderedQty);
    upcMap[upc].presaleQty = presaleQty > 0 ? presaleQty - upcMap[upc].backorderedQty - upcMap[upc].reserveQty : 0;
  }

  // Prepare list of UPC records for BigQuery
  const upcList = [];
  for (const upc in upcMap) {
    upcList.push({ id: upc, ...upcMap[upc], modifiedAt: new Date() });
  }

  // Write merged data to BigQuery, replacing the table
  await bigQueryLoadToTable({
    datasetId: "inventory",
    tableId: "live_inventory_detail",
    rows: upcList,
    replace: true, // Replace entire table for full pull
  });

  console.log("Finished updating item map");
  return true;
};

exports.updateTableOnRequest = onRequest(async (req, res) => {
  const { table, data, key } = req.body;
  if (!table || !data) {
    res.status(400).send("Table or data not provided");
    return;
  }
  await upsertRows(table, table, key, data);
});
exports.generateForecastDataOnCall = onCall(async (data, context) => {
  const { selectedMethod, selectedMonth, options } = data.data;
  if (!selectedMethod || !selectedMonth || !options) {
    throw new Error("Missing data" + JSON.stringify(data.data));
  }
  const { growthNumber, revenueGoal, hitRevenueGoal } = options;
  const parsedGrowthNumber = parseFloat(growthNumber);
  const parsedRevenueGoal = parseFloat(revenueGoal);
  const { startDate, endDate } = selectedMethod;
  const salesQuery = `SELECT 
  i.upc as upc, 
  i.basePrice as basePrice,
  i.lifeStatus as lifeStatus,
  SUM(s.quantity) as quantity, 
  CASE
    WHEN s.salesChannel = 'Wholesale' THEN 'Wholesale'
    WHEN s.salesChannel = 'Shopify' THEN 'Wholesale'
    WHEN s.salesChannel = 'Shopify US' THEN 'Shopify'
    WHEN s.salesChannel = 'HydroJug US' THEN 'Shopify'
    WHEN s.salesChannel = 'Wholesale Inbound' THEN 'Wholesale'
    WHEN s.salesChannel = 'Wholesale Outbound' THEN 'Wholesale'
    WHEN s.salesChannel = 'TikTok Shops' THEN 'TikTok'
    WHEN s.salesChannel = 'Amazon' THEN 'Amazon'
    WHEN s.salesChannel = 'Amazon FBA' THEN 'Amazon'
    WHEN s.salesChannel = '- None -' THEN 'Wholesale'
    WHEN s.salesChannel = 'Shopify CA' THEN 'Shopify'
    WHEN s.salesChannel = 'Shopify UK' THEN 'Shopify'
    WHEN s.salesChannel = 'Shopify AU' THEN 'Shopify'
    WHEN s.salesChannel IS NULL THEN 'Wholesale'
    ELSE s.salesChannel
  END as salesChannel,
  FROM \`hj-reporting.sales.dailySalesByChannel\` as s 
  LEFT JOIN \`hj-reporting.items.variants\` AS i ON i.upc=s.upc
  WHERE i.upc IS NOT NULL AND soDate >= '${startDate}' AND soDate <= '${endDate}'
  GROUP BY salesChannel, upc, sku, lifeStatus, basePrice
  `;
  const salesData = await runQuery({ query: salesQuery });
  // check for data errors
  if (!salesData || !salesData.length) return false;

  // generate a query to create forecast data
  const forecastData = [];
  const dtStr = `${selectedMonth.$y}-${String(selectedMonth.$M + 1).padStart(2, "0")}`;
  const totalRev = salesData.reduce((acc, { basePrice, quantity, salesChannel }) => {
    const avgPrice = salesChannel === "Wholesale" ? Math.round(basePrice / 2, 2) : basePrice;
    return acc + avgPrice * quantity * (parsedGrowthNumber || 1);
  }, 0);


  for (const { salesChannel, quantity, upc, basePrice } of salesData) {
    // if (lifeStatus === "Phasing Out") continue;
    // if (lifeStatus === "Launching") continue;
    // if (upc.slice(0, 1) !== "8") continue;
    if (quantity == 0) continue;
    const daysToAverage = (new Date(endDate) - new Date(startDate)) / 1000 / 60 / 60 / 24;
    if (!salesChannel) {
      console.error("No channel mapping for", salesChannel);
      throw new Error("No channel mapping for", salesChannel);
    }
    let monthlyBurn = Math.round((quantity / (daysToAverage / 30)), 0) * (parsedGrowthNumber || 1);
    const avgPrice = salesChannel === "Wholesale" ? Math.round(basePrice / 2, 2) : basePrice;
    const monthlySales = avgPrice * monthlyBurn;
    const monthlyPercentRev = monthlySales / totalRev;
    if (parsedRevenueGoal && hitRevenueGoal) {
      monthlyBurn = Math.round(parsedRevenueGoal * monthlyPercentRev / avgPrice, 0);
    }
    if (!dtStr || !upc || !salesChannel || !monthlyBurn || monthlyBurn < 50) {
      console.error("Missing data", { dtStr, upc, salesChannel, monthlyBurn });
      continue;
      // throw new Error("Missing data", {dtStr, upc, salesChannel, monthlyBurn});
    }
    const forecastObj = {
      uniqueKey: `${dtStr}_${upc}_${salesChannel}`,
      month: dtStr,
      upc: upc,
      salesChannel,
      forecast: parseInt(Math.round(monthlyBurn, 0)),
      locked: false,
      // calculatedForecast: monthlyBurn,
    };
    if (forecastObj.forecast > 50) forecastData.push(forecastObj);
  }
  if (Object.values(forecastData).length > 0) {
    await upsertRows("forecasts", "opsForecast", "uniqueKey", Object.values(forecastData));
  }

  return true;
});
exports.upsertOnCall = onCall(async (data, context) => {
  const { datasetId, table, key, rows } = data.data;
  if (!table || !data || !key || !rows) {
    return false;
  }
  await upsertRows(datasetId, table, key, rows);
  return true;
});
exports.runQueryOnCall = onCall(async (data, context) => {
  // console.log("data", data.data);
  const { options } = data.data;
  // console.log("options", options);
  try {
    const res = await runQuery(options);
    return res;
  } catch (e) {
    return e;
  }
});
exports.catchGorgiasHookOnRequest = onRequest(async (req, res) => {
  if (req.method !== "POST") {
    res.status(405).send("Method Not Allowed");
    return;
  }
  const { headers, body: hook } = req;
  // console.log("hook", hook);
  if (headers.key !== "bovirgddiY%S3&7n@LUIL50fxB&Ddw2SA@fLHs$Z9&LtsXZVm2%EKW3jSnfkqjE1rpu0iQuCGn0s%dgo7I5y*1j$i") {
    res.status(401).send("Unauthorized");
    return;
  }

  // eslint-disable-next-line quotes
  const tags = JSON.parse(hook.tags.replaceAll("'", '"')).map((x) => ({ id: x.id, name: x.name }));
  const tagNames = tags.map((x) => x.name).join(", ");
  const tagIds = tags.map((x) => x.id).join(", ");
  const createdAt = hook.created_datetime;
  const updatedAt = hook.updated_datetime;
  const closedAt = hook.closed_datetime;
  const domain = hook.domain;
  const priority = hook.priority;
  const rows = [{
    // eslint-disable-next-line quotes
    id: hook.id,
    status: hook.status,
    tags,
    tagNames,
    tagIds,
    subject: hook.subject,
    channel: hook.channel,
    createdAt: new Date(createdAt),
    updatedAt: new Date(updatedAt),
    domain,
    priority,
    hook: JSON.stringify(hook),
    closedAt: closedAt ? new Date(closedAt) : null,
  }];
  //  console.log("rows", rows);
  await upsertRows("customerService", "gorgiasTickets", "id", rows);
  res.status(200).send("Success");
  return;
});
exports.updateInboundsOnCall = onCall(async (data, context) => {
  const { locs } = data;
  // eslint-disable-next-line max-len
  let url = "https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=2418&deploy=1&compid=6810379&ns-at=AAEJ7tMQ-8spb70Ks4Zz8gBeb1WLtwrhjBx_OuvzRQUsbKLvYfk";
  if (locs) {
    url += `&locs=${locs}`;
  }
  let res = await axios.get(url);
  const tempData = await res.data;
  // console.log({tempData});
  const itemData = tempData.itemResults;
  // const itemObj = {};
  const finalData = [];
  for (const item of itemData) {
    // console.log({item});
    let launchDate = "";
    let endDate = "";
    if (
      item.values["GROUP(custitem24)"] !== "" &&
      item.values["GROUP(custitem24)"]
    ) {
      launchDate = getText(item.values["GROUP(custitem24)"]);
    }
    if (
      item.values["GROUP(custitem25)"] !== "" &&
      item.values["GROUP(custitem25)"]
    ) {
      endDate = getText(item.values["GROUP(custitem25)"]);
    }
    const itemData = {
      id: getText(item["GROUP(upccode)"]),
      desc: getText(item.values["GROUP(salesdescription)"]),
      lifeStatus: getText(item.values["GROUP(custitem20)"]),
      color: getText(item.values["GROUP(custitem_color)"]),
      productType: getText(item.values["GROUP(custitem_product_type)"]),
      sku: getText(item.values["GROUP(externalid)"]),
      upc: getText(item.values["GROUP(upccode)"]),
      qtyAvail: item.values["SUM(formulanumeric)"],
      imageUrl: getText(item.values.imageurl),
      qtyOnHand: item.values["SUM(locationquantityonhand)"],
      launchDate,
      endDate,
      basePrice: formatAsCurrency(item.values["MAX(baseprice)"]),
      prodSpec: getText(item.values["GROUP(custitem2)"]),
      qtyBreakdown: item.values["qtybreakdown"],
    };
    finalData.push(itemData);
  }
  if (locs) {
    url += `&locs=${locs}`;
  }
  // eslint-disable-next-line max-len
  url = `https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=2417&deploy=1&compid=6810379&ns-at=AAEJ7tMQgO545hv9RpDZohHE4uawyHEvVJRc0BIlSFL0j8af2Ww&reqType=json&locs=${locs}`;
  res = await axios.get(url);
  const poData = await res.data;
  // console.log('poData', poData);
  for (const d of finalData) {
    const sku = d.sku;
    if (!d.poLines) {
      d.poLines = [];
    }
    for (const line of poData) {
      if (line[2] === sku) {
        const cobj = {
          poNumber: line[0],
          qty: line[5],
          expectedDate: new Date(line[6]),
          type: line[8],
          loc: line[10],
        };
        if (line[8] == "Inbound") {
          cobj.containerNum = line[9];
        }
        d.poLines.push(cobj);
        // console.log('has po lines', line[2]);
      }
    }
  }
  // console.log(
  //   "setting po data",
  //   data.filter((x) => x.poLines.length > 0)
  // );
  for (const d of finalData) {
    d.totalIncoming = 0;
    if (d.poLines.length > 0) {
      d.totalIncoming = d.poLines.reduce((a, b) => a + parseInt(b.qty), 0);
      const sortedLines = d.poLines.sort(
        (a, b) => a.expectedDate - b.expectedDate,
      );
      // console.log(sortedLines);
      // d.poLines = sortedLines;
      d.expectedDate =
        sortedLines[0].expectedDate.toLocaleDateString() +
        ` (${formatAsLargeInt(sortedLines[0].qty)})`;
    }
  }
  await upsertRows("items", "inbounds", "sku", finalData);
  await modifyDoc("settings", "recentUpdates", { "inbounds": new Date() });
  return {
    status: 200,
    msg: "Success",
  };
});

exports.updateBigQueryReportsOnCall = onCall(async (data, context) => {
  await updateBigQueryReports();
  return true;
});
exports.updateBigQueryReportsOnSchedule = onSchedule("every 15 minutes", async (context) => {
  await updateBigQueryReports();
  return true;
});
exports.updateSalesHistoryOnCall = onCall(async (data, context) => {
  let { startDate, endDate } = data.data;
  const salesQuery = `SELECT 
      s.trandate date, 
      c.fullname customer, 
      SUM(s.amountnet) amount, 
      l.name loc, 
      class.name class, 
      sc.name saleschannel, 
      div.name division 
    FROM 
      salesinvoiced as s 
    LEFT JOIN customer c on c.id=s.entity 
    LEFT JOIN transaction t on t.id = s.transaction 
    LEFT JOIN location l on l.id = t.location 
    LEFT JOIN classification class on class.id = s.class 
    LEFT JOIN saleschannel sc on sc.id=t.saleschannel 
    LEFT JOIN customrecord_cseg_division div on div.id = t.cseg_division 
    WHERE s.amountnet!=0 AND s.trandate BETWEEN '${startDate}' AND '${endDate}' 
    GROUP BY s.trandate, c.fullname, l.name, class.name, sc.name, div.name
    `;
  const results = await queryNetSuite(salesQuery, false);
  if (typeof results === "string") {
    // console.log("Error:", results);
    return false;
  }
  if (!results || !results.length) return false;
  startDate = new Date(startDate).toISOString().split("T")[0];
  endDate = new Date(endDate).toISOString().split("T")[0];
  await runQuery({ query: `DELETE FROM \`hj-reporting.sales.salesByCustomerByDay\` WHERE date BETWEEN '${startDate}' AND '${endDate}'` });
  const formattedResults = results.map((x) => {
    return {
      date: new Date(x.date).toISOString().split("T")[0],
      customer: x.customer,
      amount: parseFloat(x.amount),
      loc: x.loc,
      class: x.class,
      saleschannel: x.saleschannel,
      division: x.division,
    };
  });
  await upsertRows("sales", "salesByCustomerByDay", null, formattedResults);
});

exports.addBigQueryTableOnCall = onCall({
  memory: "2GiB",
  timeoutSeconds: 540, // Maximum timeout (9 minutes)
}, async (data, context) => {
  console.log("=== addBigQueryTableOnCall FUNCTION STARTED ===");
  console.log("Function invoked at:", new Date().toISOString());

  try {
    // Validate input data
    if (!data || !data.data) {
      console.error("Missing data object");
      throw new Error("Missing data object");
    }
    console.log("Request data size:", JSON.stringify(data.data).length, "bytes");

    const { datasetId, table, rows } = data.data;

    // Validate required parameters
    if (!datasetId) {
      console.error("Missing datasetId");
      throw new Error("Missing required parameter: datasetId");
    }
    if (!table) {
      console.error("Missing table");
      throw new Error("Missing required parameter: table");
    }
    if (!rows) {
      console.error("Missing rows");
      throw new Error("Missing required parameter: rows");
    }
    if (!Array.isArray(rows)) {
      console.error("Rows must be an array");
      throw new Error("Rows must be an array");
    }

    console.log(`Processing request to add ${rows.length} rows to ${datasetId}.${table}`);

    // Check for large datasets and implement batching
    const BATCH_SIZE = 10000; // Process in smaller batches to match client batching

    if (rows.length > BATCH_SIZE) {
      console.log(`Large dataset detected (${rows.length} rows). Processing in batches of ${BATCH_SIZE}.`);

      const results = [];
      const batches = [];

      // Split into batches
      for (let i = 0; i < rows.length; i += BATCH_SIZE) {
        batches.push(rows.slice(i, i + BATCH_SIZE));
      }

      console.log(`Created ${batches.length} batches for processing`);

      // Process batches sequentially to avoid overwhelming BigQuery
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`Processing batch ${i + 1}/${batches.length} with ${batch.length} rows`);

        try {
          const batchResult = await bigQueryAppendRowsWithLoadJob(datasetId, table, batch);
          results.push(batchResult);
          console.log(`Batch ${i + 1} completed successfully`);
        } catch (batchError) {
          console.error(`Batch ${i + 1} failed:`, batchError.message);
          throw new Error(`Batch ${i + 1} failed: ${batchError.message}`);
        }
      }

      const totalRows = results.reduce((sum, result) => {
        const match = result.message?.match(/(\d+) rows/);
        return sum + (match ? parseInt(match[1]) : 0);
      }, 0);

      const result = {
        success: true,
        message: `Successfully processed ${totalRows} rows in ${batches.length} batches`,
        batchResults: results,
        totalBatches: batches.length
      };

      console.log("All batches completed successfully:", result);
      return result;
    } else {
      // Call the BigQuery function for smaller datasets
      const result = await bigQueryAppendRowsWithLoadJob(datasetId, table, rows);
      return result;
    }
  } catch (error) {
    console.error("Error in addBigQueryTableOnCall:", error.message, error.stack);

    // Return a structured error response instead of throwing
    return {
      success: false,
      error: error.message || "Unknown error occurred",
      details: error.stack
    };
  }
});


exports.updateBigQueryTableOnRequest = onRequest({
  memory: "512MiB",
}, async (request, response) => {
  try {
    console.log("request.body", request.body);
    const data = request.body?.data;
    const {
      bigQueryProjectId,
      bigQueryDatasetId,
      bigQueryTableId,
      bigQueryUniqueKey,
      syncMethod,
      searchResults } = data;
    if (!bigQueryProjectId || !bigQueryDatasetId || !bigQueryTableId || !bigQueryUniqueKey || !syncMethod || !searchResults) {
      console.log("Missing required fields");
      return response.status(400).send({
        success: false,
        error: "Missing required fields"
      });
    }
    // First, sanitize and normalize the data
    const normalizedRows = searchResults.map(row => {
      const newRow = {};
      for (const key in row) {
        if (Object.prototype.hasOwnProperty.call(row, key)) {
          newRow[bigQuerySanitizeFieldName(key)] = row[key];
        }
      }
      return newRow;
    });
    switch (syncMethod) {
      case "3": //append
        await bigQueryAppendRowsWithLoadJob(bigQueryDatasetId, bigQueryTableId, normalizedRows);
        break;
      case "1": //create or replace
        await bigQueryCreateTable(bigQueryDatasetId, bigQueryTableId, normalizedRows);
        await bigQueryReplaceTable(bigQueryDatasetId, bigQueryTableId, normalizedRows);
        break;
      case "2": //update or add
        await bigQueryCreateTable(bigQueryDatasetId, bigQueryTableId, normalizedRows);
        const uniqueKeyValues = normalizedRows.map(row => row[bigQuerySanitizeFieldName(bigQueryUniqueKey)]);
        await bigQueryDeleteRows(bigQueryDatasetId, bigQueryTableId, bigQuerySanitizeFieldName(bigQueryUniqueKey), uniqueKeyValues);
        await bigQueryAppendRowsWithLoadJob(bigQueryDatasetId, bigQueryTableId, normalizedRows);
        break;
      default:
        throw new Error("Invalid sync method");
    }
    console.log("Processed ", normalizedRows.length, " records");
    response.status(200).send({
      success: true,
      message: `Processed ${normalizedRows.length} records`,
    });
  } catch (error) {
    console.error("Error in updateBigQueryTableOnCall:", error);
    response.status(500).send({
      success: false,
      error: error.message
    });
  }
  return true;
});
const updateDataset = async (dataset) => {
  try {
    await modifyDoc("datasets", dataset.id, { isRefreshing: true });
    const { connectorId, operationId, updateType, tableId, key, datasetId, lookback } = dataset;
    const connector = await getDocData("connectors", connectorId);
    if (!connector) {
      return false;
    }
    const rows = [];

    const today = new Date();
    const bucket = "hj-reporting.firebasestorage.app";

    let modObj = {};
    // console.log("connector", connector);
    // console.log("dataset", dataset);

    switch (operationId) {
      case "tiktokGetOrders":
        const tiktokLookbackDate = new Date(lookback._seconds * 1000);
        tiktokLookbackDate.setDate(tiktokLookbackDate.getDate() - 7);
        const tiktokEndDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);

        const tiktokDayPointer = new Date(tiktokLookbackDate);

        try {
          let count = 0;
          while (tiktokDayPointer <= tiktokEndDate && count < 90) {
            const dayStart = new Date(tiktokDayPointer);
            const dayEnd = new Date(tiktokDayPointer);
            dayEnd.setHours(23, 59, 59, 999);

            const startISO = dayStart.toISOString();
            const endISO = dayEnd > today ? today.toISOString() : dayEnd.toISOString();

            console.log(`Fetching TikTok orders from ${startISO} to ${endISO}`);

            await modifyDoc("datasets", dataset.id, { lookback: dayStart });

            const orders = await tiktokGetOrders(connector.connection, new Date(startISO), new Date(endISO));

            const transformed_orders = [];

            orders.map((order) => {
              transformed_orders.push({
                "order_id(integer)": order["id"] || null,
                "order_status(string)": order["status"] || null,
                "buyer_email(string)": order["buyer_email"] || null,
                "created_date(datetime)": order["create_time"] ? formatISOToDateTime(order["create_time"] * 1000) : null,
                "cancel_order_sla_date(datetime)": order["cancel_order_sla_time"] ? formatISOToDateTime(order["cancel_order_sla_time"] * 1000) : null,
                "delivery_sla_date(datetime)": order["delivery_sla_time"] ? formatISOToDateTime(order["delivery_sla_time"] * 1000) : null,
                "fulfillment_type(string)": order["fulfillment_type"] || null,
                "is_replacement_order(boolean)": (order["is_replacement_order"]) ? "true" : "false",
                "is_exchange_order(boolean)": (order["is_exchange_order"]) ? "true" : "false",
                "is_sample_order(boolean)": (order["is_sample_order"]) ? "true" : "false",
                "shipping_type(string)": order["shipping_type"] || null,
                "update_datetime(datetime)": order["update_time"] ? formatISOToDateTime(order["update_time"] * 1000) : null,
                "line_id(integer)": null,
                "currency(string)": null,
                "display_status(string)": null,
                "item_tax(float)": null,
                "original_price(float)": null,
                "sale_price(float)": null,
                "seller_discount(float)": null,
                "seller_sku(string)": null,
                "tracking_number(string)": null,
                "postal_code(string)": (order["recipient_address"] && order["recipient_address"]["postal_code"]) ? order["recipient_address"]["postal_code"] : null,
                "region_code(string)": (order["recipient_address"] && order["recipient_address"]["region_code"]) ? order["recipient_address"]["region_code"] : null,
                "state(string)": (order["recipient_address"] && order["recipient_address"]["district_info"]) ? order["recipient_address"]["district_info"].find(item => item.address_level_name === "State")?.address_name : null,
              });

              if (order["line_items"] && order["line_items"].length > 0) {
                order["line_items"].forEach((line) => {
                  transformed_orders.push({
                    "order_id(integer)": order["id"] || null,
                    "order_status(string)": order["status"] || null,
                    "buyer_email(string)": null,
                    "created_date(datetime)": order["create_time"] ? formatISOToDateTime(order["create_time"] * 1000) : null,
                    "cancel_order_sla_date(datetime)": null,
                    "delivery_sla_date(datetime)": null,
                    "fulfillment_type(string)": null,
                    "is_replacement_order(boolean)": null,
                    "is_exchange_order(boolean)": null,
                    "is_sample_order(boolean)": null,
                    "shipping_type(string)": null,
                    "update_datetime(datetime)": order["update_time"] ? formatISOToDateTime(order["update_time"] * 1000) : null,
                    "line_id(integer)": line["id"] || null,
                    "currency(string)": line["currency"] || null,
                    "display_status(string)": line["display_status"] || null,
                    "item_tax(float)": line["item_tax"] ? line["item_tax"].reduce((sum, item) => sum + parseFloat(item.tax_amount), 0) : null,
                    "original_price(float)": line["original_price"] || null,
                    "sale_price(float)": line["sale_price"] || null,
                    "seller_discount(float)": line["seller_discount"] || null,
                    "seller_sku(string)": line["seller_sku"] || null,
                    "tracking_number(string)": line["tracking_number"] || null,
                    "postal_code(string)": null,
                    "region_code(string)": null,
                    "state(string)": null,
                  });
                });
              }
            });
            console.log(`Transformed ${transformed_orders.length} TikTok orders for ${dayStart.toISOString().slice(0, 10)}`);

            if (transformed_orders.length > 0) {
              const csv = convertJsonToCsv(transformed_orders);
              const tag = `${dayStart.getFullYear()}-${String(dayStart.getMonth() + 1).padStart(2, "0")}-${String(dayStart.getDate()).padStart(2, "0")}`;
              const file = `${datasetId}.${tableId}/orders_${tag}.csv`;
              await saveToCloudStorage(bucket, file, csv);
              console.log(`Uploaded CSV file: ${file}`);
            }

            tiktokDayPointer.setDate(tiktokDayPointer.getDate() + 1);
            count++;
          }

          const uploadTiktokResult = await bigQueryUploadCsvFiles({
            datasetId,
            tableId,
            bucketName: bucket,
            prefix: `${datasetId}.${tableId}/`
          });
          console.log("uploadTiktokResult", uploadTiktokResult);

          modObj = {
            isRefreshing: false,
            lastRefreshed: new Date(),
            status: "good"
          };
        } catch (error) {
          console.error("Error in fetching TikTok orders:", error.name + ": " + error.message);
          modObj = {
            isRefreshing: false,
            lastRefreshed: new Date(),
            status: "failed",
            error: error.message
          };
        }
        break;
      case "amazonGetOrders":
        const amazonLookbackDate = new Date(lookback._seconds * 1000);
        amazonLookbackDate.setDate(amazonLookbackDate.getDate() - 7);
        const amazonEndDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);

        const amazonDayPointer = new Date(amazonLookbackDate);

        try {
          let count = 0;
          while (amazonDayPointer <= amazonEndDate && count < 90) {
            const dayStart = new Date(amazonDayPointer);
            const dayEnd = new Date(amazonDayPointer);
            dayEnd.setHours(23, 59, 59, 999);

            const startISO = dayStart.toISOString();
            const endISO = dayEnd > today ? today.toISOString() : dayEnd.toISOString();

            console.log(`Fetching Amazon orders from ${startISO} to ${endISO}`);

            await modifyDoc("datasets", dataset.id, { lookback: dayStart });

            const orders = await amazonGetOrders(connector.connection, new Date(startISO), new Date(endISO));

            // console.log('orders.length',orders.length);
            // console.log('orders[0]',orders[0]);

            const transformed = orders.map((order) => {
              return {
                "order_id(string)": order["amazon-order-id"] || null,
                "order_status(string)": order["order-status"] || null,
                "currency(string)": order["currency"] || null,
                "sku(string)": order["sku"] || null,
                "item_status(string)": order["item-status"] || null,
                "quantity(integer)": order["quantity"] || null,
                "item_price(float)": order["item-price"] || null,
                "item_tax(float)": order["item-tax"] || null,
                "shipping_price(float)": order["shipping-price"] || null,
                "shipping_tax(float)": order["shipping-tax"] || null,
                "gift_wrap_price(float)": order["gift-wrap-price"] || null,
                "gift_wrap_tax(float)": order["gift-wrap-tax"] || null,
                "item_promotion_discount(float)": order["item-promotion-discount"] || null,
                "ship_promotion_discount(float)": order["ship-promotion-discount"] || null,
                "ship_city(string)": order["ship-city"] || null,
                "ship_state(string)": order["ship-state"] || null,
                "ship_postal_code(string)": order["ship-postal-code"] || null,
                "ship_country(string)": order["ship-country"] || null,
                "date_last_update(datetime)": order["last-updated-date"] ? formatISOToDateTime(order["last-updated-date"]) : null,
                "date_purchased(datetime)": order["purchase-date"] ? formatISOToDateTime(order["purchase-date"]) : null
              };
            });

            console.log(`Transformed ${transformed.length} Amazon orders for ${dayStart.toISOString().slice(0, 10)}`);

            if (transformed.length > 0 && transformed[0]["order_id(string)"]) {
              const csv = convertJsonToCsv(transformed);
              const tag = `${dayStart.getFullYear()}-${String(dayStart.getMonth() + 1).padStart(2, "0")}-${String(dayStart.getDate()).padStart(2, "0")}`;
              const file = `${datasetId}.${tableId}/orders_${tag}.csv`;
              await saveToCloudStorage(bucket, file, csv);
              console.log(`Uploaded CSV file: ${file}`);
            }

            amazonDayPointer.setDate(amazonDayPointer.getDate() + 1);
            count++;
          }
        } catch (error) {
          console.error("Error in fetching amazon orders:", error.name + ": " + error.message);
        }
        const uploadAmazonResult = await bigQueryUploadCsvFiles({
          datasetId,
          tableId,
          bucketName: bucket,
          prefix: `${datasetId}.${tableId}/`
        });
        console.log("uploadAmazonResult", uploadAmazonResult);

        modObj = {
          isRefreshing: false,
          lastRefreshed: new Date(),
          status: "good"
        };
        break;
      case "amazonGetLiveInventory":
        try {
          console.log("Fetching live Amazon inventory...");

          const inventory = await amazonGetLiveInventory(connector.connection);

          console.log("inventory.length", inventory.length);
          console.log("inventory[0]", inventory[0]);

          const transformed = inventory.map((item) => {
            const reserved = (item.inventoryDetails && item.inventoryDetails.reservedQuantity) ? item.inventoryDetails.reservedQuantity : {};
            const researching = (item.inventoryDetails && item.inventoryDetails.researchingQuantity) ? item.inventoryDetails.researchingQuantity : {};
            const unfulfillable = (item.inventoryDetails && item.inventoryDetails.unfulfillableQuantity) ? item.inventoryDetails.unfulfillableQuantity : {};
            const future = (item.inventoryDetails && item.inventoryDetails.futureSupplyQuantity) ? item.inventoryDetails.futureSupplyQuantity : {};

            return {
              "asin(string)": item.asin || null,
              "fn_sku(string)": item.fnSku || null,
              "seller_sku(string)": item.sellerSku || null,
              "condition(string)": item.condition || null,
              "fulfillable_quantity(integer)": item.inventoryDetails?.fulfillableQuantity || null,
              "inbound_working_quantity(integer)": item.inventoryDetails?.inboundWorkingQuantity || null,
              "inbound_shipped_quantity(integer)": item.inventoryDetails?.inboundShippedQuantity || null,
              "inbound_receiving_quantity(integer)": item.inventoryDetails?.inboundReceivingQuantity || null,
              "total_reserved_quantity(integer)": reserved.totalReservedQuantity || null,
              "pending_customer_order_quantity(integer)": reserved.pendingCustomerOrderQuantity || null,
              "pending_transshipment_quantity(integer)": reserved.pendingTransshipmentQuantity || null,
              "fc_processing_quantity(integer)": reserved.fcProcessingQuantity || null,
              "total_researching_quantity(integer)": researching.totalResearchingQuantity || null,
              "total_unfulfillable_quantity(integer)": unfulfillable.totalUnfulfillableQuantity || null,
              "customer_damaged_quantity(integer)": unfulfillable.customerDamagedQuantity || null,
              "warehouse_damaged_quantity(integer)": unfulfillable.warehouseDamagedQuantity || null,
              "distributor_damaged_quantity(integer)": unfulfillable.distributorDamagedQuantity || null,
              "carrier_damaged_quantity(integer)": unfulfillable.carrierDamagedQuantity || null,
              "defective_quantity(integer)": unfulfillable.defectiveQuantity || null,
              "expired_quantity(integer)": unfulfillable.expiredQuantity || null,
              "reserved_future_supply_quantity(integer)": future.reservedFutureSupplyQuantity || null,
              "future_supply_buyable_quantity(integer)": future.futureSupplyBuyableQuantity || null,
              "last_updated_time(string)": item.lastUpdatedTime || null,
              "product_name(string)": item.productName || null,
              "total_quantity(integer)": item.totalQuantity || null,
              "stores(string)": (item.stores) ? item.stores.join(", ") : null,
              "as_of(datetime)": formatISOToDateTime(new Date()),
            };
          });

          console.log("transformed", transformed[0]);

          console.log(`Transformed ${transformed.length} inventory records`);

          if (transformed.length > 0 && transformed[0]["asin(string)"]) {
            const csv = convertJsonToCsv(transformed);
            const file = `${datasetId}.${tableId}/live_inventory.csv`;
            await saveToCloudStorage(bucket, file, csv);
            console.log(`Uploaded inventory CSV file: ${file}`);
          }
        } catch (error) {
          console.error("Error in fetching Amazon inventory:", error.name + ": " + error.message);
        }

        const uploadInventoryResult = await bigQueryUploadCsvFiles({
          datasetId,
          tableId,
          bucketName: bucket,
          prefix: `${datasetId}.${tableId}/`
        });
        console.log("uploadInventoryResult", uploadInventoryResult);

        modObj = {
          isRefreshing: false,
          lastRefreshed: new Date(),
          status: "good"
        };
        break;
      case "shopifyGetOrders":
        const shopifyLookbackDate = new Date(lookback._seconds * 1000);
        //shopifyLookbackDate.setDate(shopifyLookbackDate.getDate() - 7);
        shopifyLookbackDate.setDate(shopifyLookbackDate.getDate());
        const shopifyToday = new Date();
        console.log("shopifyLookbackDate", shopifyLookbackDate);
        console.log("shopifyToday", shopifyToday);
        const shopifyBucketName = bucket;

        const shopifyDayPointer = new Date(shopifyLookbackDate);

        try {
          let shopifyCount = 0;
          while (shopifyDayPointer <= shopifyToday && shopifyCount < 150) {
            const shopifyDayStart = new Date(shopifyDayPointer);
            const shopifyDayEnd = new Date(shopifyDayStart);
            shopifyDayEnd.setHours(23, 59, 59, 999);

            const shopifyStartISO = shopifyDayStart.toISOString();
            const shopifyEndISO = shopifyDayEnd > shopifyToday ? shopifyToday.toISOString() : shopifyDayEnd.toISOString();

            await modifyDoc("datasets", dataset.id, { lookback: shopifyDayStart });

            const shopifyBulkQuery = `
              id
              name
              email
              legacyResourceId
              createdAt
              tags
              cancelledAt
              cancelReason
              discountCodes
              currencyCode
              note
              displayFulfillmentStatus
              displayFinancialStatus
              displayRefundStatus
              statusPageUrl
              poNumber
              sourceName
              paymentTerms { paymentTermsName }
              shippingAddress { 
                address1
                address2
                city
                province
                zip
                country
              }
              risk { assessments { riskLevel } }
              currentCartDiscountAmountSet { shopMoney { amount } }
              currentSubtotalPriceSet { shopMoney { amount } }
              currentTotalAdditionalFeesSet { shopMoney { amount } }
              currentTotalDiscountsSet { shopMoney { amount } }
              currentTotalDutiesSet { shopMoney { amount } }
              currentTotalPriceSet { shopMoney { amount } }
              currentTotalTaxSet { shopMoney { amount } }
              originalTotalAdditionalFeesSet { shopMoney { amount } }
              originalTotalDutiesSet { shopMoney { amount } }
              originalTotalPriceSet { shopMoney { amount } }
              totalCapturableSet { shopMoney { amount } }
              totalDiscountsSet { shopMoney { amount } }
              totalOutstandingSet { shopMoney { amount } }
              totalPriceSet { shopMoney { amount } }
              totalReceivedSet { shopMoney { amount } }
              totalRefundedSet { shopMoney { amount } }
              totalRefundedShippingSet { shopMoney { amount } }
              totalShippingPriceSet { shopMoney { amount } }
              totalTaxSet { shopMoney { amount } }
              totalTipReceivedSet { shopMoney { amount } }
              lineItems(first: 250) {
                edges {
                  node {
                    id
                    sku
                    quantity
                    product {
                      productType
                    }
                    variantTitle
                    variant {
                      barcode
                    }
                    originalUnitPriceSet { shopMoney { amount } }
                  }
                }
              }
              fulfillmentOrders(first: 10) {
                edges {
                  node {
                    deliveryMethod {
                      methodType
                    }
                  }
                }
              }
            `;

            console.log(`Fetching Shopify orders from ${shopifyStartISO} to ${shopifyEndISO}`);

            const resp = await getShopifyOrders({
              storeName: connector.connection.storeName,
              apiKey: connector.connection.apiKey,
              startDate: shopifyStartISO,
              maxDate: shopifyEndISO,
              node: shopifyBulkQuery
            });

            const orders = [];

            console.log("resp.orders.length", resp.orders.length);
            console.log("resp.orders[0]", resp.orders[0]);

            const orderData = {};

            resp.orders.forEach((oData) => {
              if (!orderData[oData.id]) {
                orderData[oData.id] = {
                  delivery_method: [],
                  name: null
                };
              }
              if (!orderData[oData.__parentId]) {
                orderData[oData.__parentId] = {
                  delivery_method: [],
                  name: null
                };
              }

              if (oData.id) {
                orderData[oData.id].name = oData.name;
                orderData[oData.id].date_created = (oData.createdAt) ? formatISOToDateTime(oData.createdAt) : null;
              }

              if (oData["deliveryMethod.methodType"]) {
                if (orderData[oData.__parentId].delivery_method.indexOf(oData["deliveryMethod.methodType"]) == -1) {
                  orderData[oData.__parentId].delivery_method.push(oData["deliveryMethod.methodType"]);
                }
              }
            });

            resp.orders.forEach((oData) => {
              if (!oData["deliveryMethod.methodType"]) {
                orders.push({
                  "id(string)": (oData.__parentId) ? oData.__parentId : oData.id,
                  "name(string)": (oData.name) ? oData.name : orderData[oData.__parentId].name,
                  "email(string)": (oData.email) ? oData.email : null,
                  "legacy_resource_id(integer)": (oData.legacyResourceId) ? oData.legacyResourceId : null,
                  "discount_codes(string)": (oData.discountCodes) ? oData.discountCodes : null,
                  "currency_code(string)": (oData.currencyCode) ? oData.currencyCode : null,
                  "note(string)": (oData.note) ? oData.note.replace(/[\r\n,]+/g, " ") : null,
                  "po_number(string)": (oData.poNumber) ? oData.poNumber.replace(/[\r\n,]+/g, " ") : null,
                  "source_name(string)": (oData.sourceName) ? oData.sourceName : null,
                  "payment_terms(string)": oData["paymentTerms.paymentTermsName"] ? oData["paymentTerms.paymentTermsName"] : null,
                  "risk_level(string)": oData["risk.assessments.riskLevel"] ? oData["risk.assessments.riskLevel"] : null,
                  "current_cart_discount_amount_set(float)": oData["currentCartDiscountAmountSet.shopMoney.amount"] ? oData["currentCartDiscountAmountSet.shopMoney.amount"] : null,
                  "current_subtotal_price_set(float)": oData["currentSubtotalPriceSet.shopMoney.amount"] ? oData["currentSubtotalPriceSet.shopMoney.amount"] : null,
                  "current_total_additional_fees_set(float)": oData["currentTotalAdditionalFeesSet.shopMoney.amount"] ? oData["currentTotalAdditionalFeesSet.shopMoney.amount"] : null,
                  "current_total_discounts_set(float)": oData["currentTotalDiscountsSet.shopMoney.amount"] ? oData["currentTotalDiscountsSet.shopMoney.amount"] : null,
                  "current_total_duties_set(float)": oData["currentTotalDutiesSet.shopMoney.amount"] ? oData["currentTotalDutiesSet.shopMoney.amount"] : null,
                  "current_total_price_set(float)": oData["currentTotalPriceSet.shopMoney.amount"] ? oData["currentTotalPriceSet.shopMoney.amount"] : null,
                  "current_total_tax_set(float)": oData["currentTotalTaxSet.shopMoney.amount"] ? oData["currentTotalTaxSet.shopMoney.amount"] : null,
                  "original_total_additional_fees_set(float)": oData["originalTotalAdditionalFeesSet.shopMoney.amount"] ? oData["originalTotalAdditionalFeesSet.shopMoney.amount"] : null,
                  "original_total_duties_set(float)": oData["originalTotalDutiesSet.shopMoney.amount"] ? oData["originalTotalDutiesSet.shopMoney.amount"] : null,
                  "original_total_price_set(float)": oData["originalTotalPriceSet.shopMoney.amount"] ? oData["originalTotalPriceSet.shopMoney.amount"] : null,
                  "total_capturable_set(float)": oData["totalCapturableSet.shopMoney.amount"] ? oData["totalCapturableSet.shopMoney.amount"] : null,
                  "total_discounts_set(float)": oData["totalDiscountsSet.shopMoney.amount"] ? oData["totalDiscountsSet.shopMoney.amount"] : null,
                  "total_outstanding_set(float)": oData["totalOutstandingSet.shopMoney.amount"] ? oData["totalOutstandingSet.shopMoney.amount"] : null,
                  "total_price_set(float)": oData["totalPriceSet.shopMoney.amount"] ? oData["totalPriceSet.shopMoney.amount"] : null,
                  "total_received_set(float)": oData["totalReceivedSet.shopMoney.amount"] ? oData["totalReceivedSet.shopMoney.amount"] : null,
                  "total_refunded_set(float)": oData["totalRefundedSet.shopMoney.amount"] ? oData["totalRefundedSet.shopMoney.amount"] : null,
                  "total_refunded_shipping_set(float)": oData["totalRefundedShippingSet.shopMoney.amount"] ? oData["totalRefundedShippingSet.shopMoney.amount"] : null,
                  "total_shipping_price_set(float)": oData["totalShippingPriceSet.shopMoney.amount"] ? oData["totalShippingPriceSet.shopMoney.amount"] : null,
                  "total_tax_set(float)": oData["totalTaxSet.shopMoney.amount"] ? oData["totalTaxSet.shopMoney.amount"] : null,
                  "total_tip_received_set(float)": oData["totalTipReceivedSet.shopMoney.amount"] ? oData["totalTipReceivedSet.shopMoney.amount"] : null,
                  "tags(string)": (oData.tags) ? oData.tags.replace(/[\r\n,]+/g, " ") : null,
                  "fulfillment_status(string)": (oData.displayFulfillmentStatus) ? oData.displayFulfillmentStatus : null,
                  "financial_status(string)": (oData.displayFinancialStatus) ? oData.displayFinancialStatus : null,
                  "refund_status(string)": (oData.displayRefundStatus) ? oData.displayRefundStatus : null,
                  "ship_to_address1(string)": oData["shippingAddress.address1"] ? oData["shippingAddress.address1"] : null,
                  "ship_to_address2(string)": oData["shippingAddress.address2"] ? oData["shippingAddress.address2"] : null,
                  "ship_to_city(string)": oData["shippingAddress.city"] ? oData["shippingAddress.city"] : null,
                  "ship_to_state(string)": oData["shippingAddress.province"] ? oData["shippingAddress.province"] : null,
                  "ship_to_zip(string)": oData["shippingAddress.zip"] ? oData["shippingAddress.zip"] : null,
                  "ship_to_country(string)": oData["shippingAddress.country"] ? oData["shippingAddress.country"] : null,
                  "status_page_url(string)": (oData.statusPageUrl) ? oData.statusPageUrl : null,
                  "cancelled_at(datetime)": (oData.cancelledAt) ? formatISOToDateTime(oData.cancelledAt) : null,
                  "cancel_reason(string)": (oData.cancelReason) ? oData.cancelReason : null,
                  "delivery_method(string)": orderData[oData.id].delivery_method.join("|"),
                  "upc(string)": (oData["variant.barcode"]) ? oData["variant.barcode"] : null,
                  "quantity(integer)": (oData.quantity) ? oData.quantity : null,
                  "price(float)": (oData["originalUnitPriceSet.shopMoney.amount"]) ? oData["originalUnitPriceSet.shopMoney.amount"] : null,
                  "date_created(datetime)": (oData.createdAt) ? formatISOToDateTime(oData.createdAt) : orderData[oData.__parentId].date_created,
                });
              }
            });
            console.log(`Retrieved ${orders.length} orders for ${shopifyDayStart.toISOString().slice(0, 10)}`);
            const shopifyCsvData = convertJsonToCsv(orders);
            const shopifyDayTag = `${shopifyDayStart.getFullYear()}-${String(shopifyDayStart.getMonth() + 1).padStart(2, "0")}-${String(shopifyDayStart.getDate()).padStart(2, "0")}`;
            const shopifyFileName = `${datasetId}.${tableId}/orders_${shopifyDayTag}.csv`;
            const shopifyNewFile = await saveToCloudStorage(shopifyBucketName, shopifyFileName, shopifyCsvData);
            console.log(`Saved file: ${shopifyNewFile}`);

            shopifyDayPointer.setDate(shopifyDayPointer.getDate() + 1);
            shopifyCount++;
          }
        } catch (shopifyError) {
          console.error("Error in fetching shopify orders:", shopifyError.name + ":" + shopifyError.message);
        }

        const shopifyUploadResult = await bigQueryUploadCsvFiles({
          datasetId,
          tableId,
          bucketName: shopifyBucketName,
          prefix: `${datasetId}.${tableId}/`
        });
        console.log("shopifyUploadResult", shopifyUploadResult);

        modObj = {
          isRefreshing: false,
          lastRefreshed: new Date(),
          status: "good"
        };
        break;
      case "shopifyGetDiscounts":
        const discountLookbackDate = new Date(lookback._seconds * 1000);
        discountLookbackDate.setDate(discountLookbackDate.getDate() - 7);
        const discountToday = new Date();
        console.log("discountLookbackDate", discountLookbackDate);
        console.log("discountToday", discountToday);
        const discountBucketName = bucket;

        const discountDayPointer = new Date(discountLookbackDate);

        try {
          let discountCount = 0;
          while (discountDayPointer <= discountToday && discountCount < 45) {
            const discountDayStart = new Date(discountDayPointer);
            const discountDayEnd = new Date(discountDayStart);
            discountDayEnd.setHours(23, 59, 59, 999);

            const discountStartISO = discountDayStart.toISOString();
            const discountEndISO = discountDayEnd > discountToday ? discountToday.toISOString() : discountDayEnd.toISOString();

            await modifyDoc("datasets", dataset.id, { lookback: discountDayStart });

            console.log(`Fetching Shopify discounts from ${discountStartISO} to ${discountEndISO}`);

            const resp = await getShopifyDiscounts({
              storeName: connector.connection.storeName,
              apiKey: connector.connection.apiKey,
              startDate: discountStartISO,
              maxDate: discountEndISO,
              node: null
            });

            const discounts = [];

            console.log("resp.discounts.length", resp.discounts.length);
            console.log("resp.discounts[0]", resp.discounts[0]);
            console.log("resp.discounts[1]", resp.discounts[0]);

            const discountData = {};

            resp.discounts.forEach((oData) => {
              if (!oData.__parentId) {
                if (!discountData[oData.id]) {
                  discountData[oData.id] = {};
                }

                discountData[oData.id] = {
                  "type(string)": (oData["discount.__typename"]) ? oData["discount.__typename"] : null,
                  "title(string)": (oData["discount.title"]) ? oData["discount.title"] : null,
                  "summary(string)": (oData["discount.summary"]) ? oData["discount.summary"] : null,
                  "status(string)": (oData["discount.status"]) ? oData["discount.status"] : null,
                  "created_at(datetime)": (oData["discount.createdAt"]) ? formatISOToDateTime(oData["discount.createdAt"]) : null,
                  "updated_at(datetime)": (oData["discount.updatedAt"]) ? formatISOToDateTime(oData["discount.updatedAt"]) : null,
                  "starts_at(datetime)": (oData["discount.startsAt"]) ? formatISOToDateTime(oData["discount.startsAt"]) : null,
                  "ends_at(datetime)": (oData["discount.endsAt"]) ? formatISOToDateTime(oData["discount.endsAt"]) : null,
                  "code_count(integer)": (oData["discount.codesCount.count"]) ? oData["discount.codesCount.count"] : null,
                  "combined_with_product_discount(boolean)": (oData["discount.combinesWith.productDiscounts"]) ? "true" : "false",
                };
              }
            });

            resp.discounts.forEach((oData) => {
              if (oData.__parentId) {
                discounts.push({
                  "id(string)": (oData["id"]) ? oData["id"] : null,
                  "code(string)": (oData["code"]) ? oData["code"] : null,
                  ...discountData[oData.__parentId]
                });
              }
            });

            console.log("discounts", discounts);

            console.log(`Retrieved ${discounts.length} discounts for ${discountDayStart.toISOString().slice(0, 10)}`);
            const discountCsvData = convertJsonToCsv(discounts);
            const discountDayTag = `${discountDayStart.getFullYear()}-${String(discountDayStart.getMonth() + 1).padStart(2, "0")}-${String(discountDayStart.getDate()).padStart(2, "0")}`;
            const discountFileName = `${datasetId}.${tableId}/discounts_${discountDayTag}.csv`;
            const discountNewFile = await saveToCloudStorage(discountBucketName, discountFileName, discountCsvData);
            console.log(`Saved file: ${discountNewFile}`);

            discountDayPointer.setDate(discountDayPointer.getDate() + 1);
            discountCount++;
          }
        } catch (discountError) {
          console.error("Error in fetching shopify discount:", discountError.name + ":" + discountError.message);
        }

        const discountUploadResult = await bigQueryUploadCsvFiles({
          datasetId,
          tableId,
          bucketName: discountBucketName,
          prefix: `${datasetId}.${tableId}/`
        });
        console.log("discountUploadResult", discountUploadResult);

        modObj = {
          isRefreshing: false,
          lastRefreshed: new Date(),
          status: "good"
        };
        break;
      case "netsuiteGetSavedSearch":
        const taskData = {
          data: {
            taskType: "netsuiteSavedSearchToBigQuery",
            connector,
            dataset: {
              id: dataset.id,
              projectId: dataset.projectId,
              datasetId: dataset.datasetId,
              tableId: dataset.tableId,
              key: (dataset.key) ? dataset.key : "",
              updateType: dataset.updateType,
            },
            searchId: dataset.savedSearchId,
            filterField: (dataset.filterField) ? dataset.filterField : null,
            filterValue: (dataset.filterValue) ? dataset.filterValue : null,
            maxResults: (dataset.maxResults) ? dataset.maxResults : 5000,
          }
        };
        // console.log("task data", JSON.stringify(taskData));
        // Always use Google Cloud Tasks (will route to sandboxTaskQueue in testing environments)
        await createTask("netsuiteTaskQueue", taskData, "https://netsuitetaskoncall-c2qnsmvlmq-uc.a.run.app");
        modObj = {
          "status": "pending",
          "isSuccess": null,
          "error": null,
        };
        break;
      case "netsuiteSuiteQL":
        // Execute the SuiteQL query from the dataset
        if (!dataset.query) {
          throw new Error("SuiteQL query is required for netsuiteSuiteQL operation");
        }
        if (dataset.useTimeFilter) {
          const startTime = dataset.timeFilterStartDate ? new Date(dataset.timeFilterStartDate).toISOString() : null;
          const endTime = dataset.timeFilterEndDate ? new Date(dataset.timeFilterEndDate).toISOString() : null;
          dataset.query = dataset.query.replace("{{startTime}}", startTime).replace("{{endTime}}", endTime);
        }

        const countQuery = `SELECT COUNT(*) FROM (${dataset.query})`;
        const countResp = await netsuiteSuiteQlQuery({ connector, q: countQuery });
        const count = parseInt(countResp.results[0]?.expr1) || 0;
        //split the count into chunks of 1000
        const countChunks = Math.ceil(count / 1000);
        //create tasks in the netsuite queue for each chunk
        // console.log("count", count, countChunks);
        const lastJobResults = {};
        const createPromises = [];
        for (let i = 0; i <= countChunks; i++) {
          const taskData = {
            data: {
              taskType: "netsuiteSuiteQLToBigQuery",
              connector,
              dataset: {
                id: dataset.id,
                projectId: dataset.projectId,
                datasetId: dataset.datasetId,
                tableId: dataset.tableId,
                key: dataset.key,
                updateType: dataset.updateType,
              },
              q: dataset.query,
              offset: i * 1000,
              limit: 1000,
              index: i,
            }
          };
          // console.log("task data", JSON.stringify(taskData));
          // Always use Google Cloud Tasks (will route to sandboxTaskQueue in testing environments)
          createPromises.push(createTask("netsuiteTaskQueue", taskData, "https://netsuitetaskoncall-c2qnsmvlmq-uc.a.run.app"));
          lastJobResults[i] = {
            "status": "pending",
            "isSuccess": null,
            "error": null,
          };
          // break;//TODO remove
        }
        await Promise.all(createPromises);
        modObj = {
          lastJobResults
        };
        await modifyDoc("datasets", dataset.id, modObj);
        return true;
      case "netsuiteTransactions":
        //load all the csv files into bigquery
        await bigQueryUploadCsvFiles({ datasetId, tableId, bucketName: "hj-reporting.firebasestorage.app", prefix: "netsuite_transactions/" });
        // console.log("uploadResult", uploadResult);
        modObj = {
          [`lastJobResults`]: {
            status: "success",
            isSuccess: true,
            error: null,
          },
          isRefreshing: false,
          lastRefreshed: new Date(),
          status: "good",
        };
        break;
      case "easypostPackages":
        //load all the csv files into bigquery
        await bigQueryUploadCsvFiles({ datasetId, tableId, bucketName: "hj-reporting.firebasestorage.app", prefix: "easypost_packages/" });
        // console.log("uploadEPResult", uploadEPResult);
        modObj = {
          [`lastJobResults`]: {
            status: "success",
            isSuccess: true,
            error: null,
          },
          isRefreshing: false,
          lastRefreshed: new Date(),
          status: "good",
        };
        break;
      default:
        throw new Error("Invalid operation");
    }
    if (rows?.length > 0) {
      if (updateType === "append") {
        await bigQueryAppendRows(datasetId, tableId, rows);
      }
      if (updateType === "replace") {
        await bigQueryReplaceTable(datasetId, tableId, rows);
      }
      if (updateType === "upsert") {
        await upsertRows(datasetId, tableId, key, rows);
      }
    }
    await modifyDoc("datasets", dataset.id, modObj);
    return true;
  } catch (error) {
    console.error("Error in updateDataset:", error.name + ":" + error.message);
    await modifyDoc("datasets", dataset.id, { isRefreshing: false, status: "error", error: JSON.stringify({ message: error?.response?.body?.message || error?.message || error?.response?.data?.message || error?.message, stack: error?.stack }) });
    return false;
  }
};
const getInboundData = async () => {
  const url = `https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=2417&deploy=1&compid=6810379&ns-at=AAEJ7tMQgO545hv9RpDZohHE4uawyHEvVJRc0BIlSFL0j8af2Ww&reqType=json`;
  const res = await fetch(url);
  const poData = await res.json();
  const inboundData = [];
  // console.log('poData', poData);
  for (let i = 1; i < poData.length; i++) {
    const line = poData[i];
    try {
      // console.log("line", line[6]);
      const expectedDate = line[6] && line[6].includes("/") ? new Date(line[6]).toISOString().split("T")[0] : null;
      const cobj = {
        type: line[8] == "Inbound" ? "inbound" : "po",
        upc: line[3] ? line[3].toString() : null,
        poNumber: line[0] ? line[0] : null,
        qty: line[5] ? parseInt(line[5]) : 0,
        expectedDate: expectedDate ? new Date(expectedDate) : null,
        loc: line[10] ? line[10] : null,
        containerNum: line[9] ? line[9] : null,
        billOfLading: line[11] ? line[11] : null,
      };
      inboundData.push(cobj);
    } catch (error) {
      console.error("Error in netsuiteTaskHandler:", error);
    }
    // console.log('has po lines', line
    // [2]);
  }
  return inboundData;
};
const netsuiteTaskHandler = async (data, context) => {
  try {
    const taskData = data.data;
    const { connector, dataset, taskType } = taskData;
    // console.log("taskType", taskType);
    // console.log("taskData", taskData);
    switch (taskType) {
      case "netsuiteSuiteQLToBigQuery":
        const { q, offset, limit, index } = taskData;
        try {
          const res = await netsuiteSuiteQlQuery({ connector, q, offset, limit });

          if (dataset.updateType == "replace") {
            await bigQueryReplaceTable(dataset.datasetId, dataset.tableId, res.results);
          } else if (dataset.updateType == "append") {
            await bigQueryAppendRows(dataset.datasetId, dataset.tableId, res.results);
          } else {
            await upsertRows(dataset.datasetId, dataset.tableId, dataset.key, res.results);
          }

          const modObj = {
            [`lastJobResults.${index}`]: {
              status: "success",
              isSuccess: true,
              error: null,
            },
            isRefreshing: false,
            lastRefreshed: new Date(),
            status: "good",
          };
          await modifyDoc("datasets", dataset.id, modObj);
          return res;
        } catch (error) {
          const modObj = {
            [`lastJobResults.${index}`]: {
              status: "error",
              isSuccess: false,
              error: error.message + " " + error.stack,
            },
            isRefreshing: false,
            lastRefreshed: new Date(),
            status: "error",
          };
          await modifyDoc("datasets", dataset.id, modObj);
          return false;
        }
      case "netsuiteSavedSearchToBigQuery":
        const { searchId, filterField, filterValue, maxResults } = taskData;
        try {
          const rows = await getNetSuiteSavedSearch({
            connector,
            searchId,
            filterField,
            filterValue,
            maxResults,
          });
          const formattedRows = rows.map(row =>
            Object.fromEntries(
              Object.entries(row).map(([key, value]) => [toSnakeCase(key), value])
            )
          );
          // console.log("formattedRows", formattedRows);
          // console.log("dataset.updateType", dataset.updateType);

          if (dataset.updateType == "replace") {
            await bigQueryReplaceTable(dataset.datasetId, dataset.tableId, formattedRows);
          } else if (dataset.updateType == "append") {
            await bigQueryAppendRows(dataset.datasetId, dataset.tableId, formattedRows);
          } else {
            await upsertRows(dataset.datasetId, dataset.tableId, dataset.key, formattedRows);
          }

          const modObj = {
            [`lastJobResults`]: {
              status: "success",
              isSuccess: true,
              error: null,
            },
            isRefreshing: false,
            lastRefreshed: new Date(),
            status: "good",
          };
          await modifyDoc("datasets", dataset.id, modObj);
          return formattedRows;
        } catch (error) {
          const modObj = {
            [`lastJobResults`]: {
              status: "error",
              isSuccess: false,
              error: error.message + " " + error.stack,
            },
            isRefreshing: false,
            lastRefreshed: new Date(),
            status: "error",
          };
          await modifyDoc("datasets", dataset.id, modObj);
          return false;
        }
      case "inventoryException": {
        try {
          // const salesSearchId = "104853";
          // const openSalesOrderSearchId = "104849";
          const itemProm = runQuery({
            query: `
              WITH items AS (
                SELECT
                  internalid,
                  CAST(launchdate AS STRING) AS launchdate,
                  CAST(enddate AS STRING) AS enddate,
                  productspecification,
                  productfamily,
                  productcategory,
                  productdivision,
                  upc,
                  producttype,
                  color,
                  size,
                  lifestatus
                FROM
                  \`hj-reporting.items.items_netsuite\`
              ),
              inventory AS (
                SELECT
                  upc,
                  on_hand,
                  on_order,
                  committed,
                  backordered,
                  available
                FROM
                  \`hj-reporting.inventory.live_inventory\`
                WHERE
                  location = 'HQ'
              ),
              sales AS (
                SELECT
                  upc,
                  ROUND(SUM(CASE WHEN Date >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY) THEN quantity ELSE 0 END) / -7.0, 0) AS avg7,
                  ROUND(SUM(CASE WHEN Date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY) THEN quantity ELSE 0 END) / -30.0, 0) AS avg30,
                  ROUND(SUM(quantity) / -90.0, 0) AS avg90
                FROM
                  \`hj-reporting.transactions.transactions_netsuite\` AS t
                WHERE
                  Date >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
                  AND class != 'Wholesale'
                  AND account = '13010 Inventory Asset'
                  AND is_posting = TRUE
                  AND type NOT IN ('InvAdjst', 'InvTrnfr', 'ItemRcpt', 'InvWksht')
                  AND upc IS NOT NULL
                  AND created_from NOT LIKE 'TO%'
                GROUP BY
                  upc
              )

              SELECT
                i.*,
                inv.on_hand,
                inv.on_order,
                inv.committed,
                inv.backordered,
                inv.available,
                COALESCE(s.avg7, 0) AS avg7,
                COALESCE(s.avg30, 0) AS avg30,
                COALESCE(s.avg90, 0) AS avg90,
                CURRENT_TIMESTAMP() AS updatedAt
              FROM
                items i
              LEFT JOIN inventory inv ON i.upc = inv.upc
              LEFT JOIN sales s ON i.upc = s.upc;

            `,
          });
          const orderProm = runQuery({
            query: `
            SELECT
              t.internal_id,
              t.document_number,
              CAST(t.date AS STRING) as date,
              t.upc,
              t.quantity*-1 as qty,
              (t.quantity - t.quantity_shipped_received)*-1 AS qty_open,
              t.status,
              c.name as customer,
              t.channel,
              t.class,
              t.location,
              t.amount,
              CAST(t.dispatch_date AS STRING) as dispatch_date
            FROM
              \`hj-reporting.transactions.transactions_netsuite\` t
            LEFT JOIN
              \`hj-reporting.items.items_netsuite\` AS i ON i.upc = t.upc
            LEFT JOIN
              \`hj-reporting.customers.customers_netsuite\` AS cust
              ON cust.internal_id = t.customer_id
            WHERE
              date >= DATE_SUB(CURRENT_DATE(), INTERVAL 360 DAY)
              AND type = 'SalesOrd'
              and is_closed =FALSE
              AND status NOT IN (
                'Sales Order : Billed',
                'Sales Order : Closed',
                'Sales Order : Cancelled',
                'Sales Order : Pending Billing'
              )
              AND t.upc is not null
              AND (t.quantity - t.quantity_shipped_received)*-1 > 0
            ORDER BY
              t.document_number,
              t.line_id;
          `,
          });
          const inboundProm = getInboundData();
          const [itemList, openOrders, inbounds] = await Promise.all([itemProm, orderProm, inboundProm]);
          //calculate avg7, avg30, avg90
          const upcObj = itemList.reduce((acc, item) => {
            acc[item.upc] = {
              ...item,
              updatedAt: new Date(),
              openOrders: openOrders.filter(o => o.upc === item.upc),
              inbounds: inbounds.filter(i => i.upc === item.upc),
            };
            return acc;
          }, {});

          // 5. Build docs by upc
          // This transforms the upcObj object into an array of documents for Firestore
          // Each document has an id (UPC with spaces replaced by underscores) and data (the item object)
          const docs = Object.entries(upcObj).map(([upc, itemObj]) => ({ id: upc.replace(/ /g, "_").replace(/\//g, "-"), data: { ...itemObj, refreshedAt: new Date() } }));

          // 6. Upsert to Firestore
          const modifyChunkLimit = 500;
          const modifyProms = [];
          for (let i = 0; i < docs.length; i += modifyChunkLimit) {
            const chunk = docs.slice(i, i + modifyChunkLimit);
            modifyProms.push(modifyDocs("inventoryExceptions", chunk));
          }
          await Promise.all(modifyProms);
          // console.log("Inventory Exception Report updated", docs.length, "docs");
          return { status: "success", count: docs.length };
        } catch (error) {
          console.error("Error in inventoryException:", error);
          return { status: "error", error: error.message + " " + error.stack };
        }
      }
      case "forecastBeta": {
        // const salesSearchId = "104853";
        // const openSalesOrderSearchId = "104849";
        const items = await runQuery({
          query: `select internalid, CAST(launchdate AS STRING) as launchdate, CAST(enddate AS STRING) as enddate, productspecification, productfamily, productcategory, productdivision, upc, producttype, color, size, lifestatus FROM items.items_netsuite`,
        });
        const inventory = await runQuery({
          query: `select upc, on_hand, committed, backordered, available FROM inventory.live_inventory WHERE location='HQ'`,
        });
        const dateRanges = [
          { startDate: new Date(new Date().setDate(new Date().getDate() - 7)), endDate: new Date(), days: 7 }, //last 7 days
          { startDate: new Date(new Date().setDate(new Date().getDate() - 30)), endDate: new Date(), days: 30 }, //last 30 days
          { startDate: new Date(new Date().setDate(new Date().getDate() - 90)), endDate: new Date(), days: 90 }, //last 90 days
        ];
        const saleObj = {};
        for (const dateRange of dateRanges) {
          const sales = await runQuery({
            query: `
              WITH calendar AS (
                SELECT
                  date
                FROM
                  UNNEST(GENERATE_DATE_ARRAY(${dateRange.startDate.toISOString().split("T")[0]}, ${dateRange.endDate.toISOString().split("T")[0]})) AS date
              ),

              distinct_upcs AS (
                SELECT DISTINCT
                  upc,
                  producttype,
                  color,
                  size,
                  lifestatus,
                  DATE(launchdate) AS launchdate
                FROM \`hj-reporting.items.items_netsuite\`
                WHERE upc IS NOT NULL
                  AND LOWER(lifestatus) IN ('active', 'launching', 'phasing out')
              ),

              calendar_upc AS (
                SELECT
                  c.date,
                  u.upc
                FROM
                  calendar c
                CROSS JOIN  
                  (SELECT upc FROM distinct_upcs) u
              ),

              sales AS (
                SELECT
                  t.upc,
                  t.forecast_node,
                  t.Date AS sale_date,
                  SUM(CASE
                      WHEN t.Account = '13010 Inventory Asset' THEN t.quantity
                      ELSE 0
                  END) * -1 AS units_sold
                FROM
                  \`hj-reporting.transactions.transactions_netsuite\` AS t
                WHERE
                  t.type NOT IN ('InvAdjst','InvTrnfr','ItemRcpt','InvWksht')
                  AND t.Date >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
                  AND is_posting = true
                  AND account = '13010 Inventory Asset'
                  AND t.upc IS NOT NULL
                  AND t.created_from NOT LIKE 'TO%'
                  AND t.upc IN (
                    SELECT upc FROM distinct_upcs
                  )
                GROUP BY
                  t.upc, t.forecast_node, t.Date
              ),

              inventory AS (
                SELECT
                  upc,
                  date,
                  qty_available
                FROM
                  \`hj-reporting.inventory.historical_inventory_netsuite\`
                WHERE
                  date >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)
              ),

              daily_status AS (
                SELECT
                  cu.date,
                  cu.upc,
                  s.forecast_node,
                  COALESCE(s.units_sold, 0) AS sales_qty,
                  i.qty_available > 0 AS in_stock,
                  d.launchdate IS NOT NULL AND cu.date >= d.launchdate AS launched,
                  d.producttype,
                  d.color,
                  d.size,
                  d.lifestatus,
                  d.launchdate
                FROM
                  calendar_upc cu
                LEFT JOIN
                  sales s
                  ON cu.upc = s.upc AND cu.date = s.sale_date
                LEFT JOIN
                  inventory i
                  ON cu.upc = i.upc AND cu.date = i.date
                LEFT JOIN
                  distinct_upcs d
                  ON cu.upc = d.upc
              )

              SELECT
                upc,
                forecast_node,
                producttype,
                color,
                size,
                lifestatus,
                launchdate,
                AVG(sales_qty) AS avg_daily_sales_run_rate
              FROM
                daily_status
              WHERE
                in_stock = TRUE
                AND launched = TRUE
              GROUP BY
                upc, forecast_node, producttype, color, size, lifestatus, launchdate;
              `,
          });
          saleObj[dateRange.days] = sales;
        }
        const openOrders = await runQuery({
          query: `
            SELECT
              internal_id,
              document_number,
              CAST(date AS STRING) as date,
              upc,
              quantity*-1 as qty,
              (quantity - quantity_shipped_received)*-1 AS qty_open,
              status,
              customer,
              channel,
              class,
              location,
              amount,
              CAST(dispatch_date AS STRING) as dispatch_date
            FROM
              \`hj-reporting.transactions.transactions_netsuite\`
            WHERE
              date >= DATE_SUB(CURRENT_DATE(), INTERVAL 360 DAY)
              AND type = 'SalesOrd'
              and class ='Wholesale'
              and is_closed =FALSE
              AND status NOT IN (
                'Sales Order : Billed',
                'Sales Order : Closed',
                'Sales Order : Pending Billing'
              )
              AND UPC is not null
              AND (quantity - quantity_shipped_received)*-1 > 0
            ORDER BY
              document_number,
              line_id;

          `,
        });
        const inbounds = await getInboundData();
        //calculate avg7, avg30, avg90
        const upcObj = items.reduce((acc, item) => {
          // item.launchDate = item.launchdate ? new Date(item.launchdate) : null;
          // item.endDate = item.enddate ? new Date(item.enddate) : null;
          acc[item.upc] = {
            ...item,
            ...inventory.find(i => i.upc === item.upc),
            openOrders: openOrders.filter(o => o.upc === item.upc),
            inbounds: inbounds.filter(i => i.upc === item.upc),
            updatedAt: new Date(),
          };
          return acc;
        }, {});
        for (const dateRange in saleObj) {
          const sales = saleObj[dateRange];
          for (const saleLine of sales) {
            const upc = saleLine.upc;
            const itemObj = upcObj[upc];
            if (!itemObj) {
              continue;
            }
            itemObj[`avg${dateRange}`] = saleLine.avg_daily_sales_run_rate;
          }
        }
        // 5. Build docs by upc
        // This transforms the upcObj object into an array of documents for Firestore
        // Each document has an id (UPC with spaces replaced by underscores) and data (the item object)
        const docs = Object.entries(upcObj).map(([upc, itemObj]) => ({ id: upc.replace(/ /g, "_").replace(/\//g, "-"), data: { ...itemObj, refreshedAt: new Date() } }));

        // 6. Upsert to Firestore
        const modifyChunkLimit = 1000;
        for (let i = 0; i < docs.length; i += modifyChunkLimit) {
          const chunk = docs.slice(i, i + modifyChunkLimit);
          await modifyDocs("inventoryExceptions", chunk);
        }
        return { status: "success", count: docs.length };
      }
      default:
        throw new Error("Invalid task type");
    }
  } catch (error) {
    console.error("Error in netsuiteTaskOnCall:", error);
    return false;
  }
};

exports.netsuiteTaskOnCall = onCall(
  {
    timeoutSeconds: 3600,
    memory: "1GiB",
    maxInstances: 1,
    maxConcurrentRequests: 1,
  },
  netsuiteTaskHandler
);
exports.updateDatasetOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "16GiB",
  cpu: 4,
  maxInstances: 1,
  maxConcurrentRequests: 1,
}, async ({ data, context }) => {
  const { datasetId } = data;
  // console.log("datasetId", datasetId);
  const dataset = await getDocData("datasets", datasetId);
  if (!dataset) {
    return false;
  }
  await updateDataset(dataset);
  return true;
});
exports.updateDatasetOnSchedule = onSchedule({
  schedule: "every 1 hours",
  timeoutSeconds: 3600,
  memory: "16GiB",
  cpu: 4,
  maxInstances: 1,
  maxConcurrentRequests: 1,
}, async (context) => {
  const adjusted_date = getAdjustedDate();
  let datasets = await queryDocs("datasets", [{
    key: "isActive",
    operator: "==",
    val: true,
  }]);
  await modifyDocs("datasets", datasets.map(dataset => ({
    id: dataset.id,
    info: (["orders_amazon", "orders_tiktok", "discounts_shopify"].indexOf(dataset.tableId) >= 0 && adjusted_date) ? { //orders_shopify
      isRefreshing: false,
      lookback: adjusted_date,
    } : {
      isRefreshing: false,
    }
  })));

  datasets = await queryDocs("datasets", [{
    key: "isActive",
    operator: "==",
    val: true,
  }]);

  //group datasets by connectorId
  const datasetsByConnector = datasets.reduce((acc, dataset) => {
    acc[dataset.connectorId] = [...(acc[dataset.connectorId] || []), dataset];
    return acc;
  }, {});
  //update datasets so all connectors are updated at the same time, but each connector is updated in series
  const promises = [];
  for (const connectorId in datasetsByConnector) {
    promises.push(Promise.all(datasetsByConnector[connectorId].map(async (dataset) => {
      await updateDataset(dataset);
    })));
  }
  await Promise.all(promises);
  return true;
});

exports.createDocOnCall = onCall(async ({ data, context }) => {
  const { collectionName, info } = data;
  if (!collectionName || !info) {
    return false;
  }
  return await createDoc(collectionName, info);
});
exports.deleteDocOnCall = onCall(async ({ data, context }) => {
  const { collectionName, docId } = data;
  // console.log(data);
  if (!collectionName || !docId) {
    return false;
  }
  await deleteDoc(collectionName, docId);
  return true;
});
exports.modifyDocOnCall = onCall(async ({ data, context }) => {
  const { collectionName, docId, info, merge } = data;
  if (!collectionName || !docId || !info) {
    return false;
  }
  await modifyDoc(collectionName, docId, info, merge);
  return true;
});
exports.getFirebaseQueryDataOnCall = onCall(async ({ data, context }) => {
  const { collectionName, queryList, id } = data;
  if (!collectionName || !queryList) {
    return false;
  }
  return await queryDocs(collectionName, queryList, id);
});
exports.bigQueryGetDatasetsAndTablesOnCall = onCall(async ({ data, context }) => {
  return await bigQueryGetDatasetsAndTables();
});
exports.bigQueryRunQueriesOnCall = onCall(async ({ data, context }) => {
  const { ids } = data;
  return await bigQueryRunQueries(ids);
});

exports.bigQueryAddColumnOnCall = onCall(async ({ data, context }) => {
  const { datasetId, tableId, newColumnName, newColumnType, options } = data;
  if (!datasetId || !tableId || !newColumnName || !newColumnType) {
    return false;
  }
  return await addColumn(datasetId, tableId, newColumnName, newColumnType, options);
});
exports.bigQueryEditColumnOnCall = onCall(async ({ data, context }) => {
  const { datasetId, tableId, columnName, newColumnName, newColumnType, options } = data;
  if (!datasetId || !tableId || !columnName || !newColumnName || !newColumnType) {
    return false;
  }
  await editColumn(datasetId, tableId, columnName, newColumnName, newColumnType, options);
  return true;
});
exports.bigQueryDeleteColumnOnCall = onCall(async ({ data, context }) => {
  const { datasetId, tableId, columnName } = data;
  if (!datasetId || !tableId || !columnName) {
    return false;
  }
  return await deleteColumn(datasetId, tableId, columnName);
});
exports.bigQueryRunQueryOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async ({ data, context }) => {
  // console.log("running big query", data);
  const { options } = data;
  // console.log(data);
  if (!options) {
    return { success: false, error: "Missing options parameter", data: [] };
  }

  try {
    const result = await runQuery(options);
    return { success: true, data: result };
  } catch (error) {
    console.error("Error in bigQueryRunQueryOnCall:", error.message, error.stack);
    return { success: false, error: error.message, data: [] };
  }
});

exports.updateNetSuiteRecordOnCall = onCall(async (data, context) => {
  const { recordType, id, data: updateData } = data.data;
  if (!recordType || !id || !data) {
    return false;
  }
  await updateNetSuiteRecord(recordType, id, updateData);
});

exports.createNetSuiteRecordOnCall = onCall(async (data, context) => {
  // console.log(data.data);
  const { recordType, recordData } = data.data;
  if (!recordType || !recordData) {
    return false;
  }
  return await createNetSuiteRecord(recordType, recordData);
});

// Shopify
exports.refreshShopifyOrdersOnCall = onCall(async (data, context) => {
  try {
    const { connectorId } = data.data;
    if (!connectorId) {
      return {
        success: false,
        message: "Connector ID is required",
      };
    }
    const connector = await getDocData("connectors", connectorId);
    if (!connector) {
      return {
        success: false,
        message: "Connector not found",
      };
    }
    const { storeName, apiKey } = connector.connection;
    if (!storeName || !apiKey || !connector.orderLookback) {
      return {
        success: false,
        message: "Connector connection is missing required fields",
      };
    }
    // console.log("Getting Shopify orders");
    let { orders, maxDate } = await getShopifyOrders({ storeName, apiKey, startDate: connector.orderLookback });
    orders = orders.map((order) => {
      return {
        storeName,
        ...order
      };
    });
    // console.log("Upserting Shopify orders", orders.length);
    await upsertRows(connector.datasetId, connector.tableId, connector.key, orders);
    // const lastOrderDate = orders.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))[orders.length - 1].createdAt;
    await modifyDoc("connectors", connectorId, { orderLookback: maxDate });
    return {
      success: true,
      message: `Processed ${orders.length} orders`,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
});
exports.refreshShopifyOrdersOnSchedule = onSchedule({
  schedule: "every 5 minutes",
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async (context) => {
  const connectors = await queryDocs("connectors", [{
    key: "system",
    operator: "==",
    val: "shopify"
  }]);
  let totalOrders = 0;
  for (const connector of connectors) {
    try {
      const { storeName, apiKey } = connector.connection;
      // console.log("Getting Shopify orders", connector.id);
      let orders = await getShopifyOrders({ storeName, apiKey, startDate: connector.orderLookback });
      orders = orders.map((order) => {
        return {
          storeName,
          ...order
        };
      });
      totalOrders += orders.length;
      // console.log("Upserting Shopify orders", orders.length);
      await upsertRows(connector.datasetId, connector.tableId, connector.key, orders);
      const lastOrderDate = orders.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))[orders.length - 1].createdAt;
      await modifyDoc("connectors", connector.id, { orderLookback: lastOrderDate });
    } catch (error) {
      console.error("Error refreshing Shopify orders", error);
    }
  }
  return {
    success: true,
    message: `Processed ${totalOrders} orders`,
  };
});

// NetSuite SuiteQL Query to BigQuery Sync
exports.syncNetSuiteDataset = onCall(async (data, context) => {
  try {
    const { datasetId, connectorId } = data;
    const dataset = await getDocData("datasets", datasetId);
    const connector = await getDocData("connectors", connectorId);

    // Execute the query
    const queryResult = await netsuiteSuiteQlQuery({
      q: dataset.query,
      limit: parseInt(dataset.limit) || 5000,
      connector: connector,
    });

    // Transform data if needed
    const transformedData = queryResult.results.map(item => ({
      ...item,
      _synced_at: new Date().toISOString(),
    }));
    await upsertRows(dataset.datasetId, dataset.tableId, dataset.key, transformedData);

    // Update dataset status
    await modifyDoc("datasets", datasetId, {
      isRefreshing: false,
      lastRefreshed: new Date(),
      status: "success",
      errors: null,
    });

    return { success: true, message: "Sync completed successfully", count: transformedData.length };
  } catch (error) {
    console.error("NetSuite Sync Error:", error);

    // Update dataset status with error
    if (data.datasetId) {
      await admin.firestore().collection("datasets").doc(data.datasetId).update({
        isRefreshing: false,
        lastRefreshed: admin.firestore.FieldValue.serverTimestamp(),
        status: "error",
        errors: error.message,
      });
    }

    throw new Error(error.message);
  }
});

exports.updateDataset = functions.https.onCall(async (data, context) => {
  // ... existing code ...

  try {
    // ... existing code ...

    // Handle SuiteQL query
    const operation = SYSTEMS.netsuite.operations.find(op => op.id === "netsuiteSuiteQL");
    if (operation && data.operationId === "netsuiteSuiteQL") {
      const query = data.query;

      // Check if time-based filtering is enabled
      if (data.useTimeFilter && data.timeFilterField && data.timeFilterStartDate && data.timeFilterEndDate) {
        const startDate = new Date(data.timeFilterStartDate);
        const endDate = new Date(data.timeFilterEndDate);
        const intervalHours = data.timeFilterInterval || 2; // Default to 2 hours if not specified

        // Format dates for NetSuite
        const formatDate = (date) => {
          return date.toISOString().replace("T", " ").substring(0, 19);
        };

        // Process data in time intervals
        let allResults = [];
        let currentStartDate = startDate;

        while (currentStartDate < endDate) {
          // Calculate the end of the current interval
          let currentEndDate = new Date(currentStartDate);
          currentEndDate.setHours(currentEndDate.getHours() + intervalHours);

          // If this interval would go beyond the overall end date, cap it
          if (currentEndDate > endDate) {
            currentEndDate = endDate;
          }

          // Add time-based WHERE clause to the query
          const timeFilteredQuery = query.replace(
            /WHERE\s+(.*?)(?:\s+ORDER\s+BY|\s+LIMIT|$)/i,
            `WHERE $1 AND ${data.timeFilterField} >= '${formatDate(currentStartDate)}' AND ${data.timeFilterField} < '${formatDate(currentEndDate)}' $2`
          );

          // Execute the query for this time interval
          const intervalResults = await netsuiteSuiteQlQuery(timeFilteredQuery);
          allResults = allResults.concat(intervalResults);

          // Move to the next interval
          currentStartDate = currentEndDate;

          // Add a small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Update the dataset with all results
        await admin.firestore().collection("datasets").doc(data.id).update({
          lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
          status: "completed",
          results: allResults
        });

        return { success: true, message: "Dataset updated successfully with time-based filtering" };
      } else {
        // Execute the query without time-based filtering (original behavior)
        const results = await netsuiteSuiteQlQuery(query);

        // Update the dataset with the results
        await admin.firestore().collection("datasets").doc(data.id).update({
          lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
          status: "completed",
          results: results
        });

        return { success: true, message: "Dataset updated successfully" };
      }
    }

    // ... existing code ...
  } catch (error) {
    console.error("Error updating dataset:", error);

    // Update the dataset with the error
    await admin.firestore().collection("datasets").doc(data.id).update({
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
      status: "error",
      error: error.message
    });

    throw new functions.https.HttpsError("internal", error.message);
  }
});

const syncShippingExceptions = async () => {
  try {
    const searchUrl = "https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1873&deploy=1&compid=6810379&ns-at=AAEJ7tMQ62yb0CjU7hRc7bEjjwQw9nJDo95m1agKif4C71WED_8&key=Hydr04L1fe!&searchId=103481";
    const response = await axios.get(searchUrl);
    const shippingData = Papa.parse(response.data, { header: true, skipEmptyLines: true, dynamicTyping: true });

    // Get existing shipping statuses
    const existingDataQuery = `SELECT document_number, is_shipped FROM \`hj-reporting.shipping.shipping_exceptions\``;
    const existingData = await runQuery({ query: existingDataQuery });
    const existingStatusMap = new Map(existingData.map(row => [row.document_number, row.is_shipped]));

    // Transform the data to match our schema
    const transformedData = shippingData.data.map(row => ({
      created_from: row["Created From"],
      po_check_number: row["PO/Check Number"],
      document_number: row["Document Number"],
      easypost_package_id: row["Easypost Package ID"],
      carrier: row["Easypost Package Carrier"],
      service_level: row["Easypost Package Service Level"],
      tracking_number: row["Easypost Package Tracking Number"],
      status: row["Easypost Package Status"],
      internal_id: row["Internal ID"],
      batch_number: row["Batch #"],
      is_shipped: existingStatusMap.get(row["Document Number"]) || false, // Preserve existing status or default to false
      // Add a unique identifier that will be used for upsert
      unique_id: row["Easypost Package ID"] || row["Document Number"] // Use package ID if available, otherwise use document number
    }));

    // First, delete existing records
    const deleteQuery = `
      DELETE FROM \`hj-reporting.shipping.shipping_exceptions\`
      WHERE unique_id IN (${transformedData.map(row => `'${row.unique_id}'`).join(",")})
    `;
    await runQuery({ query: deleteQuery });

    // Then insert all records
    await upsertRows("shipping", "shipping_exceptions", "unique_id", transformedData);

    // console.log(`Successfully synced ${transformedData.length} shipping records`);
    return { success: true, count: transformedData.length };
  } catch (error) {
    console.error("Error syncing shipping data:", error);
    throw error;
  }
};

exports.syncShippingExceptionsOnSchedule = onSchedule({
  schedule: "0 7 * * *", // Run at 7AM every day
  timeZone: "America/New_York",
  memory: "1GiB",
  timeoutSeconds: 3600,
}, async (context) => {
  try {
    return await syncShippingExceptions();
  } catch (error) {
    console.error("Error syncing shipping exceptions:", error);
    throw error;
  }
});

exports.syncShippingExceptionsOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async (data, context) => {
  try {
    return await syncShippingExceptions();
  } catch (error) {
    console.error("Error syncing shipping exceptions:", error);
    throw error;
  }
});

exports.saveJsonFileOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async (req, context) => {
  let { fileName, data, prefix } = req.data;
  const bucketName = "hj-reporting.firebasestorage.app";
  const csvData = convertJsonToCsv(data);
  fileName = fileName.replace(".json", ".csv");
  // console.log("saving file to cloud storage", fileName, csvData);
  const newFile = await saveToCloudStorage(bucketName, prefix + fileName, csvData);
  return { success: true, message: "File saved successfully", newFile };
});

exports.deleteFilesOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async (req, context) => {
  const { dates, prefix } = req.data;

  if (!Array.isArray(dates) || dates.length === 0) {
    throw new Error("Missing or invalid 'dates' array.");
  }

  const bucketName = "hj-reporting.firebasestorage.app";

  try {
    // Use the existing function to get all CSV files
    const csvFiles = await storageGetCsvFiles({ bucketName, prefix });

    // Filter files that contain any of the provided dates
    const filesToDelete = csvFiles.filter(file =>
      dates.some(date => file.name.includes(date))
    );

    // console.log("filesToDelete", filesToDelete);

    const deletePromises = filesToDelete.map(file => file.delete());
    await Promise.all(deletePromises);

    return {
      success: true,
      message: `${filesToDelete.length} CSV file(s) deleted matching the given dates.`,
    };
  } catch (error) {
    console.error("Error deleting files:", error);
    return {
      success: false,
      message: "Failed to delete files.",
      error: error.message,
    };
  }
});

const convertJsonToCsv = (dataArray) => {
  // dataArray should be an array of objects
  const sanitized = dataArray.map(row => {
    const newRow = {};
    for (const key in row) {
      newRow[key] = row[key] === null ? "" : row[key];
    }
    return newRow;
  });
  return Papa.unparse(sanitized);
};

const formatISOToDateTime = (isoString) => {
  const date = new Date(isoString);

  const pad = (num) => String(num).padStart(2, "0");

  // Format as YYYY-MM-DD HH24:MI:SS in UTC
  return `${date.getUTCFullYear()}-${pad(date.getUTCMonth() + 1)}-${pad(date.getUTCDate())} ` +
    `${pad(date.getUTCHours())}:${pad(date.getUTCMinutes())}:${pad(date.getUTCSeconds())}`;
};

exports.bigQueryReplaceTableOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async ({ data, context }) => {
  console.log("bigQueryReplaceTableOnCall", data);
  const { datasetId, tableId, rows } = data;
  if (!datasetId || !tableId || !rows) {
    throw new Error("Missing required parameters: datasetId, tableId, or rows");
  }
  try {
    // The bigQueryReplaceTable function now handles testing dataset logic internally
    await bigQueryReplaceTable(datasetId, tableId, rows);
    return { success: true };
  } catch (error) {
    console.error("Error in bigQueryReplaceTableOnCall:", error);
    return { success: false, error: error.message };
  }
});

exports.bigQueryLoadToTableOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async ({ data, context }) => {
  const { datasetId, tableId, rows, replace } = data;
  if (!datasetId || !tableId || !rows) {
    throw new Error("Missing required parameters: datasetId, tableId, or rows");
  }
  try {
    // The bigQueryLoadToTable function now handles testing dataset logic internally
    await bigQueryLoadToTable({ datasetId, tableId, rows, replace });
    return { success: true };
  } catch (error) {
    console.error("Error in bigQueryLoadToTableOnCall:", error);
    return { success: false, error: error.message, stack: error.stack };
  }
});


exports.downloadFileFromUrlToGcsOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "2GiB",
}, async (req, context) => {
  const { files, folderPath } = req.data;
  // const fileProms = [];
  for (const f of files) {
    const bucketName = "hj-reporting.firebasestorage.app";
    //fileProms.push(saveToCloudStorage(bucketName, "test/" + f.name, null, null, null, f.url));
    await saveToCloudStorage(bucketName, folderPath + f.name, null, null, null, f.url);
  }
  // await Promise.all(fileProms);
  // console.log("files saved successfully and dataset updated", files);
  return { success: true, message: "Files saved successfully", files };
});

exports.uploadCsvFromGcsToBigQueryOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "2GiB",
}, async (req, context) => {
  const { datasetId, tableId, prefix } = req.data;

  await bigQueryUploadCsvFiles({ datasetId, tableId, bucketName: "hj-reporting.firebasestorage.app", prefix });
  // console.log("uploadResult", uploadResult);
  return { success: true, message: "Files loaded successfully" };
});


exports.generateDemandPlanOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async (req, context) => {
  try {
    let { rowData, nodeList } = req.data;
    if (!rowData || !nodeList || rowData.length === 0 || nodeList.length === 0) {
      return { success: false, message: "Missing required parameters: rowData or nodeList" };
    }
    // console.log("rowData", rowData.filter(row => row.upc == "840494400470"));
    rowData = rowData.filter(row => row.hasDemandPlan !== "Yes");
    if (rowData.length === 0) {
      return { success: true, message: "No rows need to be added to demand plan" };
    }
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();
    const monthNames = [
      "Jan", "Feb", "Mar", "Apr", "May", "Jun",
      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    ];
    const next12Months = Array.from({ length: 12 }, (_, i) => {
      const monthIndex = (currentMonth + i) % 12;
      const year = currentYear + Math.floor((currentMonth + i) / 12);
      return `${monthNames[monthIndex]} ${year}`;
    });
    // Helper: check if date is before or within next 12 months
    const isLaunchDateValid = (launch, end) => {
      let hasValidDates = false;
      const forecastStart = new Date();
      forecastStart.setMonth(currentMonth);
      forecastStart.setDate(1);
      if (end) {
        const endDate = new Date(end);
        if (!isNaN(endDate)) {
          hasValidDates = endDate >= forecastStart;
        }
      }
      return hasValidDates;
    };
    // Helper: check lifestatus
    const isLifeStatusValid = (lifestatus) => {
      if (!lifestatus) return true;
      const status = String(lifestatus).toLowerCase();
      return status !== "obsolete" && status !== "phasing out";
    };
    // // Get current demand plan rows
    // const currentRows = await runQuery({
    //   query: `
    //       SELECT
    //         FORMAT_DATE('%Y-%m', forecast.date) AS month,
    //         forecast.forecast_node,
    //         forecast.division,
    //         forecast.class,
    //         forecast.region,
    //         forecast.channel,
    //         forecast.customer,
    //         items.producttype as productType,
    //         items.color ,
    //         items.size,
    //         items.upc,
    //         forecast.qty
    //       FROM \`hj-reporting.forecast.demand_plan\` as forecast
    //       LEFT JOIN \`hj-reporting.items.items_netsuite\` as items ON forecast.upc = items.upc
    //   `
    // });
    // Build rows
    const newDemandPlanRows = [];
    // console.log('rowData', rowData.length);
    for (const row of rowData) {
      if (!row.upc) continue;
      if (!row.launchdate) continue;
      if (!isLifeStatusValid(row.lifestatus)) continue;
      if (row.upc == "840494400524") {
        console.log("row", row);
      }
      // Already filtered for inactive in fetch
      for (const node of nodeList) {
        const launch = row[node.code];
        if (!launch) continue;
        if (!isLaunchDateValid(launch, row.enddate)) continue;
        // Build demand plan row
        const region = node.region_name;
        const division = node.division_name;
        const classObj = node.class_name;
        const channel = node.channel_name;
        const customer = node.customer_name;
        const newRow = {
          region,
          division,
          class: classObj,
          channel,
          customer,
          upc: row.upc,
          productType: row.producttype || "",
          color: row.color || "",
          size: row.size || "",
          created_at: new Date().toISOString(),
          locked: false,
          forecast_node: node.code,
        };
        next12Months.forEach(month => {
          newDemandPlanRows.push({ ...newRow, date: new Date(month).toISOString().split("T")[0], qty: 0, modified_at: new Date().toISOString() });
        });
      }
    }
    // console.log('newDemandPlanRows', newDemandPlanRows.length);
    if (newDemandPlanRows.length === 0) {
      return { success: true, message: "No valid demand plan rows could be generated from the matrix." };
    }
    const result = await bigQueryLoadToTable({
      datasetId: "forecast",
      tableId: "demand_plan",
      rows: newDemandPlanRows,
      replace: false,
    });
    // console.log("result", result);
    if (result) {
      return { success: true, message: "Demand plan rows generated and saved to BigQuery.", count: rowData.length };
    } else {
      console.error("Failed to save data.", result);
      return { success: false, message: "Failed to save data." };
    }
  } catch (error) {
    console.error("Failed to generate demand plan: " + error.message);
    return { success: false, message: "Failed to generate demand plan: " + error.message + " " + error.stack };
  }
});

// New callable to process add-only demand plan changes server-side.
// Accepts an array of change objects: { upc, forecast_node, action }
// Only processes items with action === 'add'. Generates daily rows for the next 12 months
// and appends only rows that don't already exist in the demand_plan table. It will NOT
// delete or overwrite any existing rows.
exports.applyDemandPlanAddsOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async (req, context) => {
  try {
    const { changes } = req.data || {};
    if (!changes || !Array.isArray(changes) || changes.length === 0) {
      return { success: true, message: "No changes provided" };
    }
    const items = await runQuery({ query: "SELECT upc, launchdate, enddate FROM `hj-reporting.items.items_netsuite`" });
    const itemsMap = new Map();
    items.forEach(i => itemsMap.set(i.upc, i));

    // Separate add and remove actions and deduplicate
    const addMap = new Map();
    const removeMap = new Map();
    for (const c of changes) {
      if (!c) continue;
      const upc = String(c.upc || "").trim();
      const node = String(c.forecast_node || c.forecastNode || "").trim();
      const action = String(c.action || "").trim().toLowerCase();
      if (!upc || !node || !action) continue;
      const key = `${upc}___${node}`;
      if (action === "add") {
        addMap.set(key, { upc, forecast_node: node });
      } else if (action === "remove") {
        removeMap.set(key, { upc, forecast_node: node });
      }
    }

    const adds = Array.from(addMap.values());
    const removes = Array.from(removeMap.values());

    // Prepare forecast node metadata map
    const forecastNodeRows = await runQuery({ query: "SELECT code, region_name, channel_name, division_name, class_name, customer_name FROM `hj-reporting.forecast.forecast_nodes`" });
    const forecastNodeMap = new Map();
    forecastNodeRows.forEach(r => forecastNodeMap.set(`${r.code}`, r));

    const start = new Date();
    const startDateStr = start.toISOString().split("T")[0];
    const end = new Date(start);
    end.setMonth(end.getMonth() + 12);
    const endDateStr = end.toISOString().split("T")[0];

    // If there are remove actions, perform a delete of all dates for those upc/node combos
    let deleteResult = null;
    if (removes.length > 0) {
      const combinations = removes.map(r => ({ upc: r.upc, forecast_node: r.forecast_node }));
      const deleteQuery = `DELETE FROM \`hj-reporting.forecast.demand_plan\` WHERE (upc, forecast_node) IN UNNEST(@combinations)`;
      try {
        deleteResult = await runQuery({ query: deleteQuery, params: { combinations } });
      } catch (err) {
        console.error("Error deleting demand_plan rows for removals:", err);
        // continue, but report error
      }
    }

    // Process adds: dedupe against existing rows in date range and append only missing daily rows
    const resultSummary = { addedRows: 0, removedPairs: removes.length };
    const promises = [];
    if (adds.length > 0) {
      // Query existing rows for these upc/node combos in the date range
      const existingQuery = `SELECT upc, forecast_node, FORMAT_DATE('%Y-%m-%d', date) AS date FROM \`hj-reporting.forecast.demand_plan\` WHERE date BETWEEN @startDate AND @endDate`;
      const existingRows = await runQuery({ query: existingQuery, params: { startDate: startDateStr, endDate: endDateStr } });
      const existingSet = new Set();
      (existingRows || []).forEach(r => existingSet.add(`${r.upc}_${r.forecast_node}_${r.date}`));

      const rowsToAdd = [];
      const dates = [];
      const cur = new Date(start);
      while (cur <= end) {
        dates.push(cur.toISOString().split("T")[0]);
        cur.setDate(cur.getDate() + 1);
      }

      for (const p of adds) {
        const upc = p.upc;
        const forecast_node = p.forecast_node;
        const nodeMeta = forecastNodeMap.get(forecast_node) || {};
        for (const date of dates) {
          const key = `${upc}_${forecast_node}_${date}`;
          if (existingSet.has(key)) continue;
          //make sure the date is a valid date between item's launchdate and enddate
          const item = itemsMap.get(upc);
          if (!item) continue;
          if (date < item.launchdate || date > item.enddate) continue;
          rowsToAdd.push({
            date,
            division: nodeMeta.division_name,
            class: nodeMeta.class_name,
            region: nodeMeta.region_name,
            channel: nodeMeta.channel_name,
            customer: nodeMeta.customer_name,
            upc,
            created_at: new Date().toISOString(),
            modified_at: new Date().toISOString(),
            locked: false,
            qty: 0,
            forecast_node,
            forecast_method: null,
            proxy_item: null,
            forecast_source: null
          });
        }
      }

      if (rowsToAdd.length > 0) {
        await bigQueryAppendRowsWithLoadJob("forecast", "demand_plan", rowsToAdd);
      }
    }
    await Promise.all(promises);

    return {
      success: true,
      message: `Processed changes: added ${resultSummary.addedRows} rows, removed ${resultSummary.removedPairs} UPC/node pairs`,
      details: { addedRows: resultSummary.addedRows, removedPairs: resultSummary.removedPairs, deleteResult }
    };
  } catch (error) {
    console.error("Error in applyDemandPlanAddsOnCall:", error);
    return { success: false, message: error.message || String(error) };
  }
});

exports.recalculateItemClassificationOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "2GiB",
}, async (req, context) => {
  try {
    console.log("Recalculating item classification...");

    // Query to calculate item classifications based on the updated demand plan
    const response = await runQuery({
      query: `
        WITH
          items AS (
          SELECT
            producttype,
            color,
            size,
            baseprice,
            launchdate,
            enddate,
            productdivision,
            productcategory,
            productform,
            productspecification,
            lifestatus,
            image,
            upc
          FROM
            \`hj-reporting.items.items_netsuite\`
          WHERE
            upc IS NOT NULL
            AND upc != 'TRUE'
          ),
          demand AS (
          SELECT
            upc,
            forecast_node,
            date,
            qty
          FROM
            \`hj-reporting.forecast.demand_plan\`
          WHERE
            date BETWEEN CURRENT_DATE()
            AND DATE_ADD(CURRENT_DATE(), INTERVAL 90 DAY)
          ),
          forecast_nodes AS (
          SELECT
            code,
            COALESCE(CAST(msrp_discount AS FLOAT64), 0) / 100 AS discount_factor
          FROM
            \`hj-reporting.forecast.forecast_nodes\`
          ),
          aggregated_demand AS (
          SELECT
            d.upc,
            i.producttype, i.color, i.size, i.baseprice, i.launchdate, i.enddate,
            i.productdivision, i.productcategory, i.productform, i.productspecification,
            i.lifestatus, i.image,
            SUM(d.qty) AS total_demand_90_days,
            SUM(IF(d.date <= DATE_ADD(CURRENT_DATE(), INTERVAL 60 DAY), d.qty, 0)) AS total_demand_60_days,
            SUM(IF(d.date <= DATE_ADD(CURRENT_DATE(), INTERVAL 30 DAY), d.qty, 0)) AS total_demand_30_days,
            SUM(d.qty * i.baseprice * COALESCE(fn.discount_factor, 1.0)) AS total_forecasted_revenue
          FROM demand d
          JOIN items i ON d.upc = i.upc
          LEFT JOIN forecast_nodes fn ON d.forecast_node = fn.code
          GROUP BY
            d.upc, i.producttype, i.color, i.size, i.baseprice, i.launchdate, i.enddate,
            i.productdivision, i.productcategory, i.productform, i.productspecification,
            i.lifestatus, i.image
          ),
          final AS (
          SELECT
            *
          FROM
            aggregated_demand
          ),
          scored AS (
          SELECT
            *,
            ROUND(CAST(100*total_demand_90_days AS FLOAT64) / SUM(total_demand_90_days) OVER (), 2) AS percent_demand,
            ROUND(CAST(100*total_forecasted_revenue AS FLOAT64) / SUM(total_forecasted_revenue) OVER (), 2) AS percent_revenue
          FROM
            final
          ),
          ranked AS (
          SELECT
            *,
            CASE
              WHEN total_demand_90_days IS NULL OR total_demand_90_days = 0 
                   OR lifestatus IN ('Phasing Out', 'Obsolete')
                   OR productform IN ('Limited Edition', 'Exclusive') THEN NULL
              ELSE
                CAST(ROW_NUMBER() OVER (
                  PARTITION BY 
                    CASE 
                      WHEN total_demand_90_days IS NOT NULL AND total_demand_90_days > 0 
                           AND lifestatus NOT IN ('Phasing Out', 'Obsolete')
                           AND productform NOT IN ('Limited Edition', 'Exclusive') 
                      THEN 1 
                      ELSE 0 
                    END
                  ORDER BY total_demand_90_days DESC
                ) AS FLOAT64) / 
                CAST(COUNT(*) OVER (
                  PARTITION BY 
                    CASE 
                      WHEN total_demand_90_days IS NOT NULL AND total_demand_90_days > 0 
                           AND lifestatus NOT IN ('Phasing Out', 'Obsolete')
                           AND productform NOT IN ('Limited Edition', 'Exclusive') 
                      THEN 1 
                      ELSE 0 
                    END
                ) AS FLOAT64)
            END AS percentile_rank
          FROM
            scored
          ),
          classified AS (
          SELECT
            *,
            CASE
              WHEN total_demand_90_days IS NULL OR total_demand_90_days = 0 OR lifestatus IN ('Phasing Out', 'Obsolete') OR productform IN ('Limited Edition', 'Exclusive') THEN 'No Class'
              WHEN ROUND(SUM(total_forecasted_revenue) OVER (ORDER BY total_forecasted_revenue DESC) / SUM(total_forecasted_revenue) OVER () * 100, 2) <= 80 THEN 'A'
              WHEN ROUND(SUM(total_forecasted_revenue) OVER (ORDER BY total_forecasted_revenue DESC) / SUM(total_forecasted_revenue) OVER () * 100, 2) <= 95 THEN 'B'
              ELSE 'C'
            END AS classification
          FROM
            ranked
          )
        SELECT
          upc,
          percent_revenue,
          ROUND(SUM(percent_revenue) OVER (ORDER BY percent_revenue DESC), 2) AS percent_total_revenue,
          classification,
          CASE
            WHEN total_demand_90_days IS NULL OR total_demand_90_days = 0 THEN 0
            WHEN classification = 'A' THEN ROUND(total_demand_90_days, 0)
            WHEN classification = 'B' THEN ROUND(total_demand_60_days, 0)
            WHEN classification = 'C' THEN ROUND(total_demand_30_days, 0)
            ELSE 0
          END AS safety_stock,
          CASE
            WHEN total_demand_90_days IS NULL OR total_demand_90_days = 0 THEN 0
            WHEN classification = 'A' THEN ROUND(total_demand_90_days * 0.75, 0)
            WHEN classification = 'B' THEN ROUND(total_demand_60_days * 0.75, 0)
            WHEN classification = 'C' THEN ROUND(total_demand_30_days * 0.75, 0)
            ELSE 0
          END AS min_stock,
          CASE
            WHEN total_demand_90_days IS NULL OR total_demand_90_days = 0 THEN 0
            WHEN classification = 'A' THEN ROUND(total_demand_90_days * 1.25, 0)
            WHEN classification = 'B' THEN ROUND(total_demand_60_days * 1.25, 0)
            WHEN classification = 'C' THEN ROUND(total_demand_30_days * 1.25, 0)
            ELSE 0
          END AS max_stock,
          classification as calculated_classification
        FROM
          classified
        WHERE
          upc IS NOT NULL
        ORDER BY
          total_forecasted_revenue DESC
      `
    });

    const classificationRows = response.map(row => ({
      upc: row.upc,
      percent_revenue: row.percent_revenue || 0,
      percent_total_revenue: row.percent_total_revenue || 0,
      classification: row.classification || "",
      safety_stock: row.safety_stock || 0,
      min_stock: row.min_stock || 0,
      max_stock: row.max_stock || 0,
      modified_at: new Date().toISOString(),
      locked: false,
      calculated_classification: row.calculated_classification || ""
    }));

    console.log("Saving item classification to BigQuery:", classificationRows.length, "rows");
    console.log("Sample classification rows:", classificationRows.slice(0, 3));

    // Save to BigQuery using the same pattern as other functions
    const classificationResult = await bigQueryLoadToTable({ datasetId: "forecast", tableId: "item_classification", rows: classificationRows, replace: true });
    console.log("classificationResult", classificationResult);
    if (classificationResult) {
      console.log("Item classification recalculated and saved successfully!");
      return {
        success: true,
        message: `Item classification updated for ${classificationRows.length} items`,
        count: classificationRows.length
      };
    } else {
      console.error("Failed to save item classification:", classificationResult);
      return {
        success: false,
        message: "Item classification calculation completed but saving to BigQuery failed"
      };
    }
  } catch (error) {
    console.error("Error recalculating item classification:", error);
    return {
      success: false,
      message: "Failed to recalculate item classification: " + error.message,
      error: error.message
    };
  }
});

exports.recalculateInventoryExceptionsOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "2GiB",
}, async (req, context) => {
  try {
    console.log("Recalculating inventory exceptions...");
    const forecastNodesResponse = await runQuery({
      query: `SELECT code FROM \`hj-reporting.forecast.forecast_nodes\``
    });

    console.log("Forecast nodes:", forecastNodesResponse);

    const seasonalForecastNodes = forecastNodesResponse.data || []; // Step 1: Fetch base data in parallel
    const nonSeasonalForecastNodes = forecastNodesResponse.data.filter(node => node.sales_predictor_method !== "seasonal");
    const [items, demandPlan, inboundData, openOrders] = await Promise.all([
      // Items from BigQuery
      runQuery({ query: "SELECT * FROM `hj-reporting.items.items_netsuite` WHERE lifestatus != 'Obsolete'" }),
      // Demand plan from BigQuery
      runQuery({ query: `SELECT upc, forecast_node, date, SUM(qty) AS qty FROM \`hj-reporting.forecast.demand_plan\` WHERE date >= DATE_SUB(CURRENT_DATE(), INTERVAL 365 DAY) AND forecast_node IN (${seasonalForecastNodes.map(node => `'${node.code}'`).join(",")}) GROUP BY upc, forecast_node, date` }),
      // Inbound data from NetSuite
      getInboundData(),
      // Open orders from BigQuery
      runQuery({
        query: `
          SELECT
            date,
            status,
            document_number,
            location,
            class,
            channel,
            cust.name AS customer,
            po_check_number AS po_number,
            i.upc,
            quantity * -1 AS qty_ordered,
            amount / quantity AS rate,
            amount * -1 AS amount,
            quantity_committed AS qty_committed,
            quantity_shipped_received AS qty_shipped,
            (quantity * -1 - quantity_shipped_received) AS qty_open,
            orginal_dispatch_date,
            prep_date,
            start_date,
            dispatch_date
          FROM
            \`hj-reporting.transactions.transactions_netsuite\` AS t
            LEFT JOIN \`hj-reporting.items.items_netsuite\` AS i ON i.upc = t.upc
            LEFT JOIN \`hj-reporting.customers.customers_netsuite\` AS cust ON cust.internal_id = t.customer_id
          WHERE
            date >= DATE_SUB(CURRENT_DATE(), INTERVAL 180 DAY)
            AND forecast_node IN (${nonSeasonalForecastNodes.map(node => `'${node.code}'`).join(",")})
            AND status NOT IN (
              'Sales Order : Billed',
              'Sales Order : Closed',
              'Sales Order : Cancelled',
              'Sales Order : Pending Billing',
              'Sales Order : Cancelled'
            )
            AND i.upc IS NOT NULL
            AND type = 'SalesOrd'
            AND is_closed = FALSE
            AND (quantity * -1 - quantity_shipped_received) > 0
          ORDER BY
            document_number,
            line_id;
        `
      })
    ]);

    console.log(`Fetched ${items.length} items, ${demandPlan.length} demand plan entries, ${inboundData.length} inbound entries, ${openOrders.length} open orders`);

    // Step 2: Generate 120-day date range
    const nextDays = [];
    for (let i = 0; i < 90; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      nextDays.push(date.toISOString().split("T")[0]);
    }

    // Step 3: Build efficient lookup maps
    const inboundsByUpcDate = {};
    inboundData.forEach(inbound => {
      if (inbound.upc && inbound.expectedDate) {
        const dateKey = inbound.expectedDate.toISOString().split("T")[0];
        if (!inboundsByUpcDate[inbound.upc]) {
          inboundsByUpcDate[inbound.upc] = {};
        }
        if (!inboundsByUpcDate[inbound.upc][dateKey]) {
          inboundsByUpcDate[inbound.upc][dateKey] = [];
        }
        inboundsByUpcDate[inbound.upc][dateKey].push(inbound);
      }
    });

    const demandByUpcDate = {};
    demandPlan.forEach(demand => {
      if (demand.upc && demand.date && demand.date.value) {
        const dateKey = demand.date.value;
        if (!demandByUpcDate[demand.upc]) {
          demandByUpcDate[demand.upc] = {};
        }
        if (!demandByUpcDate[demand.upc][dateKey]) {
          demandByUpcDate[demand.upc][dateKey] = [];
        }
        demandByUpcDate[demand.upc][dateKey].push(demand);
      }
    });

    const ordersByUpcDate = {};
    openOrders.forEach(order => {
      if (!order.dispatch_date) {
        order.dispatch_date = order.date;
      }
      if (order.upc) {
        const dateKey = order.dispatch_date.value;
        if (!ordersByUpcDate[order.upc]) {
          ordersByUpcDate[order.upc] = {};
        }
        if (!ordersByUpcDate[order.upc][dateKey]) {
          ordersByUpcDate[order.upc][dateKey] = [];
        }
        ordersByUpcDate[order.upc][dateKey].push(order);
      }
    });

    // Step 4: Calculate inventory for each item across 120 days
    const inventoryResults = [];

    for (const item of items) {
      if (!item.upc) continue;

      let currentOnHand = item.on_hand || 0;

      for (let dayIndex = 0; dayIndex < 120; dayIndex++) {
        const currentDate = nextDays[dayIndex];
        // Get inbounds for this day
        const dayInbounds = (inboundsByUpcDate[item.upc] && inboundsByUpcDate[item.upc][currentDate]) || [];
        const inbound_qty = dayInbounds.reduce((sum, inbound) => sum + (parseInt(inbound.qty) || 0), 0);

        // Get demand plan for this day
        const dayDemand = (demandByUpcDate[item.upc] && demandByUpcDate[item.upc][currentDate]) || [];
        const outbound_qty = dayDemand.reduce((sum, demand) => sum + (parseInt(demand.qty) || 0), 0);

        // Get open orders for this day
        const dayOrders = (ordersByUpcDate[item.upc] && ordersByUpcDate[item.upc][currentDate]) || [];
        const needs_approval = dayOrders.some(order => order.status && order.status.toLowerCase().includes("pending approval"));
        const needs_allocated = dayOrders.some(order => !order.order_allocation || order.order_allocation === "do not allocate");

        // Calculate inventory levels
        const starting_oh_qty = currentOnHand;
        const ending_oh_qty = starting_oh_qty + inbound_qty - outbound_qty;

        // Create inventory record
        inventoryResults.push({
          location: "HQ",
          upc: item.upc,
          product: item.producttype || "",
          date: currentDate,
          starting_oh_qty,
          inbound_qty,
          outbound_qty,
          ending_oh_qty,
          inbound_details: JSON.stringify(dayInbounds),
          outbound_details: JSON.stringify(dayDemand),
          needs_approval,
          needs_allocated,
          // Include other item fields
          lifestatus: item.lifestatus,
          color: item.color,
          size: item.size,
          baseprice: item.baseprice,
          launchdate: item.launchdate,
          enddate: item.enddate
        });

        // Update current on hand for next day
        currentOnHand = ending_oh_qty;
      }
    }

    console.log(`Generated ${inventoryResults.length} inventory records`);

    // Step 5: Upload to BigQuery
    await bigQueryLoadToTable({
      datasetId: "forecast",
      tableId: "inventory_exceptions",
      rows: inventoryResults,
      replace: true
    });

    // Step 6: Return success
    return {
      success: true,
      count: inventoryResults.length,
      items: items.length,
      days: 120
    };
  } catch (error) {
    console.error("Error recalculating inventory exceptions:", {
      stack: error.stack,
      message: error.message,
    });
    // Notify via Google Chat
    await sendGChatMessage("ERRORS", `Inventory Exceptions Error: ${error.message}`);
    return { success: false, message: error.message };
  }
});

const getInventoryExceptions = async () => {
  try {
    const demandPlanQuery = `SELECT forecast_node,upc,qty,date FROM \`hj-reporting.forecast.demand_plan\` WHERE date>=DATE_TRUNC(CURRENT_DATE(), MONTH) AND date<=DATE_ADD(CURRENT_DATE(), INTERVAL 90 DAY) ORDER BY upc, date`;
    const inboundQuery = `
          WITH inbound_keys AS (
            SELECT DISTINCT
              CAST(i.po_line_id AS STRING) AS po_line_id
            FROM
              \`hj-reporting.transactions.inbounds_netsuite\` i
            WHERE
              i.status IN ('toBeShipped', 'inTransit', 'partiallyReceived')
              AND i.po_line_id IS NOT NULL
          ),
          po AS (
            SELECT
              'Purchase Order' AS type,
              t.internal_id as id,
              t.document_number AS tranId,
              CAST(NULL AS STRING) AS container,
              t.upc,
              (t.quantity - t.quantity_shipped_received) AS qty,
              t.expected_xfd AS expected_arrival,
              CAST(t.line_unique_key AS STRING) AS line_unique_key
            FROM
              \`hj-reporting.transactions.transactions_netsuite\` t
              LEFT JOIN \`hj-reporting.items.items_netsuite\` i ON t.upc = i.upc
            WHERE
              t.date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
              AND t.type = 'PurchOrd'
              AND t.is_closed = FALSE
              AND (t.quantity - t.quantity_shipped_received) > 0
              AND t.quantity > 0
              AND t.quantity_shipped_received IS NOT NULL
              AND CAST(t.line_unique_key AS STRING) NOT IN (SELECT po_line_id FROM inbound_keys)
          ),
          inb AS (
            SELECT
              'Inbound' AS type,
              i.internal_id as id,
              i.shipment_number AS tranId,
              i.vessel_number AS container,
              i.upc,
              i.items_quantity_expected-i.items_quantity_received AS qty,
              i.expected_delivery_date AS expected_arrival
            FROM
              \`hj-reporting.transactions.inbounds_netsuite\` i
            WHERE
              i.status IN ('toBeShipped', 'inTransit', 'partiallyReceived')
              and i.items_quantity_expected-i.items_quantity_received >0
              and i.items_receiving_location='HQ'
          )
          SELECT type, id, tranId, container, upc, qty, expected_arrival
          FROM inb
          WHERE qty>0
          UNION ALL
          SELECT type, id, tranId, container, upc, qty, expected_arrival
          FROM po
          WHERE qty > 0

      ORDER BY expected_arrival DESC, upc;
    `;
    const dataProms = [];
    dataProms.push(runQuery({ query: `SELECT i.internalid as item_id, i.baseprice as msrp, i.avgcost, i.upc, i.reserveinventoryqty as reserve_qty, i.producttype as product_type, i.lifestatus as life_status, i.color, i.size, li.on_hand, li.on_order, li.available, i.productdivision,i.productspecification, i.launchdate, i.enddate FROM \`hj-reporting.items.items_netsuite\` AS i left join \`hj-reporting.inventory.live_inventory\` AS li on i.upc = li.upc WHERE li.location = 'HQ'` }));
    dataProms.push(runQuery({ query: demandPlanQuery }));
    dataProms.push(runQuery({ query: inboundQuery }));
    dataProms.push(runQuery({ query: `SELECT upc,classification,min_stock,max_stock,safety_stock FROM \`hj-reporting.forecast.item_classification\`` }));
    dataProms.push(runQuery({ query: `SELECT upc, ROUND(SUM(quantity) / -90.0, 0) AS avg90 FROM \`hj-reporting.transactions.transactions_netsuite\` AS t WHERE Date >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY) AND class != 'Wholesale' AND account = '13010 Inventory Asset' AND is_posting = TRUE AND type NOT IN ('InvAdjst', 'InvTrnfr', 'ItemRcpt', 'InvWksht') AND upc IS NOT NULL AND created_from NOT LIKE 'TO%' GROUP BY upc` }));
    const [items, demandPlan, inbounds, itemClassification, salesData] = await Promise.all(dataProms);
    console.log("items", items.length);
    console.log("inbounds", inbounds.length);
    console.log("demandPlan", demandPlan.length);
    const itemsData = [];
    const currentDate = new Date();
    const nextNDays = [];
    for (let i = 0; i < 90; i++) {
      const date = new Date();
      date.setDate(currentDate.getDate() + i);
      nextNDays.push(date.toISOString().split("T")[0]);
    }
    const itemClassificationMap = itemClassification.reduce((acc, item) => {
      acc[item.upc] = item;
      return acc;
    }, {});
    const salesDataMap = salesData.reduce((acc, item) => {
      acc[item.upc] = item;
      return acc;
    }, {});
    for (const item of items) {
      let currentQty = item.on_hand || 0;
      const productType = item.product_type;
      const productLabel = `${item.color} ${item.size}`.trim();
      const productRow = {
        path: [productType],
        id: productType,
        type: "product_type",
        product: productType,
      };

      const mainRow = {
        path: [productType, productLabel],
        id: productLabel,
        item_id: item.item_id,
        type: "main",
        product: productType,
        upc: item.upc,
        life_status: item.life_status,
        product_type: productType,
        division: item.productdivision,
        specification: item.productspecification,
        color: item.color,
        size: item.size,
        reserve_qty: item.reserve_qty || 0,
        on_hand: item.on_hand || 0,
        revenue_potential: (parseFloat(item.msrp || 0) * (item.on_hand || 0)),
        on_order: item.on_order || 0,
        available: item.available || 0,
        launch_date: item.launchdate,
        end_date: item.enddate,
        classification: itemClassificationMap[item.upc]?.classification || "",
        min_stock: itemClassificationMap[item.upc]?.min_stock || 0,
        max_stock: itemClassificationMap[item.upc]?.max_stock || 0,
        safety_stock: itemClassificationMap[item.upc]?.safety_stock || 0,
        avg90: salesDataMap[item.upc]?.avg90 || 0,
        msrp: parseFloat(item.msrp || 0),
        avgcost: parseFloat(item.avgcost || 0),
        oh_value: parseFloat(item.on_hand || 0) * parseFloat(item.avgcost || 0),
      };
      const outRow = {
        path: [productType, productLabel, "OUT"],
        id: productLabel + "-out",
        product: productType,
        upc: item.upc,
        life_status: item.life_status,
        product_type: productType,
        division: item.productdivision,
        specification: item.productspecification,
        color: item.color,
        reserve_qty: item.reserve_qty || 0,
        on_hand: item.on_hand || 0,
        on_order: item.on_order || 0,
        available: item.available || 0,
        size: item.size,
        classification: itemClassificationMap[item.upc]?.classification || "",
        min_stock: itemClassificationMap[item.upc]?.min_stock || 0,
        max_stock: itemClassificationMap[item.upc]?.max_stock || 0,
        safety_stock: itemClassificationMap[item.upc]?.safety_stock || 0,
        avg90: salesDataMap[item.upc]?.avg90 || 0,
        launch_date: item.launchdate,
        end_date: item.enddate,
        out_of_stock_days: 0,
        next_inbound_date: "",
        total_inbound: 0,
        needs_order: false,
        type: "out",
      };
      const inRow = {
        path: [productType, productLabel, "IN"],
        id: productLabel + "-in",
        product: productType,
        upc: item.upc,
        life_status: item.life_status,
        product_type: productType,
        specification: item.productspecification,
        color: item.color,
        size: item.size,
        division: item.productdivision,
        classification: itemClassificationMap[item.upc]?.classification || "",
        min_stock: itemClassificationMap[item.upc]?.min_stock || 0,
        max_stock: itemClassificationMap[item.upc]?.max_stock || 0,
        reserve_qty: item.reserve_qty || 0,
        on_hand: item.on_hand || 0,
        on_order: item.on_order || 0,
        available: item.available || 0,
        safety_stock: itemClassificationMap[item.upc]?.safety_stock || 0,
        avg90: salesDataMap[item.upc]?.avg90 || 0,
        launch_date: item.launchdate,
        end_date: item.enddate,
        type: "in",
      };
      // Demand plan rows for this item
      let itemDemandPlan = demandPlan.filter(dp => dp.upc === item.upc);

      // Parse launch and end dates for range checks
      const launchDateObj = item.launchdate ? new Date(item.launchdate.value) : null;
      const endDateObj = item.enddate ? new Date(item.enddate.value) : null;

      // If every demand plan record is on the first of the month, split each record evenly across the valid days of that month (between launch and end date, inclusive)
      if (
        itemDemandPlan.length > 0 &&
        itemDemandPlan.every(dp => dp.date.value.split("-")[2] === "01")
      ) {
        const splitDemand = [];
        for (const dp of itemDemandPlan) {
          const monthStart = new Date(dp.date.value);
          // Get year and month for this demand plan record
          const year = monthStart.getFullYear();
          const month = monthStart.getMonth(); // 0-based
          // Get number of days in this month
          const daysInMonth = new Date(year, month + 1, 0).getDate();

          // Determine the valid start and end dates for this month, based on launch and end date
          // The valid start is the later of the launch date or the first day of the month
          // The valid end is the earlier of the end date or the last day of the month
          let validStart = new Date(year, month, 1);
          let validEnd = new Date(year, month, daysInMonth);

          if (launchDateObj && launchDateObj > validStart) {
            validStart = new Date(launchDateObj);
          }
          if (endDateObj && endDateObj < validEnd) {
            validEnd = new Date(endDateObj);
          }

          // If the valid range is outside this month, skip
          if (validEnd < validStart) continue;

          // Build the list of valid dates in this month
          const validDates = [];
          for (
            let d = new Date(validStart);
            d <= validEnd;
            d.setDate(d.getDate() + 1)
          ) {
            // Only include dates within this month
            if (d.getMonth() === month) {
              validDates.push(d.toISOString().split("T")[0]);
            }
          }

          // If there are no valid days in this month, skip
          if (validDates.length === 0) continue;

          // Split qty evenly across valid days, distributing remainder to earliest days
          const qtyPerDay = Math.floor(dp.qty / validDates.length);
          const remainder = dp.qty % validDates.length;

          validDates.forEach((dateStr, idx) => {
            splitDemand.push({
              ...dp,
              date: { ...dp.date, value: dateStr },
              qty: qtyPerDay + (idx < remainder ? 1 : 0),
            });
          });
        }
        itemDemandPlan = splitDemand;
      }
      // After splitting monthly, itemDemandPlan already within valid range

      const itemInbounds = inbounds.filter(inbound => inbound.upc === item.upc);
      let totalDemandForItem = 0;
      let outOfStockDays = 0;
      let nextInboundDate = "";
      let totalInbound = 0;
      let needsOrder = false;
      let daysHeavy = 0;
      let daysLight = 0;
      // Initialize inbound details array for the main row
      mainRow.inbound_details = [];

      for (const date of nextNDays) {
        const dayDemand = itemDemandPlan.filter(dp => dp.date.value === date);
        const dayInbounds = itemInbounds.filter(inbound => inbound.expected_arrival?.value === date);
        mainRow[date] = currentQty;
        let demandQty = 0;
        let inboundQty = 0;
        if (dayInbounds.length > 0) {
          inboundQty = dayInbounds.reduce((sum, inbound) => sum + (parseInt(inbound.qty) || 0), 0);
          mainRow[date] += inboundQty;
          currentQty += inboundQty;

          // Add inbound details for this date
          dayInbounds.forEach(inbound => {
            mainRow.inbound_details.push({
              type: inbound.type,
              id: inbound.id,
              tranId: inbound.tranId,
              container: inbound.container,
              qty: parseInt(inbound.qty) || 0,
              expected_arrival: inbound.expected_arrival
            });
          });
        }
        // Always set inRow[date], even if 0
        inRow[date] = inboundQty;
        if (dayDemand.length > 0) {
          demandQty = dayDemand.reduce((sum, demand) => sum + (parseInt(demand.qty) || 0), 0);
          mainRow[date] -= demandQty;
          currentQty -= demandQty;
        }
        // Always set outRow[date], even if 0
        outRow[date] = demandQty;
        totalDemandForItem += demandQty;
      }

      // Calculate out of stock days in next 90 days
      const calculateOutOfStockDays = (row) => {
        if (row.type !== "main") return 0;

        let outOfStockDays = 0;
        nextNDays.forEach(date => {
          const value = row[date] || 0;
          // Check if the date is within the valid launch/end range
          const cellDay = new Date(date);
          const launchRaw = new Date(row.launch_date?.value ?? row.launch_date ?? "1900-01-01");
          const endRaw = new Date(row.end_date?.value ?? row.end_date ?? "2100-01-01");

          // Only count out of stock days within the valid date range
          if (cellDay >= launchRaw && cellDay <= endRaw && value <= 0) {
            outOfStockDays++;
          }
        });
        return outOfStockDays;
      };

      // Calculate next inbound date
      const calculateNextInboundDate = (row, inRow) => {
        if (row.type !== "main") return "";

        // Look for the first date where there's a positive inbound value
        for (let i = 0; i < nextNDays.length; i++) {
          const date = nextNDays[i];
          const value = inRow[date] || 0;
          if (value > 0) {
            // Format date as MM/DD/YYYY
            const dateObj = new Date(date);
            return `${String(dateObj.getMonth() + 1).padStart(2, "0")}/${String(dateObj.getDate()).padStart(2, "0")}/${dateObj.getFullYear()}`;
          }
        }
        return "";
      };

      // Calculate total inbound in next 90 days
      const calculateTotalInbound = (row, inRow) => {
        if (row.type !== "main") return 0;

        let totalInbound = 0;
        nextNDays.forEach(date => {
          const value = inRow[date] || 0;
          if (value > 0) {
            totalInbound += value;
          }
        });
        return totalInbound;
      };

      // Calculate if needs order (expected inventory at end <= 0)
      const calculateNeedsOrder = (row) => {
        if (row.type !== "main") return false;

        const lastDate = nextNDays[nextNDays.length - 1];
        const endInventory = row[lastDate] || 0;

        // Check if the end date is within the valid launch/end range
        const cellDay = new Date(lastDate);
        const launchRaw = new Date(row.launch_date?.value ?? row.launch_date ?? "1900-01-01");
        const endRaw = new Date(row.end_date?.value ?? row.end_date ?? "2100-01-01");

        // Only consider it needs order if within valid date range and inventory <= 0
        return cellDay >= launchRaw && cellDay <= endRaw && endInventory <= 0;
      };

      // Calculate the days we have more than the max stock
      const calculateDaysHeavy = (row) => {
        if (row.type !== "main") return 0;
        let daysHeavy = 0;
        nextNDays.forEach(date => {
          const value = row[date] || 0;
          // Check if the date is within the valid launch/end range
          const cellDay = new Date(date);
          const launchRaw = new Date(row.launch_date?.value ?? row.launch_date ?? "1900-01-01");
          const endRaw = new Date(row.end_date?.value ?? row.end_date ?? "2100-01-01");

          // Only count out of stock days within the valid date range
          if (cellDay >= launchRaw && cellDay <= endRaw && value > row.max_stock && value > 0) {
            daysHeavy++;
          }
        });
        return daysHeavy;
      };
      // Calculate the days we have less than the min stock
      const calculateDaysLight = (row) => {
        if (row.type !== "main") return 0;
        let daysLight = 0;
        nextNDays.forEach(date => {
          const value = row[date] || 0;
          // Check if the date is within the valid launch/end range
          const cellDay = new Date(date);
          const launchRaw = new Date(row.launch_date?.value ?? row.launch_date ?? "1900-01-01");
          const endRaw = new Date(row.end_date?.value ?? row.end_date ?? "2100-01-01");

          // Only count out of stock days within the valid date range
          if (cellDay >= launchRaw && cellDay <= endRaw && value < row.min_stock && value > 0) {
            daysLight++;
          }
        });
        return daysLight;
      };

      // Calculate the values for the main row
      outOfStockDays = calculateOutOfStockDays(mainRow);
      nextInboundDate = calculateNextInboundDate(mainRow, inRow);
      totalInbound = calculateTotalInbound(mainRow, inRow);
      needsOrder = calculateNeedsOrder(mainRow);
      daysHeavy = calculateDaysHeavy(mainRow);
      daysLight = calculateDaysLight(mainRow);

      // Update the outRow with calculated values
      // outRow.out_of_stock_days = outOfStockDays;
      // outRow.next_inbound_date = nextInboundDate;
      // outRow.total_inbound = totalInbound;
      // outRow.needs_order = needsOrder;

      // Also add these values to the main row for consistency
      mainRow.out_of_stock_days = outOfStockDays;
      mainRow.next_inbound_date = nextInboundDate;
      mainRow.total_inbound = totalInbound;
      mainRow.needs_order = needsOrder;
      mainRow.days_heavy = daysHeavy;
      mainRow.days_light = daysLight;

      // Generate forecast node child rows under OUT
      const nodeTotalsByDate = {};
      itemDemandPlan.forEach(dp => {
        const dateStr = dp.date.value;
        nodeTotalsByDate[dateStr] = nodeTotalsByDate[dateStr] || {};
        nodeTotalsByDate[dateStr][dp.forecast_node] = (nodeTotalsByDate[dateStr][dp.forecast_node] || 0) + dp.qty;
      });
      const uniqueNodes = [...new Set(itemDemandPlan.map(dp => dp.forecast_node))];
      const nodeRows = uniqueNodes.map(node => {
        const nodeRow = {
          path: [item.product_type, productLabel, "OUT", node],
          id: `${item.product_type}--${productLabel}-out-${node}`,
          type: "forecast_node",
          product: item.product_type,
          upc: item.upc,
          life_status: item.life_status,
          product_type: item.product_type,
          division: item.productdivision,
          specification: item.productspecification,
          color: item.color,
          size: item.size,
          classification: itemClassificationMap[item.upc]?.classification || "",
          min_stock: itemClassificationMap[item.upc]?.min_stock || 0,
          max_stock: itemClassificationMap[item.upc]?.max_stock || 0,
          safety_stock: itemClassificationMap[item.upc]?.safety_stock || 0,
          launch_date: item.launchdate,
          end_date: item.enddate,
          days_heavy: daysHeavy,
          days_light: daysLight,
        };
        nextNDays.forEach(dateStr => {
          nodeRow[dateStr] = Math.round(nodeTotalsByDate[dateStr]?.[node] || 0);
        });
        return nodeRow;
      });
      // filter out any forecast_node rows where all date values are zero
      const nonEmptyNodeRows = nodeRows.filter(nodeRow =>
        nextNDays.some(dateStr => nodeRow[dateStr] > 0)
      );
      itemsData.push({
        totalDemand: totalDemandForItem,
        rows: [productRow, mainRow, inRow, outRow, ...nonEmptyNodeRows]
      });
    }
    // Sort items by total demand descending
    itemsData.sort((a, b) => b.totalDemand - a.totalDemand);

    const data = itemsData.flatMap(item => item.rows);
    await modifyDoc("lists", "lastUpdates", { inventoryExceptionReport: new Date() });
    //save
    const bucketName = "hj-reporting.firebasestorage.app";
    const filePath = `forecast/inventory_exception_report/inventory_exception_report.json`;
    await saveToCloudStorage(bucketName, filePath, JSON.stringify(data), "application/json");
    return {
      success: true,
      message: "Inventory exception report saved successfully to firebasestorage.",
      inventoryExceptionReportUrl: `gs://hj-reporting.firebasestorage.app/forecast/inventory_exception_report/inventory_exception_report.json`,
    };
  } catch (error) {
    console.error("Error getting inventory exceptions:", {
      stack: error.stack,
      message: error.message,
    });
    return [];
  }
};

exports.getInventoryExceptionsOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "2GiB",
}, async (req, context) => {
  const response = await getInventoryExceptions();
  return response;
});
exports.getInventoryExceptionsOnSchedule = onSchedule({
  memory: "1GiB",
  schedule: "every 1 hours",
  timeZone: "America/New_York"
}, async (context) => {
  const response = await getInventoryExceptions();
  return response;
});

exports.refreshOpenOrdersOnTask = onCall({
  timeoutSeconds: 3600,
  memory: "2GiB",
}, async (data, context) => {
  const results = await refreshOpenOrders({ filters: {} });
  return results;
});

// Cloud Function to fetch open orders for a given item
exports.refreshOpenOrdersOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "2GiB",
}, async (data, context) => {
  const { filters } = data.data;
  try {
    await modifyDoc("lists", "lastUpdates", { openOrdersRefreshing: true });

    // In testing/emulator environment, bypass Google Cloud Tasks and call function directly
    const isTestingEnvironment = process.env.FUNCTIONS_EMULATOR || process.env.NODE_ENV === "test";
    if (isTestingEnvironment) {
      console.log("Testing environment detected - calling refreshOpenOrders directly (bypassing Google Cloud Tasks)");
      const results = await refreshOpenOrders({ filters: filters || {} });
      return { success: true, results, direct: true };
    } else {
      // Production: Always use Google Cloud Tasks
      await createTask("netsuiteTaskQueue", { data: { filters } }, "https://us-central1-hj-reporting.cloudfunctions.net/refreshOpenOrdersOnTask");
      return { success: true };
    }
  } catch (error) {
    console.error("Error fetching open orders:", error);
    return { error: error.message };
  }
});
exports.refreshOpenOrdersOnSchedule = onSchedule({
  memory: "2GiB",
  timeoutSeconds: 3600,
  schedule: "every 1 hours",
}, async (context) => {
  //trigger a cloud function to refresh the open orders

  // In testing/emulator environment, bypass Google Cloud Tasks and call function directly
  const isTestingEnvironment = process.env.FUNCTIONS_EMULATOR || process.env.NODE_ENV === "test";
  if (isTestingEnvironment) {
    console.log("Testing environment detected - calling refreshOpenOrders directly from schedule (bypassing Google Cloud Tasks)");
    await modifyDoc("lists", "lastUpdates", { openOrdersRefreshing: true });
    const results = await refreshOpenOrders({ filters: {} });
    return { success: true, results, direct: true };
  } else {
    // Production: Always use Google Cloud Tasks (will route to sandboxTaskQueue in testing environments)
    await createTask("netsuiteTaskQueue", { data: { filters: {} } }, "https://us-central1-hj-reporting.cloudfunctions.net/refreshOpenOrdersOnTask");
    return { success: true };
  }
});

// Cloud Function to update allocation strategy for open orders
exports.updateSalesOrderOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async (data, context) => {
  const { updates, taskId } = data.data;
  if (!updates) {
    return { error: "Updates not provided" };
  }
  try {
    const grouped = updates.reduce((acc, u) => {
      acc[u.orderId] = acc[u.orderId] || [];
      acc[u.orderId].push(u);
      return acc;
    }, {});
    // In testing/emulator environment, bypass Google Cloud Tasks and process updates directly
    const isTestingEnvironment = process.env.FUNCTIONS_EMULATOR || process.env.NODE_ENV === "test";
    if (isTestingEnvironment) {
      console.log("Testing environment detected - processing sales order updates directly (bypassing Google Cloud Tasks)");

      const results = [];
      for (const orderId in grouped) {
        if (!Object.prototype.hasOwnProperty.call(grouped, orderId)) continue;
        const lines = grouped[orderId];
        const isLastUpdate = Object.keys(grouped).indexOf(orderId) === Object.keys(grouped).length - 1;

        // Set status to queued first
        await modifyDocs("openOrders", lines.map(d => ({ id: d.lineId, data: { syncStatus: "queued" } })));

        // Process directly
        const result = await updateSalesOrderTask({ orderId, lines, isLastUpdate, taskId });
        results.push(result);
      }

      return { success: true, message: "Tasks processed directly", results, direct: true };
    } else {
    // Production: Use Google Cloud Tasks
      const tasks = [];
      const totalCnt = Object.keys(grouped).length;
      let isLastUpdate = false;
      let cnt = 0;
      for (const orderId in grouped) {
        cnt++;
        if (cnt == totalCnt) {
          isLastUpdate = true;
        }
        if (!Object.prototype.hasOwnProperty.call(grouped, orderId)) continue;
        const lines = grouped[orderId];
        await modifyDocs("openOrders", lines.map(d => ({ id: d.lineId, data: { syncStatus: "queued" } })));
        // Always use Google Cloud Tasks (will route to sandboxTaskQueue in testing environments)
        const taskResult = await createTask("netsuiteTaskQueue", { data: { orderId, lines, isLastUpdate, taskId } }, "https://us-central1-hj-reporting.cloudfunctions.net/updateSalesOrderOnTask");
        tasks.push(taskResult);

        // Update the process queue task with cloud task information and status
        if (taskId && taskResult.taskPath) {
          await modifyDoc("tasks", taskId, {
            status: "queued",
            cloudTaskPath: taskResult.taskPath,
            cloudTaskProject: taskResult.project,
            cloudTaskLocation: taskResult.location,
            cloudTaskQueue: taskResult.queue,
            queuedAt: new Date()
          });
        }
      }
      await Promise.all(tasks);
      // Return success with task information, but actual completion is tracked via Firestore
      // Success here only means tasks were successfully queued in Google Cloud Tasks
      return { success: true, message: "Tasks queued successfully", tasks, queued: true };
    }
  } catch (error) {
    console.error("Error updating allocation strategy:", error);
    return { error: error.message };
  }
});
exports.updateSalesOrderOnTask = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async (data, context) => {
  console.log("updateSalesOrderOnTask", data, context);

  const { orderId, lines, isLastUpdate, taskId } = data.data;
  // Update task status to processing when the cloud task starts
  if (taskId) {
    try {
      await modifyDoc("tasks", taskId, {
        status: "processing",
        processingStartedAt: new Date(),
        message: "Processing NetSuite updates..."
      });
    } catch (error) {
      console.warn("Could not update task status to processing:", error);
    }
  }

  try {
    // Execute the actual NetSuite update
    const result = await updateSalesOrderTask({ orderId, lines, isLastUpdate, taskId });

    // Task completion is now handled by updateSalesOrderTask itself in hybrid approach
    // No need to override here - the function handles its own completion

    return result;
  } catch (error) {
    console.error("Error in updateSalesOrderOnTask:", error);

    // Update task status to failed on error
    if (taskId) {
      await modifyDoc("tasks", taskId, {
        status: "failed",
        completedAt: new Date(),
        error: error.message,
        message: `NetSuite update failed: ${error.message}`
      });
    }

    throw error;
  }
});

// Cloud Function to cancel a queued task
exports.cancelTaskOnCall = onCall(async (data, context) => {
  const { taskPath, taskId } = data.data;

  if (!taskPath) {
    return { error: "Task path is required for cancellation" };
  }

  try {
    // Delete the task from Google Cloud Tasks
    const result = await deleteTask(taskPath);

    // Update the process queue item status to cancelled if taskId is provided
    if (taskId) {
      await modifyDoc("tasks", taskId, {
        status: "cancelled",
        cancelledAt: new Date(),
        error: null
      });
    }

    return { success: true, message: result.message };
  } catch (error) {
    console.error("Error cancelling task:", error);
    return { error: error.message };
  }
});

exports.upsert = upsert;

exports.bigQueryUpsertOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async ({ data, context }) => {
  const { datasetId, tableId, rows, uniqueKey } = data;
  if (!datasetId || !tableId || !rows || !uniqueKey) {
    throw new Error("Missing required parameters: datasetId, tableId, uniqueKey, or rows");
  }
  try {
    await upsert(datasetId, tableId, uniqueKey, rows);
    return { success: true };
  } catch (error) {
    console.error("Error in bigQueryUpsertOnCall:", error);
    return { success: false, error: error.message };
  }
});

exports.bigQueryLoadToTableOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async ({ data, context }) => {
  const { datasetId, tableId, rows, replace } = data;
  if (!datasetId || !tableId || !rows) {
    throw new Error("Missing required parameters: datasetId, tableId, or rows");
  }
  try {
    // The bigQueryLoadToTable function now handles testing dataset logic internally
    await bigQueryLoadToTable({ datasetId, tableId, rows, replace });
    return { success: true };
  } catch (error) {
    console.error("Error in bigQueryLoadToTableOnCall:", error);
    return { success: false, error: error.message, stack: error.stack };
  }
});
const refreshDemandPlanFile = async () => {
  console.log("refreshing demand plan file");
  const startTime = Date.now();
  console.log("fetching demand plan");

  // // Get the number of rows in the demand_plan table
  // const demandPlanRows = await runQuery({
  //   query: `
  //     SELECT COUNT(*) as count
  //     FROM \`hj-reporting.forecast.demand_plan\`
  //   `
  // });
  // console.log("demand plan rows", demandPlanRows[0].count);

  // Fetch all comparison data sources
  console.log("fetching comparison data sources...");
  const promises = [];

  // 1. Fetch external demand plans
  // promises.push(runQuery({
  //   query: `
  //     SELECT node_code, upc, date, qty
  //       FROM \`hj-reporting.forecast.external_demand_plans\`
  //       WHERE date BETWEEN DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH) AND DATE_ADD(CURRENT_DATE(), INTERVAL 12 MONTH)
  //     `
  // }));

  // 2. Fetch seasonality data
  promises.push(runQuery({
    query: `
      SELECT forecast_node, seasonality_factor, month_id, month_name
      FROM \`hj-reporting.forecast.seasonality\`
    `
  }));

  // 3. Fetch TAS data for different periods
  promises.push(runQuery({
    query: `
      SELECT forecast_node, upc, tas_7, tas_30, tas_90
      FROM \`hj-reporting.sales.sales_summary\`
      WHERE tas_7 > 0 OR tas_30 > 0 OR tas_90 > 0
    `
  }));

  const [
    // externalDemandData, 
    seasonalityData,
    tasData] = await Promise.all(promises);

  // Build lookup maps for comparison data
  // const externalLookup = new Map();
  // externalDemandData.forEach(row => {
  //   const key = `${row.node_code}_${row.upc}_${row.date.value}`;
  //   externalLookup.set(key, row.qty);
  // });

  const seasonalityLookup = new Map();
  seasonalityData.forEach(row => {
    const key = `${row.forecast_node}_${row.month_id}`;
    seasonalityLookup.set(key, row.seasonality_factor);
  });

  // Build seasonal TAS lookups
  const today = new Date();
  const seasonalTasLookups = { 7: new Map(), 30: new Map(), 90: new Map() };
  const tasDataMap = new Map();

  tasDataMap.set(7, new Map());
  tasDataMap.set(30, new Map());
  tasDataMap.set(90, new Map());
  tasData.forEach(row => {
    const key = `${row.forecast_node}_${row.upc}`;
    tasDataMap.get(7).set(key, row.tas_7);
    tasDataMap.get(30).set(key, row.tas_30);
    tasDataMap.get(90).set(key, row.tas_90);
  });

  // For each TAS period (7,30,90) iterate its map of forecast_node_upc -> value
  [7, 30, 90].forEach(days => {
    const dayMap = tasDataMap.get(days) || new Map();
    dayMap.forEach((tasValue, key) => {
      if (tasValue > 0) {
        if (!seasonalTasLookups[days].has(key)) {
          seasonalTasLookups[days].set(key, {});
        }

        const rowData = seasonalTasLookups[days].get(key);

        // Calculate seasonal adjustments for next 365 days
        const currentMonthId = today.getMonth() + 1;
        const daysInCurrentMonthTotal = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate();

        const prevMonthDate = new Date(today);
        prevMonthDate.setDate(0);
        const prevMonthId = prevMonthDate.getMonth() + 1;
        const daysInPrevMonthTotal = prevMonthDate.getDate();

        const daysAgo = new Date(today);
        daysAgo.setDate(today.getDate() - (days - 1));

        let daysInCurrentMonthForTas;
        let daysInPrevMonthForTas;

        if (daysAgo.getMonth() === today.getMonth()) {
          daysInCurrentMonthForTas = days;
          daysInPrevMonthForTas = 0;
        } else {
          const currentDayOfMonth = today.getDate();
          daysInCurrentMonthForTas = currentDayOfMonth;
          daysInPrevMonthForTas = days - currentDayOfMonth;
        }

        const seasonalityKeyCurrentMonth = `${rowData.forecast_node}_${currentMonthId}`;
        const seasonalityFactorCurrentMonth = seasonalityLookup[seasonalityKeyCurrentMonth];

        const seasonalityKeyPrevMonth = `${rowData.forecast_node}_${prevMonthId}`;
        const seasonalityFactorPrevMonth = seasonalityLookup[seasonalityKeyPrevMonth];

        // Check for required seasonality factors
        if (seasonalityFactorCurrentMonth === undefined || (daysInPrevMonthForTas > 0 && seasonalityFactorPrevMonth === undefined)) {
          // Fallback logic: use raw TAS value for all days
          for (let i = 0; i < 365; i++) {
            const futureDate = new Date();
            futureDate.setDate(today.getDate() + i);
            const dateStr = futureDate.toISOString().split("T")[0];
            rowData[dateStr] = Math.round(tasValue);
          }
          return;
        }

        const dailySeasonalityCurrent = (seasonalityFactorCurrentMonth || 0) / daysInCurrentMonthTotal;
        const dailySeasonalityPrev = (seasonalityFactorPrevMonth || 0) / daysInPrevMonthTotal;

        const weightedDailySeasonalityBase = ((dailySeasonalityPrev * daysInPrevMonthForTas) + (dailySeasonalityCurrent * daysInCurrentMonthForTas)) / days;

        if (weightedDailySeasonalityBase === 0) {
          // Fallback for this item if base seasonality is zero
          for (let i = 0; i < 365; i++) {
            const futureDate = new Date();
            futureDate.setDate(today.getDate() + i);
            const dateStr = futureDate.toISOString().split("T")[0];
            rowData[dateStr] = Math.round(tasValue);
          }
          return;
        }

        // Generate daily lookups for the next 12 months
        for (let i = 0; i < 365; i++) {
          const futureDate = new Date();
          futureDate.setDate(today.getDate() + i);

          const futureMonthId = futureDate.getMonth() + 1;
          const daysInFutureMonthTotal = new Date(futureDate.getFullYear(), futureDate.getMonth() + 1, 0).getDate();
          const seasonalityKeyFutureMonth = `${rowData.forecast_node}_${futureMonthId}`;
          const seasonalityFactorFutureMonth = seasonalityLookup[seasonalityKeyFutureMonth];

          const dateStr = futureDate.toISOString().split("T")[0];

          if (seasonalityFactorFutureMonth !== undefined) {
            const dailySeasonalityFuture = seasonalityFactorFutureMonth / daysInFutureMonthTotal;
            const seasonalAdjustment = dailySeasonalityFuture / weightedDailySeasonalityBase;
            const adjustedQty = tasValue * seasonalAdjustment;
            rowData[dateStr] = Math.round(Math.max(0, adjustedQty));
          } else {
            // Fallback for this day if seasonality factor for the future month is missing
            rowData[dateStr] = Math.round(tasValue);
          }
        }
      }
    });
  });

  // Fetch all demand plan data in one query to avoid chunking issues
  const dataProms = [
    runQuery({
      query: `
        SELECT
          forecast.upc,
          forecast.date AS date,
          forecast.forecast_node,
          forecast.division,
          forecast.class,
          forecast.region,
          forecast.channel,
          forecast.customer,
          forecast.locked,
          forecast.proxy_item,
          forecast.qty,
          forecast.forecast_method
        FROM \`hj-reporting.forecast.demand_plan\` as forecast
        LEFT JOIN \`hj-reporting.forecast.forecast_nodes\` as nodes ON forecast.forecast_node = nodes.code
        WHERE forecast.date >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH)
          AND forecast.date <= DATE_ADD(CURRENT_DATE(), INTERVAL 11 MONTH)
        ORDER BY forecast.upc, forecast.forecast_node, forecast.date
      `
    }),
    runQuery({
      query: `
      SELECT
        items.producttype as product,
        items.lifestatus,
        items.productspecification as specification,
        items.productcategory as category,
        items.productform as form,
        items.image,
        items.launchdate as launchdate,
        items.enddate as enddate,
        items.color,
        items.size,
        items.baseprice,
        items.productdivision as division,
        items.upc,
        items.internalid as netsuite_id
      FROM \`hj-reporting.items.items_netsuite\` as items
    `
    }),
    runQuery({
      query: `
      SELECT
        nodes.code,
        (1-(nodes.msrp_discount/100)) as msrp_discount,
        nodes.color as forecast_node_color
      FROM \`hj-reporting.forecast.forecast_nodes\` as nodes
    `
    })
  ];

  const [forecast, items, nodes] = await Promise.all(dataProms);
  console.log("items", items.length);
  console.log("nodes", nodes.length);

  const itemMap = {};
  const upcOptions = [];
  items.forEach(item => {
    itemMap[item.upc] = item;
    if (item.upc) {
      upcOptions.push({ value: item.upc, label: `${item.upc} - ${item.product} ${item.color} ${item.size}`.trim() });
    }
  });

  const nodeMap = {};
  nodes.forEach(node => {
    nodeMap[node.code] = node;
  });

  // Merge forecast and items and nodes, and add comparison data
  const data = forecast.map(row => {
    const item = itemMap[row.upc];
    const node = nodeMap[row.forecast_node];
    const tas7 = tasDataMap.get(7).get(`${row.forecast_node}_${row.upc}`);
    const tas30 = tasDataMap.get(30).get(`${row.forecast_node}_${row.upc}`);
    const tas90 = tasDataMap.get(90).get(`${row.forecast_node}_${row.upc}`);

    // Get comparison values
    const dateStr = row.date.value;
    const tasKey = `${row.forecast_node}_${row.upc}`;

    const compOriginalQty = row.qty; // Original value is the same as current
    // const compExternalQty = externalLookup.get(externalKey) || 0;
    const compSeasonalTas7Qty = seasonalTasLookups[7].get(tasKey)?.[dateStr] || 0;
    const compSeasonalTas30Qty = seasonalTasLookups[30].get(tasKey)?.[dateStr] || 0;
    const compSeasonalTas90Qty = seasonalTasLookups[90].get(tasKey)?.[dateStr] || 0;

    return {
      ...row,
      ...item,
      ...node,
      tas7,
      tas30,
      tas90,
      // Add comparison fields
      comp_original_qty: compOriginalQty,
      // comp_external_qty: compExternalQty,
      comp_seasonal_tas7_qty: compSeasonalTas7Qty,
      comp_seasonal_tas30_qty: compSeasonalTas30Qty,
      comp_seasonal_tas90_qty: compSeasonalTas90Qty,
    };
  });

  // Calculated fields
  data.forEach(row => {
    row.date = row.date.value;
    row.enddate = row.enddate?.value;
    row.launchdate = row.launchdate?.value;
    row.msrp_discount = row.msrp_discount || 0;
    row.forecast_node_color = row.forecast_node_color || "black";
  });

  console.log("refreshDemandPlanFile took", (Date.now() - startTime) / 1000, "seconds", data.length, data[0]);

  // Debug: Check final data for specific UPC
  // const finalDebugRows = data.filter(row => row.upc === "810084159057" && row.forecast_node === "US_HJ_Marketplace_Amazon");
  // console.log("Debug - final rows for 810084159057 US_HJ_Marketplace_Amazon:", finalDebugRows.length);
  // if (finalDebugRows.length > 0) {
  //   const finalAugRows = finalDebugRows.filter(row => row.date.includes("2025-08"));
  //   console.log("Debug - final Aug 2025 rows:", finalAugRows.length);
  //   const finalAugTotal = finalAugRows.reduce((acc, row) => acc + row.qty, 0);
  //   console.log("Debug - final Aug 2025 total:", finalAugTotal);
  // }

  // Save the data to a csv file in the firebasestorage bucket
  const bucketName = "hj-reporting.firebasestorage.app";
  const filePath = `forecast/demand_plan/demand_plan.json`;
  await saveToCloudStorage(bucketName, filePath, JSON.stringify(data), "application/json");
  const upcBucketPath = `forecast/demand_plan/upc_options.json`;
  await saveToCloudStorage(bucketName, upcBucketPath, JSON.stringify(upcOptions), "application/json");
  await modifyDoc("lists", "lastUpdates", { demandPlan: new Date() });

  return {
    success: true,
    message: "Demand plan saved successfully to firebasestorage.",
    demandPlanUrl: `gs://hj-reporting.firebasestorage.app/forecast/demand_plan/demand_plan.json`,
    upcOptionsUrl: `gs://hj-reporting.firebasestorage.app/forecast/demand_plan/upc_options.json`,
  };
};

const fetchDemandPlanFile = async () => {
  const startTime = Date.now();
  const data = await runQuery({
    query: `
        SELECT
          forecast.upc,
          forecast.date AS date,
          forecast.forecast_node,
          forecast.division,
          forecast.class,
          forecast.region,
          forecast.channel,
          forecast.customer,
          forecast.locked,
          forecast.proxy_item,
          forecast.qty,
          forecast.forecast_method,
          items.producttype as product,
          items.lifestatus,
          items.productspecification as specification,
          items.productcategory as category,
          items.productform as form,
          items.image,
          items.launchdate as launchdate,
          items.enddate as enddate,
          items.color,
          items.size,
          items.baseprice,
          items.productdivision as division,
          items.upc,
          items.internalid as netsuite_id,
          (1-(nodes.msrp_discount/100)) as msrp_discount,
          nodes.color as forecast_node_color
        FROM \`hj-reporting.forecast.demand_plan\` as forecast
        LEFT JOIN \`hj-reporting.forecast.forecast_nodes\` as nodes ON forecast.forecast_node = nodes.code
        LEFT JOIN \`hj-reporting.items.items_netsuite\` as items ON forecast.upc = items.upc
        WHERE forecast.date >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH)
          AND forecast.date <= DATE_ADD(CURRENT_DATE(), INTERVAL 11 MONTH)
        ORDER BY forecast.upc, forecast.forecast_node, forecast.date
      `
  });
  console.log("fetchDemandPlanFile took", (Date.now() - startTime) / 1000, "seconds", data.length, data[0]);
  //save to storage via stream
  const bucketName = "hj-reporting.firebasestorage.app";
  const filePath = `forecast/demand_plan/demand_plan.json`;
  await saveToCloudStorage(bucketName, filePath, JSON.stringify(data), "application/json");
  return {
    success: true,
    message: "Demand plan saved successfully to firebasestorage.",
    demandPlanUrl: `gs://hj-reporting.firebasestorage.app/forecast/demand_plan/demand_plan.json`,
  };
};

exports.refreshDemandPlanOnCall = onCall({
  timeoutSeconds: 3600, // 1 hour
  memory: "2GiB",
}, async (data, context) => {
  try {
    await refreshDemandPlanFile();
  } catch (error) {
    console.error("Error refreshing demand plan:", error);
    return {
      success: false,
      message: "Error refreshing demand plan.",
      error: error.message,
    };
  }
  return {
    success: true,
    message: "Demand plan refreshed successfully.",
    demandPlanUrl: `gs://hj-reporting.firebasestorage.app/forecast/demand_plan/demand_plan.json`,
  };
});

exports.fetchDemandPlanOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "2GiB",
}, async (data, context) => {
  const response = await fetchDemandPlanFile();
  return response;
});

exports.stageDemandPlanChunkOnCall = onCall({
  timeoutSeconds: 300,
  memory: "512MiB",
}, async ({ data }) => {
  const { rows, uploadId, chunkIndex } = data;
  if (!rows || !uploadId || chunkIndex === undefined) {
    throw new Error("Missing required parameters: rows, uploadId, or chunkIndex.");
  }

  const bucketName = "hj-reporting.firebasestorage.app";
  const filePath = `demand_plan_staging/${uploadId}/chunk_${chunkIndex}.csv`;

  try {
    const csvData = Papa.unparse(rows);
    await saveToCloudStorage(bucketName, filePath, csvData);
    return { success: true, message: `Chunk ${chunkIndex} staged successfully.` };
  } catch (error) {
    console.error(`Error staging chunk ${chunkIndex} for upload ${uploadId}:`, error);
    throw new Error("Failed to stage data chunk.");
  }
});

exports.commitDemandPlanFromGcsOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "1GiB",
}, async ({ data }) => {
  const { uploadId } = data;
  if (!uploadId) {
    throw new Error("Missing required parameter: uploadId.");
  }

  const bucketName = "hj-reporting.firebasestorage.app";
  const prefix = `demand_plan_staging/${uploadId}/`;
  const datasetId = "forecast";
  const tableId = "demand_plan";

  const bucket = storage.bucket(bucketName);

  try {
    const [files] = await bucket.getFiles({ prefix });

    if (files.length === 0) {
      throw new Error("No staged files found for this upload ID.");
    }

    const allRows = [];
    const readPromises = files.map(file => file.download());
    const fileBuffers = await Promise.all(readPromises);

    fileBuffers.forEach(buffer => {
      const csvData = buffer[0].toString("utf-8");
      const parsed = Papa.parse(csvData, { header: true, skipEmptyLines: true, dynamicTyping: true });
      allRows.push(...parsed.data);
    });

    console.log(`Downloaded and parsed ${allRows.length} rows from ${files.length} staged files.`);

    await bigQueryLoadToTable({
      datasetId,
      tableId,
      rows: allRows,
      replace: true,
    });

    console.log(`BigQuery table ${tableId} loaded successfully for upload ${uploadId}.`);

    await bucket.deleteFiles({ prefix });
    console.log(`Staged files for ${uploadId} deleted.`);

    return { success: true, message: "Demand plan saved successfully from staged files." };
  } catch (error) {
    console.error(`Error committing demand plan for upload ${uploadId}:`, error);
    try {
      await bucket.deleteFiles({ prefix });
    } catch (cleanupError) {
      console.error(`Failed to cleanup files for failed upload ${uploadId}:`, cleanupError);
    }
    throw new Error("Failed to commit demand plan from staged files.");
  }
});
// === FIRESTORE DEMAND PLAN FUNCTIONS ===

exports.migrateDemandPlanToFirestoreOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "2GiB",
}, async (req, context) => {
  try {
    const { limitRows } = req.data || {};


    const result = await migrateBigQueryToFirestore({ limitRows });
    return result;
  } catch (error) {
    console.error("Error migrating to Firestore:", error);
    throw new Error("Failed to migrate demand plan to Firestore: " + error.message);
  }
});


// === ITEM NODE MATRIX FUNCTIONS ===

// exports.syncItemNodeMatrixOnCall = onCall({
//   timeoutSeconds: 600,
// }, async (req, context) => {
//   try {
//     const result = await syncUpcList();
//     return result;
//   } catch (error) {
//     console.error("Error syncing item node matrix:", error);
//     throw new Error("Failed to sync item node matrix: " + error.message);
//   }
// });

// exports.updateUpcForecastNodesOnCall = onCall({
//   timeoutSeconds: 300,
// }, async (req, context) => {
//   try {
//     const { upc, forecastNodes } = req.data;
//     if (!upc || !forecastNodes) {
//       throw new Error("Missing required parameters: upc, forecastNodes");
//     }


//     const result = await updateUpcForecastNodes({ upc, forecastNodes });

//     return result;
//   } catch (error) {
//     console.error("Error updating UPC forecast nodes:", error);
//     throw new Error("Failed to update UPC forecast nodes: " + error.message);
//   }
// });

// exports.bulkUpdateUpcForecastNodesOnCall = onCall({
//   timeoutSeconds: 1800,
//   memory: "1GiB",
// }, async (req, context) => {
//   try {
//     const { updates } = req.data;
//     if (!updates || !Array.isArray(updates)) {
//       throw new Error("Missing required parameter: updates (array)");
//     }


//     const result = await bulkUpdateUpcForecastNodes({ updates });

//     return result;
//   } catch (error) {
//     console.error("Error in bulk update:", error);
//     throw new Error("Failed to bulk update UPC forecast nodes: " + error.message);
//   }
// });

// exports.getAllUpcNodesOnCall = onCall({
//   timeoutSeconds: 60,
// }, async (req, context) => {
//   try {
//     const result = await getAllUpcNodes();

//     return { success: true, data: result };
//   } catch (error) {
//     console.error("Error getting all UPC nodes:", error);
//     throw new Error("Failed to get UPC nodes: " + error.message);
//   }
// });

// exports.getForecastNodesListOnCall = onCall({
//   timeoutSeconds: 60,
// }, async (req, context) => {
//   try {
//     const result = await getForecastNodesList();

//     return { success: true, data: result };
//   } catch (error) {
//     console.error("Error getting forecast nodes list:", error);
//     throw new Error("Failed to get forecast nodes list: " + error.message);
//   }
// });
exports.getItemNodeMatrixDataOnCall = onCall({
  timeoutSeconds: 60,
}, async (req, context) => {
  try {
    const proms = [bigQueryGetItems({ whereClause: "lifestatus in ('Active', 'Launching') AND upc IS NOT NULL AND upc != ''" }), getForecastNodesList()];
    const [items, forecastNodes] = await Promise.all(proms);
    return { success: true, items, forecastNodes };
  } catch (error) {
    console.error("Error getting items:", error);
    throw new Error("Failed to get items: " + error.message);
  }
});

exports.syncUpcOptionsOnCall = onCall({
  timeoutSeconds: 300,
}, async (req, context) => {
  try {
    // Query BigQuery for UPC options
    const query = `
      SELECT DISTINCT upc, item_name 
      FROM \`hj-reporting.forecast.forecast_data\` 
      WHERE upc IS NOT NULL 
      AND upc != '' 
      ORDER BY item_name
    `;

    const result = await runQuery(query);
    const upcOptions = result.map(row => ({
      upc: row.upc,
      item_name: row.item_name || row.upc
    }));

    // Store in Firestore
    await admin.firestore().doc("settings/upcOptions").set({
      options: upcOptions,
      lastUpdated: admin.firestore.FieldValue.serverTimestamp()
    });

    return {
      success: true,
      message: `Synced ${upcOptions.length} UPC options to Firestore`,
      count: upcOptions.length
    };
  } catch (error) {
    console.error("Error syncing UPC options:", error);
    throw new Error("Failed to sync UPC options: " + error.message);
  }
});

// === SCHEDULED FUNCTIONS ===


// exports.scheduledSyncItemNodeMatrix = onSchedule("0 6 * * *", async (event) => {
//   // Runs daily at 6 AM
//   try {
//     console.log("Running scheduled item node matrix sync...");


//     const result = await syncUpcList();
//     console.log("Scheduled item sync completed:", result.message);

//     return result;
//   } catch (error) {
//     console.error("Error in scheduled item sync:", error);
//     throw error;
//   }
// });

// === FIRESTORE TRIGGERS ===

exports.onItemNodeMatrixWrite = onDocumentWritten("itemNodeMatrix/{upc}", async (event) => {
  try {
    const { upc } = event.params;
    const beforeData = event.data?.before?.data();
    const afterData = event.data?.after?.data();

    console.log(`ItemNodeMatrix document changed for UPC: ${upc}`, { beforeData, afterData });
    if (!afterData) {
      // Document was deleted - remove corresponding demand plan documents
      console.log(`Document deleted for UPC: ${upc}, cleaning up demand plans...`);

      const oldNodes = beforeData?.forecastNodes || [];
      for (const node of oldNodes) {
        const upcForecastNode = `${upc}_${node}`;
        try {
          await deleteDoc("demandPlan", upcForecastNode);
          console.log(`Deleted demand plan document: ${upcForecastNode}`);
        } catch (error) {
          console.error(`Error deleting demand plan document ${upcForecastNode}:`, error);
        }
      }
    } else {
      // Document was created or updated
      const oldNodes = beforeData?.forecastNodes || [];
      const newNodes = afterData?.forecastNodes || [];
      const newNodeColor = afterData?.nodeColor || "";
      // Find nodes that were removed
      const removedNodes = oldNodes.filter(node => !newNodes.includes(node));
      console.log("removedNodes", removedNodes);

      // Find nodes that were added
      const addedNodes = newNodes.filter(node => !oldNodes.includes(node));
      console.log("addedNodes", addedNodes);
      // Remove demand plan documents for removed nodes
      for (const node of removedNodes) {
        const upcForecastNode = `${upc}_${node}`;
        try {
          await modifyDoc("demandPlan", upcForecastNode, {
            active: false,
            updatedAt: new Date()
          }, { merge: true });
          console.log(`Deactivated demand plan document: ${upcForecastNode}`);
        } catch (error) {
          console.error(`Error deactivating demand plan document ${upcForecastNode}:`, error);
        }
      }

      // Create demand plan documents for added nodes (with empty data structure)
      for (const node of addedNodes) {
        const upcForecastNode = `${upc}_${node}`;
        try {
          await modifyDoc("demandPlan", upcForecastNode, {
            upc: upc,
            forecastNode: node,
            nodeColor: newNodeColor,
            active: true,
            updatedAt: new Date()
          });
          console.log(`Activated demand plan document: ${upcForecastNode}`);
        } catch (error) {
          console.error(`Error activating demand plan document ${upcForecastNode}:`, error);
        }
      }
    }
    return { success: true };
  } catch (error) {
    console.error("Error in onItemNodeMatrixWrite trigger:", error);
    throw error;
  }
});

exports.getFromCloudStorageOnCall = onCall({
  timeoutSeconds: 3600,
  memory: "2GiB",
}, async (data) => {
  const { bucketName, filePath } = data.data;
  const response = await getFromCloudStorage(bucketName, filePath);
  return JSON.parse(response);
});

exports.updateDocsOnCall = onCall({}, async (request) => {
  console.log("updateDocsOnCall", request);
  console.log("updateDocsOnCall", JSON.stringify(request.data));
  const { collection, updates, type, ids } = request.data;
  if (type === "delete") {
    await deleteDocs(collection, [], ids);
  } else {
    await modifyDocs(collection, updates);
  }
  return { success: true, message: "Docs updated successfully." };
});
// Use dayjs for date handling, but only use methods available in Node.js (avoid browser-only plugins)
