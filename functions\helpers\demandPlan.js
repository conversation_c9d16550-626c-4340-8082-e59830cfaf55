const { createDoc, getDocData } = require("./firestore");

// Get next 12 months from current date
const getNext12Months = () => {
  const months = [];
  const now = new Date();
  
  for (let i = 0; i < 12; i++) {
    const date = new Date(now.getFullYear(), now.getMonth() + i, 1);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    months.push({ year, month, key: `${year}-${month}` });
  }
  
  return months;
};

// Get days in a specific month
const getDaysInMonth = (year, month) => {
  const days = [];
  const daysInMonth = new Date(year, month, 0).getDate();
  
  for (let day = 1; day <= daysInMonth; day++) {
    const dayStr = String(day).padStart(2, "0");
    days.push({
      day: dayStr,
      total: 0,
      isValid: true
    });
  }
  
  return days;
};

// Get demand plan document
const getDemandPlanDocument = async ({ upcForecastNode }) => {
  try {
    const docData = await getDocData("demandPlan", upcForecastNode);
    return docData;
  } catch (error) {
    console.error("Error getting demand plan document:", error);
    return null;
  }
};

// Create or update demand plan document
const createOrUpdateDemandPlanDocument = async ({ upcForecastNode, metadata, months, days }) => {
  try {
    const docData = {
      upcForecastNode,
      metadata,
      months: months || {},
      days: days || {},
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await createDoc("demandPlan", upcForecastNode, docData);
    console.log(`Created/updated demand plan document: ${upcForecastNode}`);
    return docData;
  } catch (error) {
    console.error("Error creating/updating demand plan document:", error);
    throw error;
  }
};

module.exports = {
  getNext12Months,
  getDaysInMonth,
  getDemandPlanDocument,
  createOrUpdateDemandPlanDocument
};
