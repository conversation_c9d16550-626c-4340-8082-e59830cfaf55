{"[javascript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[javascriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "editor.tabSize": 2, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "vscode.css-language-features"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "diffEditor.ignoreTrimWhitespace": false, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[python]": {"editor.formatOnType": true}, "editor.formatOnSaveMode": "modifications", "editor.indentSize": "tabSize", "postman.settings.dotenv-detection-notification-visibility": false}