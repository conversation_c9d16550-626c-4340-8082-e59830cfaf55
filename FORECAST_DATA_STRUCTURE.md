# Forecast Data Structure Documentation

## Overview
The forecast data structure has been optimized to reduce redundancy and improve performance. The system now generates **12 months** of data instead of 24, and includes proper `isValid` properties for each day based on item-specific criteria.

## Key Improvements
1. **Reduced from 24 months to 12 months** - eliminates excessive future data
2. **Added `isValid` property** to each day based on item launch/end dates and life status
3. **Nested day structure** within months for better organization
4. **Backward compatibility** maintained with flat day structure

## Data Structure

### Months Structure
```javascript
months: {
  "2025-09": {
    monthId: "2025-09",
    label: "Sep 25",
    total: 0,
    numDays: 6,                    // Number of days in this month (from today forward)
    validDays: 4,                  // Number of valid days for this specific item
    isValid: true,                 // Whether this month is valid for the item
    days: {                        // Nested days within this month
      "2025-09-25": {
        total: 0,
        isValid: true,             // Based on item launch/end dates
        date: "2025-09-25"
      },
      "2025-09-26": {
        total: 0,
        isValid: true,
        date: "2025-09-26"
      }
      // ... more days
    }
  }
  // ... more months
}
```

### Days Structure (Flat - for backward compatibility)
```javascript
days: {
  "2025-09-25": {
    total: 0,
    isValid: true,                 // Item-specific validity
    monthId: "2025-09"            // Reference to parent month
  },
  "2025-09-26": {
    total: 0,
    isValid: false,               // Item might be ended or not launched yet
    monthId: "2025-09"
  }
  // ... more days
}
```

## isValid Logic

### For Days
A day is considered valid for an item if:
- ✅ The date is today or in the future
- ✅ The item has been launched by this date (`launchdate`)
- ✅ The item has not ended by this date (`enddate`)
- ✅ The item's life status is not 'obsolete' or 'phasing out'

### For Months
A month's validity depends on the specific item and its date range constraints.

## Usage Examples

### Accessing Month Data
```javascript
// Get a specific month
const septemberData = item.months['2025-09'];
console.log(`${septemberData.label}: ${septemberData.validDays}/${septemberData.numDays} valid days`);

// Get all days in September
Object.keys(septemberData.days).forEach(dayStr => {
  const day = septemberData.days[dayStr];
  if (day.isValid) {
    console.log(`${dayStr}: ${day.total} units (valid)`);
  }
});
```

### Accessing Day Data (Flat Structure)
```javascript
// Get a specific day
const todayData = item.days['2025-09-25'];
console.log(`Today: ${todayData.total} units, valid: ${todayData.isValid}`);

// Find parent month
const parentMonth = item.months[todayData.monthId];
```

### Calculating Month Totals
```javascript
// Calculate month total from valid days only
const calculateMonthTotal = (monthData) => {
  return Object.values(monthData.days)
    .filter(day => day.isValid)
    .reduce((sum, day) => sum + day.total, 0);
};
```

## Performance Benefits
- **50% reduction** in data size (12 months vs 24 months)
- **Better organization** with nested structure
- **Faster validity checks** with pre-calculated `isValid` flags
- **Reduced memory usage** in browser console and data transfer

## Migration Notes
- Existing code using the flat `days` structure will continue to work
- New code should prefer the nested `months[monthId].days[dayStr]` structure
- The `isValid` property is now available on every day for business logic
