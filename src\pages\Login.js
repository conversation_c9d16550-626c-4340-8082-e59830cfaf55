/* eslint-disable require-jsdoc */
import React, {useEffect} from 'react';
import {useNavigate} from 'react-router-dom';
import {signInWithGoogle} from './firebase';
import {useUser} from '../contexts/UserContext';
import {Layout, Button, Typography, Card, Space, Divider, Spin} from 'antd';
import {GoogleOutlined, BarChartOutlined, LoadingOutlined} from '@ant-design/icons';

const {Title, Text} = Typography;

function Login() {
  const { user, userData, isAuthenticated, loading } = useUser();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  const handleSignIn = async () => {
    // Prevent double sign-in attempts
    if (user && !userData) {
      console.log('User already authenticated, waiting for user data...');
      return;
    }
    
    try {
      // Ensure the current window is focused before opening popup
      // This helps with popup positioning and user experience
      window.focus();
      await signInWithGoogle();
    } catch (error) {
      console.error('Sign-in error:', error);
      // Re-focus the window if sign-in fails
      window.focus();
    }
  };

  if (loading) {
    return (
      <Layout 
        className="login-container"
        style={{ 
          minHeight: '100vh',
          background: '#fafafa',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '20px'
        }}>
        <Card 
          style={{ 
            width: '100%',
            maxWidth: '400px',
            borderRadius: '16px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
            border: 'none'
          }}
          bodyStyle={{ padding: '48px 32px' }}
        >
          <div style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: '24px' }}>
              <BarChartOutlined 
                style={{ 
                  fontSize: '48px', 
                  color: '#1976d2',
                  marginBottom: '16px'
                }} 
              />
              <Title level={2} style={{ margin: 0, color: '#1976d2' }}>
                HJ Reporting
              </Title>
              
            </div>
            
            <Divider style={{ margin: '32px 0' }} />
            
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <Spin 
                indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
                size="large"
              />
              <Text style={{ fontSize: '16px', color: '#616161' }}>
                {user && !userData ? 'Loading user permissions...' : 'Initializing...'}
              </Text>
            </Space>
          </div>
        </Card>
      </Layout>
    );
  }

  return (
    <Layout 
      className="login-container"
      style={{ 
        minHeight: '100vh',
        background: '#fafafa',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '20px'
      }}>
      <Card 
        style={{ 
          width: '100%',
          maxWidth: '400px',
          borderRadius: '16px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
          border: 'none'
        }}
        bodyStyle={{ padding: '48px 32px' }}
      >
        <div style={{ textAlign: 'center' }}>
          {/* Brand Section */}
          <div style={{ marginBottom: '32px' }}>
            <BarChartOutlined 
              style={{ 
                fontSize: '64px', 
                color: '#1976d2',
                marginBottom: '16px',
                display: 'block'
              }} 
            />
            <Title level={1} style={{ margin: 0, color: '#1976d2', fontSize: '28px' }}>
              HJ Reporting
            </Title>
            
          </div>
          
          <Divider style={{ margin: '32px 0' }} />
          
          {/* Welcome Section */}
          <div style={{ marginBottom: '32px' }}>
            <Title level={3} style={{ color: '#424242', fontWeight: 400 }}>
              Welcome Back
            </Title>
            <Text style={{ fontSize: '15px', color: '#616161' }}>
              Sign in to access your dashboard and reports
            </Text>
          </div>
          
          {/* Login Button */}
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Button 
              type="primary"
              size="large"
              icon={<GoogleOutlined />}
              onClick={handleSignIn}
              loading={user && !userData}
              disabled={user && !userData}
              style={{
                height: '48px',
                width: '100%',
                fontSize: '16px',
                fontWeight: '500',
                borderRadius: '8px',
                background: user && !userData ? undefined : '#1976d2',
                borderColor: user && !userData ? undefined : '#1976d2'
              }}
            >
              {user && !userData ? 'Setting up your account...' : 'Continue with Google'}
            </Button>
            
            {user && !userData && (
              <Text style={{ fontSize: '14px', color: '#616161' }}>
                Please wait while we configure your permissions
              </Text>
            )}
          </Space>
          
          {/* Footer */}
          <div style={{ marginTop: '32px', paddingTop: '24px', borderTop: '1px solid #f0f0f0' }}>
            <Text type="secondary" style={{ fontSize: '13px' }}>
              Secure authentication powered by Google
            </Text>
          </div>
        </div>
      </Card>
    </Layout>
  );
}
export default Login;
