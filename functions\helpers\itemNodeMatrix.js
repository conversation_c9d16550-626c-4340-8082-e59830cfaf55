const { firestore } = require("firebase-admin");
const { runQuery } = require("./bigQuery");
const { modifyDoc } = require("./firestore");
const { createOrUpdateDemandPlanDocument, getDemandPlanDocument, getNext12Months, getDaysInMonth } = require("./demandPlan");

// Sync UPCs from BigQuery to itemNodeMatrix collection
const syncUpcList = async () => {
  try {
    console.log("Syncing UPC list from BigQuery...");
    
    const query = `
      SELECT DISTINCT
        i.upc,
        i.name,
        i.producttype as product,
        i.productdivision as division,
        i.color,
        i.size,
        i.lifestatus,
        i.productspecification as specification,
        i.productcategory as category,
        i.productfamily as family,
        i.launchdate,
        i.enddate,
        i.image,
        i.baseprice,
        i.inactive
      FROM \`hj-reporting.items.items_netsuite\` i
      WHERE i.upc IS NOT NULL 
        AND i.upc != ''
        AND i.inactive = FALSE
      ORDER BY i.producttype, i.color, i.size
    `;
    
    const items = await runQuery({ query });
    console.log(`Fetched ${items.length} items from BigQuery`);
    
    // Get existing UPCs from Firestore
    const existingSnapshot = await firestore().collection("itemNodeMatrix").get();
    const existingUpcs = new Set();
    existingSnapshot.docs.forEach(doc => {
      existingUpcs.add(doc.id);
    });
    
    // Prepare batch operations
    const batch = firestore().batch();
    let newItems = 0;
    let updatedItems = 0;
    
    items.forEach(item => {
      const docRef = firestore().collection("itemNodeMatrix").doc(item.upc);
      
      const itemData = {
        ...item,
        forecastNodes: [], // Empty by default, will be populated by user
        lastModified: new Date(),
        syncedFromBigQuery: new Date()
      };
      
      if (existingUpcs.has(item.upc)) {
        // Update existing item but preserve forecastNodes
        batch.update(docRef, {
          ...itemData,
          forecastNodes: firestore.FieldValue.delete() // Don't overwrite
        });
        updatedItems++;
      } else {
        // Create new item
        batch.set(docRef, itemData);
        newItems++;
      }
    });
    
    await batch.commit();
    
    // Update sync timestamp
    await modifyDoc("lists", "lastUpdates", { 
      itemNodeMatrixSync: new Date() 
    });
    
    console.log(`UPC sync completed: ${newItems} new, ${updatedItems} updated`);
    return { 
      success: true, 
      message: `Synced ${items.length} items: ${newItems} new, ${updatedItems} updated`,
      newItems,
      updatedItems
    };
  } catch (error) {
    console.error("Error syncing UPC list:", error);
    throw error;
  }
};

// Update forecast nodes for a UPC
const updateUpcForecastNodes = async ({ upc, forecastNodes }) => {
  try {
    console.log(`Updating forecast nodes for UPC ${upc}:`, forecastNodes);
    
    // Get current UPC document
    const upcDocRef = firestore().collection("itemNodeMatrix").doc(upc);
    const upcDoc = await upcDocRef.get();
    
    if (!upcDoc.exists) {
      throw new Error(`UPC ${upc} not found in itemNodeMatrix`);
    }
    
    const upcData = upcDoc.data();
    const currentNodes = upcData.forecastNodes || [];
    
    // Determine what changed
    const nodesToAdd = forecastNodes.filter(node => !currentNodes.includes(node));
    const nodesToRemove = currentNodes.filter(node => !forecastNodes.includes(node));
    
    console.log(`Changes for ${upc}: +${nodesToAdd.length} -${nodesToRemove.length}`);
    
    const batch = firestore().batch();
    
    // Update the UPC document
    batch.update(upcDocRef, {
      forecastNodes,
      lastModified: new Date()
    });
    
    // Handle additions - create demand plan documents
    for (const node of nodesToAdd) {
      const demandPlanKey = `${upc}_${node}`;
      
      // Check if demand plan document already exists
      const existingDoc = await getDemandPlanDocument({ upcForecastNode: demandPlanKey });
      
      if (!existingDoc) {
        // Create new demand plan document with zero values
        const next12Months = getNext12Months();
        
        const monthData = {};
        next12Months.forEach(({ key, year, month }) => {
          monthData[key] = {
            days: getDaysInMonth(year, month)
          };
        });
        
        await createOrUpdateDemandPlanDocument({
          upcForecastNode: demandPlanKey,
          metadata: {
            ...upcData,
            forecast_node: node,
            pivotKey: demandPlanKey
          },
          monthData
        });
        
        console.log(`Created demand plan document: ${demandPlanKey}`);
      }
    }
    
    // Handle removals - delete demand plan documents
    for (const node of nodesToRemove) {
      const demandPlanKey = `${upc}_${node}`;
      const demandPlanRef = firestore().collection("demandPlan").doc(demandPlanKey);
      
      // Delete main document
      batch.delete(demandPlanRef);
      
      // Delete month subcollections
      const monthsSnapshot = await demandPlanRef.collection("months").get();
      monthsSnapshot.docs.forEach(monthDoc => {
        batch.delete(monthDoc.ref);
      });
      
      console.log(`Deleted demand plan document: ${demandPlanKey}`);
    }
    
    await batch.commit();
    
    return { 
      success: true, 
      message: `Updated ${upc}: +${nodesToAdd.length} nodes, -${nodesToRemove.length} nodes`,
      added: nodesToAdd,
      removed: nodesToRemove
    };
  } catch (error) {
    console.error("Error updating UPC forecast nodes:", error);
    throw error;
  }
};

// Bulk update forecast nodes for multiple UPCs
const bulkUpdateUpcForecastNodes = async ({ updates }) => {
  try {
    console.log(`Processing bulk update for ${updates.length} UPCs`);
    
    const results = [];
    
    // Process in smaller batches to avoid timeout
    const batchSize = 10;
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (update) => {
        try {
          const result = await updateUpcForecastNodes(update);
          return { upc: update.upc, ...result };
        } catch (error) {
          console.error(`Error updating ${update.upc}:`, error);
          return { 
            upc: update.upc, 
            success: false, 
            error: error.message 
          };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      console.log(`Processed batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(updates.length/batchSize)}`);
    }
    
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    return {
      success: true,
      message: `Bulk update completed: ${successful} successful, ${failed} failed`,
      results,
      successful,
      failed
    };
  } catch (error) {
    console.error("Error in bulk update:", error);
    throw error;
  }
};

// Get all UPCs with their forecast nodes (for ItemNodeMatrix component)
const getAllUpcNodes = async () => {
  try {
    const snapshot = await firestore().collection("itemNodeMatrix").get();
    
    const items = [];
    snapshot.docs.forEach(doc => {
      items.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return items;
  } catch (error) {
    console.error("Error getting all UPC nodes:", error);
    throw error;
  }
};

// Get forecast nodes list from BigQuery
const getForecastNodesList = async () => {
  try {
    const query = `
      SELECT 
        code,
        region_name,
        region_id,
        division_name,
        division_id,
        class_name,
        class_id,
        channel_name,
        channel_id,
        customer_name,
        customer_id,
        color
      FROM \`hj-reporting.forecast.forecast_nodes\`
      WHERE code IS NOT NULL AND code != ''
      ORDER BY code
    `;
    
    const nodes = await runQuery({ query });
    
    return nodes.map(node => ({
      value: node.code,
      label: node.code,
      ...node
    }));
  } catch (error) {
    console.error("Error getting forecast nodes:", error);
    throw error;
  }
};

module.exports = {
  syncUpcList,
  updateUpcForecastNodes,
  bulkUpdateUpcForecastNodes,
  getAllUpcNodes,
  getForecastNodesList
};