import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth, db } from '../pages/firebase';
import { doc, getDoc, onSnapshot } from 'firebase/firestore';

const UserContext = createContext();

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export const UserProvider = ({ children }) => {
  const [user, loading] = useAuthState(auth);
  const [userData, setUserData] = useState(null);
  const [userLoading, setUserLoading] = useState(true);

  useEffect(() => {
    let unsubscribe = null;
    let timeouts = [];
    let isMounted = true;
    
    if (!loading && user?.uid) {
      setUserLoading(true);

      const userRef = doc(db, 'users', user.uid);

      unsubscribe = onSnapshot(userRef,
        (doc) => {
          if (!isMounted) return;

          if (doc.exists()) {
            setUserData(doc.data());
            setUserLoading(false);
          } else {
            // Document might not exist yet, retry once
            const retryTimeout = setTimeout(() => {
              if (isMounted) {
                getDoc(userRef).then((retryDoc) => {
                  if (isMounted) {
                    if (retryDoc.exists()) {
                      setUserData(retryDoc.data());
                    } else {
                      setUserData(null);
                    }
                    setUserLoading(false);
                  }
                }).catch((error) => {
                  console.error('Error fetching user document:', error);
                  if (isMounted) {
                    setUserData(null);
                    setUserLoading(false);
                  }
                });
              }
            }, 2000);
            timeouts.push(retryTimeout);
          }
        },
        (error) => {
          console.error('Error in user data listener:', error);
          if (isMounted) {
            setUserData(null);
            setUserLoading(false);
          }
        }
      );
    } else if (!loading && !user) {
      if (isMounted) {
        setUserData(null);
        setUserLoading(false);
      }
    }

    return () => {
      isMounted = false;
      if (unsubscribe) {
        unsubscribe();
      }
      timeouts.forEach(clearTimeout);
    };
  }, [user, loading]);

  const value = {
    user,
    userData,
    loading: loading || userLoading,
    isAuthenticated: !!user && !!userData,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}; 