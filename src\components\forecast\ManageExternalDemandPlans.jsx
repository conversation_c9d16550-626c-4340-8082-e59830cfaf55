/* eslint-disable guard-for-in */
import React, { useRef, useState, useEffect } from 'react';
import { Row, Col, Dropdown, Button, Tooltip, Menu, Upload, Modal, Form, Select, DatePicker, InputNumber } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import { buildForecastNode } from '../../constants';
import <PERSON> from 'papaparse';
import { api } from '../../pages/firebase';
import { message } from 'antd';
import { SaveOutlined, UploadOutlined, MenuOutlined, UndoOutlined, DownloadOutlined } from '@ant-design/icons';

const ManageExternalDemandPlans = ({ userObj }) => {
  const [loading, setLoading] = useState(false);
  const [rows, setRows] = useState([]);
  const [pivotedRows, setPivotedRows] = useState([]);
  const [pendingRows, setPendingRows] = useState(JSON.parse(localStorage.getItem('pendingRows') || '{}'));
  const [selectedRows, setSelectedRows] = useState([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const gridApi = useRef(null);

  // Generate next 12 months for column headers
  const generateMonthColumns = () => {
    const today = new Date();
    const columns = [];
    
    for (let i = 0; i < 12; i++) {
      const date = new Date(today.getFullYear(), today.getMonth() + i, 1);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
      const monthLabel = date.toLocaleString('default', { month: 'short', year: 'numeric' });
      
      columns.push({
        headerName: monthLabel,
        field: monthKey,
        sortable: true,
        filter: true,
        editable: true,
        type: 'numericColumn',
        aggFunc: 'sum',
        cellStyle: params => {
          const originalValue = params.data?.originalValues?.[monthKey] || 0;
          const currentValue = params.value || 0;
          
          // Check if this is a deleted row
          if (params.data?.changeType === 'deleted') {
            return { color: '#fa8c16', fontWeight: 'bold' }; // Orange for deleted
          }
          
          // Check if this is an added row
          if (params.data?.changeType === 'added') {
            return { color: '#1890ff', fontWeight: 'bold' }; // Blue for added
          }
          
          // Highlight if current value differs from original (including 0 to non-zero changes)
          if (currentValue !== originalValue) {
            const delta = currentValue - originalValue;
            if (delta > 0) {
              return { color: '#52c41a', fontWeight: 'bold' }; // Green for positive delta
            } else if (delta < 0) {
              return { color: '#ff4d4f', fontWeight: 'bold' }; // Red for negative delta
            }
          }
          return null;
        },
        cellRenderer: params => {
          const originalValue = params.data?.originalValues?.[monthKey] || 0;
          const currentValue = params.value || 0;
          
          // Debug logging
          if (currentValue !== originalValue) {
            // console.log(`CellRenderer: ${monthKey} - Original: ${originalValue}, Current: ${currentValue}, Delta: ${currentValue - originalValue}`);
          }
          
          // Show delta if there's an actual difference from original (including 0 to non-zero changes)
          if (currentValue !== originalValue) {
            const delta = currentValue - originalValue;
            const deltaText = delta > 0 ? `+${delta}` : `${delta}`;
            return `${currentValue} (${deltaText})`;
          }
          
          // Return just the value if it matches original
          return currentValue;
        }
      });
    }
    
    return columns;
  };

  // Generate columns for preview modal with change type
  const generatePreviewColumns = () => {
    const baseColumns = [
      {
        headerName: 'Change Type',
        field: 'changeType',
        sortable: true,
        filter: true,
        cellStyle: params => {
          const changeType = params.value || 'modified';
          const rowData = params.data;
          
          // Check if this is a deleted row
          if (changeType === 'deleted') {
            return { backgroundColor: '#fff7e6', color: '#fa8c16', fontWeight: 'bold' }; // Orange for deleted
          }
          
          // Check if this is an added row
          if (changeType === 'added') {
            return { backgroundColor: '#e6f7ff', color: '#1890ff', fontWeight: 'bold' }; // Blue for added
          }
          
          // For modified rows, check if there are any month values to determine positive/negative deltas
          if (changeType === 'modified') {
            const today = new Date();
            let hasPositiveDelta = false;
            let hasNegativeDelta = false;
            
            // Check all month columns for deltas
            for (let i = 0; i < 12; i++) {
              const date = new Date(today.getFullYear(), today.getMonth() + i, 1);
              const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
              const originalValue = rowData?.originalValues?.[monthKey] || 0;
              const currentValue = rowData?.[monthKey] || 0;
              
              if (currentValue !== originalValue) {
                const delta = currentValue - originalValue;
                if (delta > 0) hasPositiveDelta = true;
                if (delta < 0) hasNegativeDelta = true;
              }
            }
            
            if (hasPositiveDelta && !hasNegativeDelta) {
              return { backgroundColor: '#f6ffed', color: '#52c41a', fontWeight: 'bold' }; // Green for positive only
            } else if (hasNegativeDelta && !hasPositiveDelta) {
              return { backgroundColor: '#fff2f0', color: '#ff4d4f', fontWeight: 'bold' }; // Red for negative only
            } else if (hasPositiveDelta && hasNegativeDelta) {
              return { backgroundColor: '#fffbe6', color: '#faad14', fontWeight: 'bold' }; // Yellow for mixed
            }
          }
          
          return { backgroundColor: '#e6f7ff', color: '#1890ff', fontWeight: 'bold' }; // Default blue for modified
        },
        cellRenderer: params => {
          const changeType = params.value || 'modified';
          const icons = {
            added: 'Added',
            deleted: 'Deleted',
            modified: 'Modified'
          };
          return `${icons[changeType]} ${changeType.charAt(0).toUpperCase() + changeType.slice(1)}`;
        }
      },
      {
        headerName: 'Region',
        field: 'region',
        sortable: true,
        filter: true,
        enableRowGroup: true,
      },
      {
        headerName: 'Division',
        field: 'division',
        sortable: true,
        filter: true,
        enableRowGroup: true,
      },
      {
        headerName: 'Class',
        field: 'class',
        sortable: true,
        filter: true,
        enableRowGroup: true,
      },
      {
        headerName: 'Channel',
        field: 'channel',
        sortable: true,
        filter: true,
        enableRowGroup: true,
      },
      {
        headerName: 'Customer',
        field: 'customer',
        sortable: true,
        filter: true,
        enableRowGroup: true,
      },
      {
        headerName: 'Spec',
        field: 'productspecification',
        sortable: true,
        filter: true,
        enableRowGroup: true,
      },
      {
        headerName: 'UPC',
        field: 'upc',
        sortable: true,
        filter: true,
        enableRowGroup: true,
      }
    ];

    const monthColumns = generateMonthColumns();
    return [...baseColumns, ...monthColumns];
  };

  // Add onCellValueChanged handler
  const columns = [
    {
      headerName: '',
      width: 40,
      pinned: 'left',
      suppressMovable: true,
      suppressSizeToFit: true,
      lockPosition: true,
      lockVisible: true
    },
    {
      headerName: 'Region',
      field: 'region',
      sortable: true,
      filter: true,
      enableRowGroup: true,
      editable: true,
    },
    {
      headerName: 'Division',
      field: 'division',
      sortable: true,
      filter: true,
      enableRowGroup: true,
      editable: true,
    },
    {
      headerName: 'Class',
      field: 'class',
      sortable: true,
      filter: true,
      enableRowGroup: true,
    },
    {
      headerName: 'Channel',
      field: 'channel',
      sortable: true,
      filter: true,
      enableRowGroup: true,
      editable: true,
    },
    {
      headerName: 'Customer',
      field: 'customer',
      sortable: true,
      filter: true,
      editable: true,
      enableRowGroup: true,
    },
    {
      headerName: 'Spec',
      field: 'productspecification',
      sortable: true,
      filter: true,
      enableRowGroup: true,
    },
    {
      headerName: 'UPC',
      field: 'upc',
      sortable: true,
      filter: true,
      enableRowGroup: true,
      //  cellStyle: params => params.data?.pendingChanges ? { color: '#1890ff' } : null
    },
    ...generateMonthColumns()
  ];

  // Function to pivot the data
  const pivotData = (rawRows) => {
    const pivotedData = {};
    const today = new Date();
    
    // Generate month keys for the next 12 months
    const monthKeys = [];
    for (let i = 0; i < 12; i++) {
      const date = new Date(today.getFullYear(), today.getMonth() + i, 1);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
      monthKeys.push(monthKey);
    }

    rawRows.forEach(row => {
      const date = new Date(row.date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
      
      // Only include if it's one of the next 12 months
      if (!monthKeys.includes(monthKey)) return;
      
      // Create a unique key for grouping
      const groupKey = `${row.region}||${row.division}||${row.class}||${row.channel}||${row.customer || ''}||${row.productspecification}||${row.upc}`;
      
      if (!pivotedData[groupKey]) {
        pivotedData[groupKey] = {
          key: groupKey,
          region: row.region,
          division: row.division,
          class: row.class,
          channel: row.channel,
          customer: row.customer || '',
          productspecification: row.productspecification,
          upc: row.upc,
          pendingChanges: {},
          originalValues: {}, // Store original values for delta calculation
          changeType: 'modified', // 'modified', 'added', 'deleted'
          // Initialize all month columns with 0
          ...monthKeys.reduce((acc, month) => {
            acc[month] = 0;
            return acc;
          }, {})
        };
        
        // Initialize original values for all months to 0
        monthKeys.forEach(month => {
          pivotedData[groupKey].originalValues[month] = 0;
        });
      }
      
      // Add the quantity to the appropriate month
      pivotedData[groupKey][monthKey] += row.qty;
      
      // Store the original value from the database (this is the actual original value)
      // Only set original value if it doesn't already exist (to preserve delta calculations)
      if (!pivotedData[groupKey].originalValues[monthKey] || pivotedData[groupKey].originalValues[monthKey] === 0) {
        // Only set original value for rows without pendingChanges (i.e., original database rows)
        if (!row.pendingChanges) {
          // This is an original row from the database
          pivotedData[groupKey].originalValues[monthKey] = row.qty;
        }
        // If row has pendingChanges, don't overwrite the original value
        // The upload process will handle setting the original values properly
      }
      
      // Mark as pending if the original row had pending changes
      if (row.pendingChanges) {
        pivotedData[groupKey].pendingChanges[monthKey] = true;
      }
    });
    
    return Object.values(pivotedData);
  };

  const handleDownloadSelected = () => {
    // Convert selectedRows to CSV
    let filteredRows = pivotedRows.filter(row => selectedRows.map(r => r.key).includes(row.key));
    if (filteredRows.length === 0) {
      filteredRows = pivotedRows;
    }

    if (filteredRows.length === 0) {
      message.warning('No data to download');
      return;
    }

    // Create headers array
    const baseHeaders = [
      'region', 'division', 'class', 'channel', 'customer', 'productspecification', 'upc'
    ];
    
    const today = new Date();
    const monthHeaders = [];
    for (let i = 0; i < 12; i++) {
      const date = new Date(today.getFullYear(), today.getMonth() + i, 1);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
      // Use YYYY-MM-DD format to prevent Excel auto-formatting
      monthHeaders.push(monthKey);
    }

    const headers = [...baseHeaders, ...monthHeaders];

    // Create data array
    const data = filteredRows.map(row => {
      const rowData = baseHeaders.map(header => row[header]);
      // Add quantities for each month
      monthHeaders.forEach((_, index) => {
        const date = new Date(today.getFullYear(), today.getMonth() + index, 1);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
        rowData.push(row[monthKey] || 0);
      });
      return rowData;
    });

    // Create CSV with headers and data
    const csv = Papa.unparse({
      fields: headers,
      data: data
    });

    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'external_demand_plan_selected.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleUploadFile = (file) => {
    setUploading(true);
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const parsed = Papa.parse(e.target.result, {
          header: true,
          skipEmptyLines: true,
          transformHeader: header => header.trim().toLowerCase().replace(/[^\w\s-\/]/g, ''),
          transform: (value) => value.trim()
        });

        if (!parsed.data || parsed.data.length === 0) {
          message.error('No data found in CSV.');
          setUploading(false);
          return false;
        }

        // Debug: Log the parsed data structure
        // console.log('Parsed CSV headers:', parsed.meta.fields);
        // console.log('Parsed CSV data length:', parsed.data.length);
        // console.log('First row sample:', parsed.data[0]);

        // Validate base headers
        const requiredBaseHeaders = ['region', 'division', 'class', 'channel', 'upc'];
        const missingBaseHeaders = requiredBaseHeaders.filter(header => !parsed.meta.fields.includes(header));
        if (missingBaseHeaders.length > 0) {
          console.error('Missing headers:', missingBaseHeaders);
          console.error('Available headers:', parsed.meta.fields);
          throw new Error(`Missing required headers: ${missingBaseHeaders.join(', ')}. Available headers: ${parsed.meta.fields.join(', ')}`);
        }

        // Identify month columns (format: YYYY-MM-DD) or M/D/YYYY or MM/DD/YYYY or M/D/YY
        let monthColumns = parsed.meta.fields.slice(7); // Skip the first 7 base columns
        if (monthColumns.length === 0) {
          // Try to find month columns by pattern matching
          const potentialMonthColumns = parsed.meta.fields.filter(field => {
            const lowerField = field.toLowerCase();
            // console.log('Checking field for month pattern:', field, 'lowerField:', lowerField);
            return lowerField.match(/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/) ||
                   lowerField.match(/^\d{4}-\d{2}-\d{2}$/) ||
                   lowerField.match(/^\d{1,2}\/\d{1,2}\/\d{2,4}$/) ||
                   // Handle Excel serial number format (e.g., "612025" for "6/1/2025")
                   lowerField.match(/^\d{6,7}$/);
          });
          
          if (potentialMonthColumns.length === 0) {
            throw new Error('No month columns found. Expected format: YYYY-MM-DD (e.g., 2024-01-01) or MM/DD/YYYY (e.g., 01/01/2024) or M/D/YY (e.g., 01/01/25) or month names (e.g., Jun 2025, Jun-25)');
          } else {
            monthColumns = potentialMonthColumns;
          }
        }
        
        // console.log('Detected month columns:', monthColumns);
        // console.log('Sample month column format:', monthColumns[0]);
        // console.log('All parsed fields:', parsed.meta.fields);

        // Helper function to parse any date format safely
        const parseDateSafely = (dateString) => {
          if (!dateString || typeof dateString !== 'string') {
            throw new Error('Invalid date string');
          }
          
          // Remove any extra whitespace
          const cleanDate = dateString.trim();
          
          // Handle YYYY-MM-DD format
          if (cleanDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const [year, month, day] = cleanDate.split('-').map(Number);
            return new Date(year, month - 1, day); // month is 0-indexed
          }
          
          // Handle MM/DD/YYYY format
          if (cleanDate.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
            const [month, day, year] = cleanDate.split('/').map(Number);
            return new Date(year, month - 1, day); // month is 0-indexed
          }
          
          // Handle M/D/YYYY format
          if (cleanDate.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) {
            const parts = cleanDate.split('/').map(Number);
            const [month, day, year] = parts;
            return new Date(year, month - 1, day); // month is 0-indexed
          }
          
          // Handle M/D/YY format
          if (cleanDate.match(/^\d{1,2}\/\d{1,2}\/\d{2}$/)) {
            const parts = cleanDate.split('/').map(Number);
            const [month, day, shortYear] = parts;
            const year = shortYear < 50 ? 2000 + shortYear : 1900 + shortYear;
            return new Date(year, month - 1, day); // month is 0-indexed
          }
          
          // Handle Excel serial number format (e.g., "612025" for "6/1/2025")
          if (cleanDate.match(/^\d{6,7}$/)) {
            const serialNumber = cleanDate;
            let month;
            let year;
            
            if (serialNumber.length === 6) {
              // MMDDYY format
              month = parseInt(serialNumber.substring(0, 2)) - 1;
              const shortYear = parseInt(serialNumber.substring(4, 6));
              year = shortYear < 50 ? 2000 + shortYear : 1900 + shortYear;
            } else if (serialNumber.length === 7) {
              // MMDDYYY format
              month = parseInt(serialNumber.substring(0, 2)) - 1;
              year = parseInt(serialNumber.substring(4, 7));
            } else {
              throw new Error('Invalid Excel serial number format');
            }
            
            return new Date(year, month, 1);
          }
          
          // Handle month name formats
          if (cleanDate.match(/^[A-Za-z]{3}\s+\d{4}$/)) {
            // "Jun 2025" format
            const monthNames = {
              'jan': 0, 'feb': 1, 'mar': 2, 'apr': 3, 'may': 4, 'jun': 5,
              'jul': 6, 'aug': 7, 'sep': 8, 'oct': 9, 'nov': 10, 'dec': 11
            };
            const parts = cleanDate.toLowerCase().split(' ');
            const month = monthNames[parts[0]];
            const year = parseInt(parts[1]);
            if (month === undefined || isNaN(year)) {
              throw new Error('Invalid month name format');
            }
            return new Date(year, month, 1);
          }
          
          if (cleanDate.match(/^[A-Za-z]{3}-\d{2}$/)) {
            // "Jun-25" format
            const monthNames = {
              'jan': 0, 'feb': 1, 'mar': 2, 'apr': 3, 'may': 4, 'jun': 5,
              'jul': 6, 'aug': 7, 'sep': 8, 'oct': 9, 'nov': 10, 'dec': 11
            };
            const parts = cleanDate.toLowerCase().split('-');
            const month = monthNames[parts[0]];
            const shortYear = parseInt(parts[1]);
            if (month === undefined || isNaN(shortYear)) {
              throw new Error('Invalid month name format');
            }
            const year = shortYear < 50 ? 2000 + shortYear : 1900 + shortYear;
            return new Date(year, month, 1);
          }
          
          if (cleanDate.match(/^[A-Za-z]{3}-\d{4}$/)) {
            // "Jun-2025" format
            const monthNames = {
              'jan': 0, 'feb': 1, 'mar': 2, 'apr': 3, 'may': 4, 'jun': 5,
              'jul': 6, 'aug': 7, 'sep': 8, 'oct': 9, 'nov': 10, 'dec': 11
            };
            const parts = cleanDate.toLowerCase().split('-');
            const month = monthNames[parts[0]];
            const year = parseInt(parts[1]);
            if (month === undefined || isNaN(year)) {
              throw new Error('Invalid month name format');
            }
            return new Date(year, month, 1);
          }
          
          throw new Error(`Unsupported date format: ${cleanDate}`);
        };

        // Get unique UPCs from the upload
        const uniqueUpcs = [...new Set(parsed.data.map(row => row.upc))];

        // Query BigQuery to validate UPCs and get item information
        const upcValidationResponse = await api.bigQueryRunQueryOnCall({
          options: {
            query: `
              SELECT 
                upc,
                lifestatus,
                productspecification,
                producttype,
                color,
                size
              FROM \`hj-reporting.items.items_netsuite\`
              WHERE upc IN (${uniqueUpcs.map(upc => `'${upc}'`).join(',')})
            `
          }
        });

        // Create a map of valid UPCs and their item information
        const validUpcs = new Map(
          upcValidationResponse.data.map(item => [item.upc, item])
        );

        // Check for invalid UPCs
        const invalidUpcs = uniqueUpcs.filter(upc => !validUpcs.has(upc));
        if (invalidUpcs.length > 0) {
          throw new Error(`Invalid UPCs found: ${invalidUpcs.join(', ')}`);
        }

        // Convert pivoted data back to individual rows for processing
        const newRows = [];
        parsed.data
          .filter(row => Object.values(row).some(value => value !== ''))
          .forEach((row, rowIndex) => {
            // Validate required base fields
            const requiredFields = ['region', 'division', 'class', 'channel', 'upc'];
            const missingFields = requiredFields.filter(field => !row[field]);
            if (missingFields.length > 0) {
              throw new Error(`Row ${rowIndex + 1}: Missing required fields: ${missingFields.join(', ')}`);
            }
            if (!row.upc || row.upc.length < 5) {
              throw new Error(`Row ${rowIndex + 1}: UPC must be at least 5 characters long`);
            }
            const itemInfo = validUpcs.get(row.upc);
            if (!itemInfo) {
              throw new Error(`Row ${rowIndex + 1}: Invalid UPC ${row.upc}`);
            }
            
            monthColumns.forEach(monthColumn => {
              let date;
              try {
                date = parseDateSafely(monthColumn);
                
                if (isNaN(date.getTime())) {
                  throw new Error('Invalid date');
                }
                
                const today = new Date();
                // Set today to first of current month for proper comparison
                const firstOfCurrentMonth = new Date(today.getFullYear(), today.getMonth(), 1);
                
                // For debugging - log the dates being compared
                // console.log('Comparing:', date, 'with first of current month:', firstOfCurrentMonth);
                
                if (date < firstOfCurrentMonth) {
                  throw new Error('Date must be current month or later');
                }
              } catch (err) {
                throw new Error(`Row ${rowIndex + 1}, Month ${monthColumn}: ${err.message}`);
              }
              
              const qty = parseFloat(row[monthColumn]) || 0;
              if (!qty) return; // skip zero or empty
              
              newRows.push({
                region: row.region,
                division: row.division,
                class: row.class,
                channel: row.channel,
                customer: row.customer || null,
                upc: row.upc,
                date: monthColumn,
                qty: qty,
                lifestatus: itemInfo.lifestatus,
                productspecification: itemInfo.productspecification,
                producttype: itemInfo.producttype,
                color: itemInfo.color,
                size: itemInfo.size,
                year: date.getFullYear(),
                month: date.toLocaleString('default', { month: 'long' }),
                quarter: `Q${Math.floor((date.getMonth() / 3) + 1)}`,
                id: Date.now() + Math.random(),
                node_code: buildForecastNode({
                  region: row.region,
                  division: row.division,
                  class: row.class,
                  channel: row.channel,
                  customer: row.customer || null,
                  upc: row.upc
                }),
                upload_date: new Date().toISOString(),
                uploaded_by: (userObj && userObj.email) || '',
                pendingChanges: false // Don't set pendingChanges on new rows
              });
            });
          });

        // Merge with existing rows
        setRows(prevRows => {
          // Create a map of existing rows by their unique key
          const existingRowsMap = new Map();
          prevRows.forEach(row => {
            const rowKey = `${row.region}||${row.division}||${row.class}||${row.channel}||${row.customer || ''}||${row.productspecification}||${row.upc}||${row.date}`;
            existingRowsMap.set(rowKey, row);
          });
          
          // Merge new rows with existing ones, replacing where keys match
          newRows.forEach(newRow => {
            const rowKey = `${newRow.region}||${newRow.division}||${newRow.class}||${newRow.channel}||${newRow.customer || ''}||${newRow.productspecification}||${newRow.upc}||${newRow.date}`;
            existingRowsMap.set(rowKey, newRow);
          });
          
          return Array.from(existingRowsMap.values());
        });

        // Update pivoted rows directly to preserve original values
        setPivotedRows(prevPivotedRows => {
          const newPivotedRows = [...prevPivotedRows];
          
          // Group new rows by their key
          const newRowsByKey = {};
          newRows.forEach(row => {
            const groupKey = `${row.region}||${row.division}||${row.class}||${row.channel}||${row.customer || ''}||${row.productspecification}||${row.upc}`;
            if (!newRowsByKey[groupKey]) {
              newRowsByKey[groupKey] = [];
            }
            newRowsByKey[groupKey].push(row);
          });

          // Check if any existing pivoted rows need to be updated or if new ones need to be added
          Object.entries(newRowsByKey).forEach(([groupKey, groupRows]) => {
            const existingRow = newPivotedRows.find(row => row.key === groupKey);
            
            if (existingRow) {
              // Update existing row with new quantities
              groupRows.forEach(row => {
                // console.log('Processing row with date:', row.date, 'qty:', row.qty);
                
                // Parse the date safely to avoid timezone issues
                let date;
                try {
                  date = parseDateSafely(row.date);
                } catch (err) {
                  console.error('Failed to parse date:', row.date, err);
                  return; // Skip this row if date parsing fails
                }
                
                const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
                // console.log('Parsed date:', date, 'monthKey:', monthKey);
                
                // Ensure originalValues exists
                if (!existingRow.originalValues) {
                  existingRow.originalValues = {};
                }
                
                // Store the original value before updating (only if not already stored)
                // This is the key fix - we need to preserve the original value from the database
                if (existingRow.originalValues[monthKey] === undefined) {
                  existingRow.originalValues[monthKey] = existingRow[monthKey] || 0;
                }
                
                // Replace the value (don't add to existing)
                const newValue = row.qty;
                const originalValue = existingRow.originalValues[monthKey] || 0;
                
                // console.log(`Upload: ${monthKey} - Original: ${originalValue}, New: ${newValue}, Current: ${existingRow[monthKey]}`);
                
                existingRow[monthKey] = newValue;
                
                // Mark as pending change if different from original
                if (!existingRow.pendingChanges) {
                  existingRow.pendingChanges = {};
                }
                
                if (newValue !== originalValue) {
                  existingRow.pendingChanges[monthKey] = true;
                  existingRow.changeType = 'modified';
                  // console.log(`Marked as pending change: ${monthKey} - ${newValue} !== ${originalValue}`);
                } else {
                  // Remove from pending changes if value matches original
                  delete existingRow.pendingChanges[monthKey];
                }
              });
            } else {
              // Create new pivoted row
              const today = new Date();
              const monthKeys = [];
              for (let i = 0; i < 12; i++) {
                const date = new Date(today.getFullYear(), today.getMonth() + i, 1);
                const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
                monthKeys.push(monthKey);
              }

              const newPivotedRow = {
                key: groupKey,
                region: groupRows[0].region,
                division: groupRows[0].division,
                class: groupRows[0].class,
                channel: groupRows[0].channel,
                customer: groupRows[0].customer || '',
                productspecification: groupRows[0].productspecification,
                upc: groupRows[0].upc,
                pendingChanges: {},
                originalValues: {},
                changeType: 'added',
                // Initialize all month columns with 0
                ...monthKeys.reduce((acc, month) => {
                  acc[month] = 0;
                  return acc;
                }, {})
              };

              // Add quantities for each month
              groupRows.forEach(row => {
                // console.log('Processing row with date:', row.date, 'qty:', row.qty);
                
                // Parse the date safely to avoid timezone issues
                let date;
                try {
                  date = parseDateSafely(row.date);
                } catch (err) {
                  console.error('Failed to parse date:', row.date, err);
                  return; // Skip this row if date parsing fails
                }
                
                const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
                // console.log('Parsed date:', date, 'monthKey:', monthKey);
                newPivotedRow[monthKey] = row.qty; // Set the new value
                newPivotedRow.pendingChanges[monthKey] = true;
                newPivotedRow.originalValues[monthKey] = 0; // Original value is 0 for new rows
              });

              newPivotedRows.push(newPivotedRow);
            }
          });

          setShowUploadModal(false);
          
          // Force grid refresh
          setTimeout(() => {
            if (gridApi.current && gridApi.current.api) {
              gridApi.current.api.refreshCells();
              gridApi.current.api.redrawRows();
            }
          }, 100);
          
          return newPivotedRows;
        });
      } catch (err) {
        message.error('Failed to upload: ' + err.message);
      }
      setUploading(false);
    };
    reader.readAsText(file);
    return false; // Prevent default upload
  };

  const handleDeleteSelectedRows = () => {
    const selectedKeys = new Set(selectedRows.map(row => row.key));

    // Get existing changes from localStorage
    const changes = JSON.parse(localStorage.getItem('pendingRows') || '{}');

    // Add deleted rows to changes
    selectedRows.forEach(row => {
      const rowKey = `${row.key}`;
      if (!changes[rowKey]) {
        changes[rowKey] = {};
      }
      changes[rowKey]._deleted = {
        old: row,
        new: null,
        timestamp: new Date().toISOString(),
        user: userObj?.email || 'unknown'
      };
      // console.log('handleDeleteSelectedRows - added deleted row:', rowKey, changes[rowKey]._deleted);
    });

    // Update the rows state
    localStorage.setItem('pendingRows', JSON.stringify(changes));
    // console.log('handleDeleteSelectedRows - final changes saved to localStorage:', changes);
    setPivotedRows(prev => prev.filter(row => !selectedKeys.has(row.key)));
    setPendingRows(changes);
    setSelectedRows([]);
  };

  const handleDownloadTemplate = () => {
    // Create template with month columns structure
    const baseHeaders = [
      'region', 'division', 'class', 'channel', 'customer', 'productspecification', 'upc'
    ];

    // Add the next 12 months as columns
    const today = new Date();
    const monthColumns = [];
    for (let i = 0; i < 12; i++) {
      const date = new Date(today.getFullYear(), today.getMonth() + i, 1);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
      const monthLabel = date.toLocaleString('default', { month: 'short', year: 'numeric' });
      monthColumns.push(monthLabel);
    }

    const headers = [...baseHeaders, ...monthColumns];

    // Create example data row
    const exampleData = [
      baseHeaders.map(header => {
        switch (header) {
          case 'region': return 'North America';
          case 'division': return 'Apparel';
          case 'class': return 'Shirts';
          case 'channel': return 'Wholesale';
          case 'customer': return 'Example Customer';
          case 'productspecification': return 'Example Product';
          case 'upc': return '123456789012';
          default: return '';
        }
      }).concat(monthColumns.map(() => '100')) // Example quantity of 100 for each month
    ];

    const template = Papa.unparse({
      fields: headers,
      data: exampleData
    });

    const blob = new Blob([template], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'external_demand_plan_template.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const saveExternalPlans = async () => {
    setLoading(true);
    try {
      // Convert pivoted data back to individual rows for saving
      const rowsToSave = [];
      pivotedRows.forEach(pivotedRow => {
        const today = new Date();
        for (let i = 0; i < 12; i++) {
          const date = new Date(today.getFullYear(), today.getMonth() + i, 1);
          const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
          const qty = pivotedRow[monthKey] || 0;
          
          // Save ALL rows, including unchanged ones with 0 quantities
          rowsToSave.push({
            region: pivotedRow.region,
            division: pivotedRow.division,
            class: pivotedRow.class,
            channel: pivotedRow.channel,
            customer: pivotedRow.customer,
            upc: pivotedRow.upc,
            date: monthKey,
            qty: qty,
            node_code: buildForecastNode(pivotedRow),
            upload_date: new Date().toISOString(),
            uploaded_by: (userObj && userObj.email) || ''
          });
        }
      });

      // console.log('Saving rows to BigQuery:', rowsToSave.length, 'total rows');
      // console.log('Sample rows:', rowsToSave.slice(0, 3));

      const result = await api.bigQueryLoadToTableOnCall({
        datasetId: 'forecast',
        tableId: 'external_demand_plans',
        rows: rowsToSave,
        replace: true
      });
      if (result.data && result.data.success) {
        message.success('External demand plan saved successfully!');
        // Clear pending changes from localStorage
        localStorage.removeItem('pendingRows');
        // Refetch the data from BigQuery
        await fetchExternalPlans({ reset: true });
        setShowPreviewModal(false);
      } else {
        throw new Error(result.data?.error || 'Failed to save data.');
      }
    } catch (error) {
      message.error('Failed to save: ' + error.message);
    }
    setLoading(false);
  };

  const handleResetChanges = async () => {
    localStorage.removeItem('pendingRows');
    setPendingRows({});
    // Clear any pending changes from the current rows
    setRows(prevRows => prevRows.map(row => ({
      ...row,
      pendingChanges: false
    })));
    // Clear pending changes from pivoted rows and restore original values
    setPivotedRows(prevRows => prevRows.map(row => {
      const resetRow = { ...row, pendingChanges: {} };
      // Restore original values for all month columns
      if (row.originalValues) {
        const today = new Date();
        for (let i = 0; i < 12; i++) {
          const date = new Date(today.getFullYear(), today.getMonth() + i, 1);
          const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
          if (row.originalValues[monthKey] !== undefined) {
            resetRow[monthKey] = row.originalValues[monthKey];
          }
        }
      }
      return resetRow;
    }));
    await fetchExternalPlans({ reset: true });
    message.success('Changes have been reset');
  };

  const buildRowKey = (row) => {
    if (!row.node_code) {
      row.node_code = buildForecastNode(row);
    }
    return `${row.node_code}_${row.upc}`;
  };

  const fetchExternalPlans = async ({ reset = false }) => {
    setLoading(true);
    try {
      const response = await api.bigQueryRunQueryOnCall({
        options: {
          query: `
            SELECT
              e.node_code,
              e.region,
              e.division,
              e.class,
              e.channel,
              e.customer,
              i.lifestatus,
              i.productspecification,
              i.producttype,
              i.color,
              i.size,
              e.upc,
              e.date,
              e.qty
            FROM
              \`hj-reporting.forecast.external_demand_plans\` e
            left join \`hj-reporting.items.items_netsuite\` i on e.upc = i.upc
            WHERE
              date between CURRENT_DATE() and CURRENT_DATE() + INTERVAL 12 MONTH
            ORDER BY
              date
        `
        }
      });
      let formattedData = response.data.map((row, i) => {
        const date = new Date(row.date.value);
        return {
          key: buildRowKey(row),
          ...row,
          node_code: buildForecastNode(row),
          date: new Date(row.date.value),
          month: date.toLocaleString('default', { month: 'long' }),
          year: date.getFullYear(),
          quarter: `Q${Math.floor((date.getMonth() / 3) + 1)}`
        };
      });
      
      // Only apply pending changes if there are actually pending changes
      if (Object.keys(pendingRows).length > 0 && !reset) {
        for (let key in pendingRows) {
          if (pendingRows[key]._deleted) {
            formattedData = formattedData.filter(r => r.key !== key);
            continue;
          }
          const row = formattedData.find(r => r.key === key);
          if (row) {  
            row.qty = pendingRows[key].qty;
            row.pendingChanges = true;
          }
        }
      }

      setRows(formattedData || []);
      
      // Pivot the data for display
      const pivoted = pivotData(formattedData || []);
      
      // Apply pending changes to the pivoted rows if there are any and we're not resetting
      if (Object.keys(pendingRows).length > 0 && !reset) {
        const updatedPivoted = pivoted.map(row => {
          const pendingData = pendingRows[row.key];
          if (pendingData && !pendingData._deleted) {
            // Apply pending changes to this row
            const updatedRow = { ...row };
            
            // Update values that have pending changes
            Object.keys(pendingData.pendingChanges || {}).forEach(field => {
              if (field !== '_deleted') {
                updatedRow[field] = pendingData[field] || 0;
              }
            });
            
            // Preserve original values and pending changes
            updatedRow.originalValues = pendingData.originalValues || row.originalValues || {};
            updatedRow.pendingChanges = pendingData.pendingChanges || {};
            updatedRow.changeType = pendingData.changeType || 'modified';
            
            return updatedRow;
          }
          return row;
        });
        
        setPivotedRows(updatedPivoted);
      } else {
        setPivotedRows(pivoted);
      }
    } catch (error) {
      message.error('Failed to load external demand plan from BigQuery' + error.message);
      setRows([]);
      setPivotedRows([]);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchExternalPlans({});
  }, []);

  const onCellValueChanged = (params) => {
    // console.log('onCellValueChanged');
    const { data, colDef, oldValue, newValue } = params;
    // console.log('params:', params);
    // console.log('originalValues:', data.originalValues);
    // console.log('field:', colDef.field);
    // console.log('original value for field:', data.originalValues?.[colDef.field]);
    
    setPivotedRows(prevRows => prevRows.map(row => {
      if (row.key === data.key) {
        const updatedRow = { ...row, [colDef.field]: newValue };
        if (!updatedRow.pendingChanges) {
          updatedRow.pendingChanges = {};
        }
        if (!updatedRow.originalValues) {
          updatedRow.originalValues = {};
        }
        
        const originalValue = updatedRow.originalValues[colDef.field] || 0;
        // console.log('comparing newValue:', newValue, 'with originalValue:', originalValue);
        
        // If the new value matches the original, remove it from pending changes
        if (newValue === originalValue) {
          delete updatedRow.pendingChanges[colDef.field];
          // If no more pending changes, reset change type
          if (Object.keys(updatedRow.pendingChanges).length === 0) {
            updatedRow.changeType = 'modified';
          }
        } else {
          // Add to pending changes if it differs from original
          updatedRow.pendingChanges[colDef.field] = true;
          updatedRow.changeType = 'modified';
        }
        
        return updatedRow;
      }
      return row;
    }));
  };

  // Save pending changes to localStorage whenever pivotedRows change
  useEffect(() => {
    // Get existing changes from localStorage to preserve deleted rows
    const existingChanges = JSON.parse(localStorage.getItem('pendingRows') || '{}');
    const pendingChanges = {};
    
    // Add modified and added rows from pivotedRows
    pivotedRows.forEach(row => {
      if (row.pendingChanges && Object.keys(row.pendingChanges).length > 0) {
        // Only include fields that actually differ from original values
        const actualChanges = {};
        Object.keys(row.pendingChanges).forEach(field => {
          const originalValue = row.originalValues?.[field] || 0;
          const currentValue = row[field] || 0;
          if (currentValue !== originalValue) {
            actualChanges[field] = true;
          }
        });
        
        // Only save if there are actual changes
        if (Object.keys(actualChanges).length > 0) {
          pendingChanges[row.key] = {
            ...row,
            pendingChanges: actualChanges,
            originalValues: row.originalValues || {}, // Preserve original values
            timestamp: new Date().toISOString(),
            user: userObj?.email || 'unknown'
          };
        }
      }
    });
    
    // Preserve deleted rows from existing changes
    Object.entries(existingChanges).forEach(([key, data]) => {
      if (data._deleted) {
        pendingChanges[key] = data;
      }
    });
    
    if (Object.keys(pendingChanges).length > 0) {
      localStorage.setItem('pendingRows', JSON.stringify(pendingChanges));
      setPendingRows(pendingChanges);
    } else {
      // Clear localStorage if no pending changes
      localStorage.removeItem('pendingRows');
      setPendingRows({});
    }
  }, [pivotedRows, userObj?.email]);

  // Function to get all changes including deleted rows
  const getAllChanges = () => {
    const changes = [];
    
    // console.log('getAllChanges - pivotedRows:', pivotedRows.length);
    // console.log('getAllChanges - pendingRows:', pendingRows);
    
    // Add modified and added rows
    pivotedRows.forEach(row => {
      if (row.pendingChanges && Object.keys(row.pendingChanges).length > 0) {
        // console.log('Adding modified/added row:', row.key, row.changeType);
        changes.push(row);
      }
    });
    
    // Add deleted rows from localStorage
    const pendingRowsData = JSON.parse(localStorage.getItem('pendingRows') || '{}');
    // console.log('getAllChanges - pendingRowsData from localStorage:', pendingRowsData);
    
    Object.entries(pendingRowsData).forEach(([key, data]) => {
      if (data._deleted) {
        // console.log('Found deleted row:', key, data._deleted);
        const deletedRow = {
          ...data._deleted.old,
          key: key,
          changeType: 'deleted',
          pendingChanges: { _deleted: true }
        };
        changes.push(deletedRow);
      }
    });
    
    // console.log('getAllChanges - total changes:', changes.length);
    // console.log('getAllChanges - changes:', changes);
    
    return changes;
  };

  return <>
    <Row>
      <Col span={24}>
        <Dropdown
          disabled={selectedRows.length === 0 || loading || uploading}
          menu={{
            items: [
              {
                key: 'download',
                label: 'Download Selected',
                onClick: handleDownloadSelected
              },
              {
                key: 'delete',
                label: 'Delete Selected',
                onClick: handleDeleteSelectedRows,
              }
            ]
          }}
          placement="bottomLeft"
          trigger={["click"]}
        >
          <Tooltip title={() => {
            if (selectedRows.length === 0) {
              return 'Please select rows to perform bulk actions';
            }
            if (loading || uploading) {
              return 'Please wait for the grid to load or the upload to complete';
            }
            return 'Bulk actions for selected rows';
          }}>
            <Button style={{ marginRight: 8 }} icon={<MenuOutlined />} />
          </Tooltip>
        </Dropdown>
        <Button
          disabled={loading || uploading}
          onClick={() => handleDownloadSelected()} icon={<DownloadOutlined />} />
        <Button
          disabled={loading || uploading}
          icon={<UploadOutlined />}
          onClick={() => setShowUploadModal(true)}
          style={{ marginLeft: 8 }}
        />
        <Button
          disabled={loading || getAllChanges().length === 0}
          icon={<SaveOutlined />}
          type="primary"
          onClick={() => setShowPreviewModal(true)}
          style={{ marginLeft: 8 }}
        />
        <Button
          onClick={handleResetChanges}
          disabled={loading || uploading || getAllChanges().length === 0}
          style={{ marginLeft: 8 }}
          icon={<UndoOutlined />}
        />
      </Col>
      <Col span={24} style={{ height: 400, width: '100%' }}>
        <AgGridReact
          ref={gridApi}
          rowData={pivotedRows}
          columnDefs={columns}
          loading={loading}
          onCellValueChanged={onCellValueChanged}
          editable={true}
          rowSelection={{
            mode: 'multiple',
            checkboxes: true,
            headerCheckbox: true,
            selectAll: 'filtered'
          }}
          onSelectionChanged={(event) => {
            setSelectedRows(event.api.getSelectedRows());
          }}
          autoSizeStrategy={{
            type: 'fitCellContents',
            defaultMinWidth: 80,
          }}
          sideBar={{
            toolPanels: [
              {
                id: 'columns',
                labelDefault: 'Columns',
                labelKey: 'columns',
                iconKey: 'columns',
                toolPanel: 'agColumnsToolPanel',
              },
              {
                id: 'filters',
                labelDefault: 'Filters',
                labelKey: 'filters',
                iconKey: 'filter',
                toolPanel: 'agFiltersToolPanel',
              }
            ],
          }}
          groupDefaultExpanded={-1}
          cellSelection={true}
          suppressAggFuncInHeader={true}
          groupDisplayType="groupRows"
          onGridReady={(params) => {
            params.api.sizeColumnsToFit();
          }}
          pagination={true}
          paginationPageSize={50}
        />
      </Col>
    </Row>

    <Modal
      title="Upload External Demand Plan"
      open={showUploadModal}
      onCancel={() => setShowUploadModal(false)}
      footer={null}
    >
      <div style={{ marginBottom: 16 }}>
        <p>Required base columns: region, division, class, channel, upc</p>
        <p>Optional base columns: customer, productspecification</p>
        <p>Month columns: Use format YYYY-MM-DD (e.g., 2024-01-01, 2024-02-01)</p>
        <p>Each month column should contain the quantity for that month.</p>
        <p>Rows with zero or empty quantities will be ignored.</p>
      </div>
      <Upload
        accept=".csv"
        showUploadList={false}
        beforeUpload={handleUploadFile}
        disabled={uploading || loading}
      >
        <Button type="primary" loading={uploading || loading}>Select CSV File</Button>
      </Upload>
      <Button type="primary" loading={uploading || loading} onClick={handleDownloadTemplate}>Download Template</Button>
    </Modal>
    <Modal
      title="Preview Changes"
      open={showPreviewModal}
      onCancel={() => setShowPreviewModal(false)}
      footer={null}
      width={1400}
    >
      <div style={{ height: 400, width: '100%', marginBottom: 16 }}>
        <AgGridReact
          rowData={getAllChanges()}
          columnDefs={generatePreviewColumns()}
          loading={loading}
          autoSizeStrategy={{
            type: 'fitCellContents',
          }}
        />
      </div>
      <Button type="primary" loading={uploading || loading} onClick={saveExternalPlans}>Save Changes</Button>
    </Modal>
  </>;
};
export default ManageExternalDemandPlans;