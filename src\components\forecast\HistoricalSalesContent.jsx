import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Form, Select, Button, Row, Col, Tabs, Spin, message, DatePicker, Checkbox, Tooltip, Popconfirm, Modal, Input, Switch } from 'antd';
import { SaveOutlined, DeleteOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import { AgGridReact } from 'ag-grid-react';
import { AgCharts } from 'ag-charts-react';
import { themeBalham } from 'ag-grid-community';
import { api, db } from '../../pages/firebase';
import { useUser } from '../../contexts/UserContext';
import { onSnapshot, doc, collection, addDoc, setDoc, deleteDoc, query, where, or } from 'firebase/firestore';
import dayjs from 'dayjs';


const { RangePicker } = DatePicker;

const HistoricalSalesContent = ({
  initialSelection = { upcs: [], forecastNodes: [], dateRange: '365' },
  rowData = [],
  isModal = false,
  autoSearch = false
}) => {
  const { userData } = useUser();
  const gridRef = useRef(null);
  const autoSearchExecuted = useRef(false);
  const [loading, setLoading] = useState(false);
  const [loadingFilters, setLoadingFilters] = useState(true);
  const [salesData, setSalesData] = useState([]);
  const [chartData, setChartData] = useState([]);
  const [dateRange, setDateRange] = useState(initialSelection.dateRange || '365');
  const [customDateRange, setCustomDateRange] = useState(null);
  const [lastFetchTime, setLastFetchTime] = useState(0);

  // Grouping state
  const [grouping, setGrouping] = useState({
    timeperiod: 'month',
    primary: 'product',
    secondary: 'color'
  });

  // Chart view state
  const [chartView, setChartView] = useState('yoy'); // 'yoy' or 'trendline'

  // Saved views state
  const [savedChartViews, setSavedChartViews] = useState([]);
  const [savedTableViews, setSavedTableViews] = useState([]);
  const [selectedChartViewId, setSelectedChartViewId] = useState(null);
  const [selectedTableViewId, setSelectedTableViewId] = useState(null);

  // Debug logging for selected view changes (removed to reduce console noise)
  // useEffect(() => {
  //   console.log('Selected chart view ID changed to:', selectedChartViewId);
  // }, [selectedChartViewId]);

  // useEffect(() => {
  //   console.log('Selected table view ID changed to:', selectedTableViewId);
  // }, [selectedTableViewId]);
  const [applyingView, setApplyingView] = useState(false);
  const [saveViewModalVisible, setSaveViewModalVisible] = useState(false);
  const [saveViewMode, setSaveViewMode] = useState('table'); // only 'table' now
  const [newViewName, setNewViewName] = useState('');
  const [newViewIsPublic, setNewViewIsPublic] = useState(false);
  const [activeTab, setActiveTab] = useState('chart');

  // Selection state - starts with initial selection but user can modify
  const [selection, setSelection] = useState({
    upcs: initialSelection.upcs || [],
    forecastNodes: initialSelection.forecastNodes || [],
    dateRange: initialSelection.dateRange || '365',
    products: [],
    divisions: [],
    colors: [],
    sizes: [],
    categories: [],
    lifeStatuses: [],
    specifications: [],
    includeForecast: initialSelection.includeForecast || true
  });

  // Date range options
  const dateRangeOptions = [
    { value: '30', label: 'L30D' },
    { value: '90', label: 'L90D' },
    { value: '180', label: 'L6M' },
    { value: '365', label: 'L1Y' },
    { value: '730', label: 'L2Y' },
    { value: 'custom', label: 'Custom' }
  ];

  // Grouping options
  const timePeriodOptions = [
    { value: 'date', label: 'Date' },
    { value: 'week', label: 'Week' },
    { value: 'month', label: 'Month' },
    { value: 'year', label: 'Year' }
  ];

  const groupByOptions = [
    { value: 'upc', label: 'UPC' },
    { value: 'product', label: 'Product' },
    { value: 'forecast_node', label: 'Forecast Node' },
    { value: 'division', label: 'Division' },
    { value: 'color', label: 'Color' },
    { value: 'size', label: 'Size' },
    { value: 'category', label: 'Category' },
    { value: 'specification', label: 'Specification' }
  ];

  const secondaryGroupOptions = useMemo(() => [
    { value: 'none', label: '-- Select Secondary Grouping --' },
    ...groupByOptions.filter(option => option.value !== grouping.primary)
  ], [grouping.primary]);

  // Chart view options
  const chartViewOptions = [
    { value: 'yoy', label: 'Year over Year' },
    { value: 'trendline', label: 'Trendline' }
  ];

  // State for filter options
  const [filterOptions, setFilterOptions] = useState({
    upcs: [],
    forecastNodes: [],
    forecastNodeColors: new Map(),
    products: [],
    divisions: [],
    colors: [],
    sizes: [],
    categories: [],
    lifeStatuses: [],
    specifications: []
  });

  // Fetch filter options from items.netsuite table
  const fetchFilterOptions = useCallback(async () => {
    setLoadingFilters(true);
    try {
      // Fetch forecast nodes from forecast.forecast_nodes table
      const forecastNodeQuery = `
        SELECT DISTINCT code as forecast_node, color as forecast_node_color
        FROM \`hj-reporting.forecast.forecast_nodes\`
        WHERE code IS NOT NULL AND code != ''
        ORDER BY code
      `;

      // Fetch product attributes
      const productAttrsQuery = `
        SELECT DISTINCT
          upc,
          CONCAT(upc, ' - ', COALESCE(producttype, ''), ' ', COALESCE(color, ''), ' ', COALESCE(size, '')) as label,
          producttype as product,
          productdivision as division,
          color,
          size,
          productcategory as category,
          lifestatus,
          productspecification as specification
        FROM \`hj-reporting.items.items_netsuite\`
        WHERE upc IS NOT NULL
        LIMIT 1000
      `;

      const [{ data: forecastNodeResult }, { data: productAttrsResult }] = await Promise.all([
        api.bigQueryRunQueryOnCall({ options: { query: forecastNodeQuery } }),
        api.bigQueryRunQueryOnCall({ options: { query: productAttrsQuery } })
      ]);

      // Process UPC results
      const upcs = (productAttrsResult || []).map(row => [row.upc, row.label]);

      // Process forecast node results and create color mapping
      const forecastNodes = (forecastNodeResult || []).map(row => row.forecast_node);
      const forecastNodeColors = new Map();
      (forecastNodeResult || []).forEach(row => {
        if (row.forecast_node && row.forecast_node_color) {
          forecastNodeColors.set(row.forecast_node, row.forecast_node_color);
        }
      });

      // Process product attribute results
      const products = [...new Set((productAttrsResult || []).map(row => row.product).filter(Boolean))].sort();
      const divisions = [...new Set((productAttrsResult || []).map(row => row.division).filter(Boolean))].sort();
      const colors = [...new Set((productAttrsResult || []).map(row => row.color).filter(Boolean))].sort();
      const sizes = [...new Set((productAttrsResult || []).map(row => row.size).filter(Boolean))].sort();
      const categories = [...new Set((productAttrsResult || []).map(row => row.category).filter(Boolean))].sort();
      const lifeStatuses = [...new Set((productAttrsResult || []).map(row => row.lifestatus).filter(Boolean))].sort();
      const specifications = [...new Set((productAttrsResult || []).map(row => row.specification).filter(Boolean))].sort();

      setFilterOptions({
        upcs,
        forecastNodes,
        forecastNodeColors,
        products,
        divisions,
        colors,
        sizes,
        categories,
        lifeStatuses,
        specifications
      });
    } catch (error) {
      console.error('Error fetching filter options:', error);
      message.error('Failed to load filter options. Please refresh the page.');
    } finally {
      setLoadingFilters(false);
    }
  }, []);

  // Fetch saved views from Firestore - only when needed
  const fetchViews = useCallback(() => {
    if (!userData?.id) return () => {}; // Don't fetch if user not loaded
    
    let chartViewsUnsub = () => {};
    let tableViewsUnsub = () => {};
    
    try {
      // Fetch chart views - only if chart tab is active or we need chart views
      if (activeTab === 'chart' || !isModal) {
        const chartQuery = query(
          collection(db, 'historicalSalesChartViews'),
          or(
            where('createdBy', '==', userData.id), // User's own views (private)
            where('isPublic', '==', true) // All public views
          )
        );
        chartViewsUnsub = onSnapshot(chartQuery, (snap) => {
          const views = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }))
            .sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
          setSavedChartViews(views);
        });
      }

      // Fetch table views - only if table tab is active
      if (activeTab === 'table') {
        const tableQuery = query(
          collection(db, 'historicalSalesTableViews'),
          or(
            where('createdBy', '==', userData.id), // User's own views (private)
            where('isPublic', '==', true) // All public views
          )
        );
        tableViewsUnsub = onSnapshot(tableQuery, (snap) => {
          const views = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }))
            .sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
          setSavedTableViews(views);
        });
      }

      return () => {
        chartViewsUnsub();
        tableViewsUnsub();
      };
    } catch (error) {
      console.error('Error fetching saved views:', error);
      message.error('Failed to load saved views');
      return () => {};
    }
  }, [userData?.id, activeTab, isModal]);

  // Load filter options when component mounts
  useEffect(() => {
    fetchFilterOptions();
  }, [fetchFilterOptions]);

  // Set up view listeners when userData or activeTab changes
  useEffect(() => {
    if (userData?.id) {
      const unsubscribe = fetchViews();
      return unsubscribe; // Clean up listeners when userData or activeTab changes
    }
  }, [userData?.id, activeTab, fetchViews]);

  // Calculate date range based on selection
  const getDateRange = useCallback(() => {
    if (dateRange === 'custom' && customDateRange) {
      return {
        start: customDateRange[0].format('YYYY-MM-DD'),
        end: customDateRange[1].format('YYYY-MM-DD')
      };
    }

    const days = parseInt(dateRange);
    const endDate = dayjs();
    const startDate = endDate.subtract(days, 'day');

    return {
      start: startDate.format('YYYY-MM-DD'),
      end: endDate.format('YYYY-MM-DD')
    };
  }, [dateRange, customDateRange]);

  // Forward declare fetchSalesData to avoid circular dependency
  const fetchSalesDataRef = useRef();


  // Load chart view function
  const handleLoadChartView = useCallback(async (viewId) => {
    try {
      if (!userData?.id) {
        message.warning('Please login to load views');
        return;
      }
      if (!savedChartViews.length) {
        message.warning('No chart views found');
        return;
      }
      if (!viewId) {
        // Clear chart view selection and reset to default state
        setSelectedChartViewId(null);
        setSelection({
          upcs: initialSelection.upcs || [],
          forecastNodes: initialSelection.forecastNodes || [],
          dateRange: initialSelection.dateRange || '365',
          products: [],
          divisions: [],
          colors: [],
          sizes: [],
          categories: [],
          lifeStatuses: [],
          specifications: [],
          includeForecast: initialSelection.includeForecast !== undefined ? initialSelection.includeForecast : true
        });
        setDateRange(initialSelection.dateRange || '365');
        setCustomDateRange(null);
        setGrouping({ timeperiod: 'month', primary: 'product', secondary: 'color' });
        setChartView('yoy');
        // Optionally clear last used chart view in user doc
        if (!isModal && userData?.id) {
          await setDoc(doc(db, 'users', userData.id), {
            lastHistoricalSalesChartView: null
          }, { merge: true });
        }
        setApplyingView(false);
        return;
      }
      setApplyingView(true);
      const view = savedChartViews.find(v => v.id === viewId);

      if (!view || !view.chartViewState) {
        message.warning('Chart view not found or invalid');
        setApplyingView(false);
        return;
      }

      const state = view.chartViewState;

      // Apply the saved state
      setSelection({
        ...state.selection,
        includeForecast: state.includeForecast !== undefined ? state.includeForecast : true
      });

      setDateRange(state.dateRange || '365');

      if (state.customDateRange) {
        setCustomDateRange([dayjs(state.customDateRange[0]), dayjs(state.customDateRange[1])]);
      } else {
        setCustomDateRange(null);
      }

      setGrouping(state.grouping || { timeperiod: 'month', primary: 'product', secondary: 'color' });
      setChartView(state.chartView || 'yoy');

      setSelectedChartViewId(viewId);

      // Save as last used chart view if not modal
      if (!isModal && userData?.id) {
        await setDoc(doc(db, 'users', userData.id), {
          lastHistoricalSalesChartView: viewId
        }, { merge: true });
      }

      // Auto-fetch data with new selection
      setTimeout(() => {
        if (fetchSalesDataRef.current) {
          fetchSalesDataRef.current();
        }
        setApplyingView(false);
      }, 100);
    } catch (error) {
      console.error('Error loading chart view:', error);
      message.error('Failed to load chart view');
      setApplyingView(false);
    }
  }, [savedChartViews, userData, isModal]);

  // Delete chart view function
  const handleDeleteChartView = useCallback(async (viewId) => {
    try {
      const view = savedChartViews.find(v => v.id === viewId);
      if (!view) return;

      // Check permissions - only creator or admin can delete
      if (view.createdBy !== userData?.id && !userData?.isAdmin) {
        message.error('You can only delete views you created');
        return;
      }

      await deleteDoc(doc(db, 'historicalSalesChartViews', viewId));

      if (selectedChartViewId === viewId) {
        setSelectedChartViewId(null);
      }

      // Clear from user's last used view if applicable
      if (!isModal && userData?.id && userData.lastHistoricalSalesChartView === viewId) {
        await setDoc(doc(db, 'users', userData.id), {
          lastHistoricalSalesChartView: null
        }, { merge: true });
      }

      message.success('Chart view deleted!');
    } catch (error) {
      console.error('Error deleting chart view:', error);
      message.error('Failed to delete chart view');
    }
  }, [savedChartViews, selectedChartViewId, userData, isModal]);

    // Save table view function
  const handleSaveTableView = useCallback(async (viewName, isPublic) => {
    try {
      if (!gridRef.current?.api) {
        message.warning('Table not ready for saving');
        return;
      }

      const api = gridRef.current.api;
      
      // Safely capture grid state with error handling
      let gridState = {};
      try {
        gridState = {
          columnState: api.getColumnState ? api.getColumnState() : [],
          columnGroupState: api.getColumnGroupState ? api.getColumnGroupState() : [],
          filterModel: api.getFilterModel ? api.getFilterModel() : {},
          sortModel: api.getSortModel ? api.getSortModel() : [],
          rowGroupCols: api.getRowGroupColumns ? api.getRowGroupColumns().map(c => c.getColId()) : [],
          pivotCols: api.getPivotColumns ? api.getPivotColumns().map(c => c.getColId()) : [],
          valueCols: api.getValueColumns ? api.getValueColumns().map(c => c.getColId()) : [],
          pivotMode: api.isPivotMode ? api.isPivotMode() : false,
          groupAggFiltering: api.isGroupAggFiltering ? api.isGroupAggFiltering() : false
        };
      } catch (gridError) {
        console.warn('Some grid state could not be captured:', gridError);
        // Continue with partial state
      }
      
      // Capture complete table view state
      const tableViewState = {
        selection,
        dateRange,
        customDateRange: customDateRange ? [customDateRange[0].format(), customDateRange[1].format()] : null,
        includeForecast: selection.includeForecast,
        gridState
      };

      const viewData = {
        name: viewName.trim(),
        tableViewState: JSON.parse(JSON.stringify(tableViewState)),
        isPublic,
        createdBy: userData?.id || 'unknown',
        createdByEmail: userData?.email || 'unknown', // Keep email for display purposes
        createdAt: new Date(),
        timestamp: Date.now()
      };

      const newDoc = await addDoc(collection(db, 'historicalSalesTableViews'), viewData);
      setSelectedTableViewId(newDoc.id);

      // Save as last used table view if not modal
      if (!isModal && userData?.id) {
        await setDoc(doc(db, 'users', userData.id), {
          lastHistoricalSalesTableView: newDoc.id
        }, { merge: true });
      }

      message.success('Table view saved!');
      return newDoc.id;
    } catch (error) {
      console.error('Error saving table view:', error);
      message.error('Failed to save table view');
    }
  }, [selection, dateRange, customDateRange, userData, isModal]);

  // Load table view function
  const handleLoadTableView = useCallback(async (viewId) => {
    try {
      setApplyingView(true);
      const view = savedTableViews.find(v => v.id === viewId);

      if (!view || !view.tableViewState) {
        message.warning('Table view not found or invalid');
        setApplyingView(false);
        return;
      }

      const state = view.tableViewState;

      // Apply selection and filters first
      setSelection({
        ...state.selection,
        includeForecast: state.includeForecast !== undefined ? state.includeForecast : true
      });

      setDateRange(state.dateRange || '365');

      if (state.customDateRange) {
        setCustomDateRange([dayjs(state.customDateRange[0]), dayjs(state.customDateRange[1])]);
      } else {
        setCustomDateRange(null);
      }

      setSelectedTableViewId(viewId);

      // Save as last used table view if not modal
      if (!isModal && userData?.id) {
        await setDoc(doc(db, 'users', userData.id), {
          lastHistoricalSalesTableView: viewId
        }, { merge: true });
      }

      // Fetch data first, then apply grid state
      setTimeout(async () => {
        if (fetchSalesDataRef.current) {
          await fetchSalesDataRef.current();
        }

        // Apply grid state after data is loaded
        setTimeout(() => {
          if (gridRef.current?.api && state.gridState) {
            const api = gridRef.current.api;

            try {
              // Apply column state
              if (state.gridState.columnState) {
                api.applyColumnState({
                  state: state.gridState.columnState,
                  applyOrder: true,
                  applySize: true,
                  applyVisible: true,
                  applySort: false // Apply sorting separately
                });
              }

              // Apply column group state
              if (state.gridState.columnGroupState) {
                api.setColumnGroupState(state.gridState.columnGroupState);
              }

              // Apply filters
              if (state.gridState.filterModel) {
                api.setFilterModel(state.gridState.filterModel);
              }

              // Apply sorting
              if (state.gridState.sortModel) {
                api.setSortModel(state.gridState.sortModel);
              }

              // Apply row grouping
              if (state.gridState.rowGroupCols) {
                api.setRowGroupColumns(state.gridState.rowGroupCols);
              }

              // Apply pivot columns
              if (state.gridState.pivotCols) {
                api.setPivotColumns(state.gridState.pivotCols);
              }

              // Apply value columns
              if (state.gridState.valueCols) {
                api.setValueColumns(state.gridState.valueCols);
              }

              // Apply pivot mode
              if (state.gridState.pivotMode !== undefined) {
                api.setPivotMode(state.gridState.pivotMode);
              }

              // Refresh to ensure everything is applied
              api.refreshClientSideRowModel();
              api.sizeColumnsToFit();
            } catch (gridError) {
              console.error('Error applying grid state:', gridError);
              message.warning('Some table settings could not be restored');
            }
          }

          setApplyingView(false);
        }, 500);
      }, 100);
    } catch (error) {
      console.error('Error loading table view:', error);
      message.error('Failed to load table view');
      setApplyingView(false);
    }
  }, [savedTableViews, userData, isModal]);

  // Delete table view function
  const handleDeleteTableView = useCallback(async (viewId) => {
    try {
      const view = savedTableViews.find(v => v.id === viewId);
      if (!view) return;

      // Check permissions - only creator or admin can delete
      if (view.createdBy !== userData?.id && !userData?.isAdmin) {
        message.error('You can only delete views you created');
        return;
      }

      await deleteDoc(doc(db, 'historicalSalesTableViews', viewId));

      if (selectedTableViewId === viewId) {
        setSelectedTableViewId(null);
      }

      // Clear from user's last used view if applicable
      if (!isModal && userData?.id && userData.lastHistoricalSalesTableView === viewId) {
        await setDoc(doc(db, 'users', userData.id), {
          lastHistoricalSalesTableView: null
        }, { merge: true });
      }

      message.success('Table view deleted!');
    } catch (error) {
      console.error('Error deleting table view:', error);
      message.error('Failed to delete table view');
    }
  }, [savedTableViews, selectedTableViewId, userData, isModal]);

  // Fetch historical sales data
  const fetchSalesData = useCallback(async () => {
    // Rate limiting - prevent multiple rapid requests
    const now = Date.now();
    if (now - lastFetchTime < 2000) { // 2 second cooldown
      message.warning('Please wait a moment before making another request');
      return;
    }
    setLastFetchTime(now);

    setLoading(true);
    try {
      const dateRange = getDateRange();

      // Validate and sanitize inputs to prevent SQL injection
      const sanitizeInput = (input) => {
        if (typeof input !== 'string') return '';
        // Remove any potentially dangerous characters and limit length leave space in for product type and color
        if (input.includes(' ')) {
          return input.replace(/[^a-zA-Z0-9_\-\. ]/g, '').substring(0, 50);
        }
        return input.replace(/[^a-zA-Z0-9_\-\.]/g, '').substring(0, 50);
      };

      // Additional validation for array lengths to prevent excessive queries
      if (selection.upcs.length > 100 || selection.forecastNodes.length > 50) {
        throw new Error('Too many items selected. Please reduce your selection.');
      }

      // Build WHERE clause for the query with input validation
      let whereConditions = [
        `b.account IN ('40100 Gross Revenue','40200 Discounts')`,
        `i.upc IS NOT NULL`
      ];
      if (selection.upcs.length > 0) {
        const sanitizedUpcs = selection.upcs.map(upc => sanitizeInput(upc)).filter(upc => upc.length > 0);
        if (sanitizedUpcs.length > 0) {
          whereConditions.push(`b.upc IN (${sanitizedUpcs.map(upc => `'${upc}'`).join(',')})`);
        }
      }

      if (selection.forecastNodes.length > 0) {
        const sanitizedNodes = selection.forecastNodes.map(node => sanitizeInput(node)).filter(node => node.length > 0);
        if (sanitizedNodes.length > 0) {
          whereConditions.push(`b.forecast_node IN (${sanitizedNodes.map(node => `'${node}'`).join(',')})`);
        }
      }

      // Add additional filters for product attributes
      if (selection.products.length > 0) {
        const sanitizedProducts = selection.products.map(product => sanitizeInput(product)).filter(product => product.length > 0);
        if (sanitizedProducts.length > 0) {
          if (sanitizedProducts.length > 1) {
            whereConditions.push(`i.producttype IN (${sanitizedProducts.map(product => `'${product}'`).join(',')})`);
          } else {
            whereConditions.push(`i.producttype = '${sanitizedProducts[0]}'`);
          }
        }
      }

      if (selection.divisions.length > 0) {
        const sanitizedDivisions = selection.divisions.map(division => sanitizeInput(division)).filter(division => division.length > 0);
        if (sanitizedDivisions.length > 0) {
          if (sanitizedDivisions.length > 1) {
            whereConditions.push(`i.productdivision IN (${sanitizedDivisions.map(division => `'${division}'`).join(',')})`);
          } else {
            whereConditions.push(`i.productdivision = '${sanitizedDivisions[0]}'`);
          }
        }
      }

      if (selection.colors.length > 0) {
        const sanitizedColors = selection.colors.map(color => sanitizeInput(color)).filter(color => color.length > 0);
        if (sanitizedColors.length > 0) {
          if (sanitizedColors.length > 1) {
            whereConditions.push(`i.color IN (${sanitizedColors.map(color => `'${color}'`).join(',')})`);
          } else {
            whereConditions.push(`i.color = '${sanitizedColors[0]}'`);
          }
        }
      }

      if (selection.sizes.length > 0) {
        const sanitizedSizes = selection.sizes.map(size => sanitizeInput(size)).filter(size => size.length > 0);
        if (sanitizedSizes.length > 0) {
          if (sanitizedSizes.length > 1) {
            whereConditions.push(`i.size IN (${sanitizedSizes.map(size => `'${size}'`).join(',')})`);
          } else {
            whereConditions.push(`i.size = '${sanitizedSizes[0]}'`);
          }
        }
      }

      if (selection.categories.length > 0) {
        const sanitizedCategories = selection.categories.map(category => sanitizeInput(category)).filter(category => category.length > 0);
        if (sanitizedCategories.length > 0) {
          if (sanitizedCategories.length > 1) {
            whereConditions.push(`i.productcategory IN (${sanitizedCategories.map(category => `'${category}'`).join(',')})`);
          } else {
            whereConditions.push(`i.productcategory = '${sanitizedCategories[0]}'`);
          }
        }
      }

      // Validate date format (YYYY-MM-DD)
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (dateRegex.test(dateRange.start) && dateRegex.test(dateRange.end)) {
        whereConditions.push(`b.date BETWEEN '${dateRange.start}' AND '${dateRange.end}'`);
      } else {
        throw new Error('Invalid date format');
      }

      if (whereConditions.length === 0) {
        throw new Error('No valid filter conditions');
      }

      const whereClause = whereConditions.join(' AND ');

      // Build the SELECT clause
      let selectClause;
      let groupByClause;
      let orderByClause;
      selectClause = `
              FORMAT_DATE('%Y-%m-%d', b.date) as date,
              i.upc as upc,
              b.forecast_node as forecast_node,
              SUM(b.quantity) as total_quantity,
              SUM(b.amount) as total_amount,
              'sales' as source,
              i.producttype as product,
              i.color as color,
              i.size as size,
              i.productfamily as family,
              i.productcategory as category,
              i.productdivision as division,
              i.lifestatus as lifestatus,
              i.productspecification as specification
            `;
      groupByClause = `b.date, i.upc, b.forecast_node, i.producttype, i.color, i.size, i.productfamily, i.productcategory, i.productdivision, i.lifestatus, i.productspecification`;
      orderByClause = `b.date ASC, i.upc ASC, b.forecast_node ASC`;
      const query = `
          SELECT
            ${selectClause}
          FROM \`hj-reporting.sales.billed_sales\` b
          LEFT JOIN \`hj-reporting.items.items_netsuite\` i ON b.upc = i.upc
          WHERE ${whereClause}
          GROUP BY ${groupByClause}
          ORDER BY ${orderByClause}
        `;
      console.log('Sales query:', query);
      // get demand_plan data
      const demandPlanWhereClauseList = [
        `i.upc IS NOT NULL`,
        `d.forecast_node IS NOT NULL`
      ];
      if (selection.upcs.length > 0) {
        const sanitizedUpcs = selection.upcs.map(upc => sanitizeInput(upc)).filter(upc => upc.length > 0);
        if (sanitizedUpcs.length > 1) {
          demandPlanWhereClauseList.push(`d.upc IN (${sanitizedUpcs.map(upc => `'${upc}'`).join(',')})`);
        } else {
          demandPlanWhereClauseList.push(`d.upc = '${sanitizedUpcs[0]}'`);
        }
      }
      if (selection.forecastNodes.length > 0) {
        const sanitizedNodes = selection.forecastNodes.map(node => sanitizeInput(node)).filter(node => node.length > 0);
        if (sanitizedNodes.length > 1) {
          demandPlanWhereClauseList.push(`d.forecast_node IN (${sanitizedNodes.map(node => `'${node}'`).join(',')})`);
        } else {
          demandPlanWhereClauseList.push(`d.forecast_node = '${sanitizedNodes[0]}'`);
        }
      }
      // Build demand plan WHERE clause with additional filters
      if (selection.products.length > 0) {
        const sanitizedProducts = selection.products.map(product => sanitizeInput(product)).filter(product => product.length > 0);
        if (sanitizedProducts.length > 0) {
          demandPlanWhereClauseList.push(`i.producttype IN (${sanitizedProducts.map(product => `'${product}'`).join(',')})`);
        }
      }

      if (selection.divisions.length > 0) {
        const sanitizedDivisions = selection.divisions.map(division => sanitizeInput(division)).filter(division => division.length > 0);
        if (sanitizedDivisions.length > 0) {
          demandPlanWhereClauseList.push(`i.productdivision IN (${sanitizedDivisions.map(division => `'${division}'`).join(',')})`);
        }
      }

      if (selection.colors.length > 0) {
        const sanitizedColors = selection.colors.map(color => sanitizeInput(color)).filter(color => color.length > 0);
        if (sanitizedColors.length > 0) {
          demandPlanWhereClauseList.push(`i.color IN (${sanitizedColors.map(color => `'${color}'`).join(',')})`);
        }
      }

      if (selection.sizes.length > 0) {
        const sanitizedSizes = selection.sizes.map(size => sanitizeInput(size)).filter(size => size.length > 0);
        if (sanitizedSizes.length > 0) {
          demandPlanWhereClauseList.push(`i.size IN (${sanitizedSizes.map(size => `'${size}'`).join(',')})`);
        }
      }

      if (selection.categories.length > 0) {
        const sanitizedCategories = selection.categories.map(category => sanitizeInput(category)).filter(category => category.length > 0);
        if (sanitizedCategories.length > 0) {
          demandPlanWhereClauseList.push(`i.productcategory IN (${sanitizedCategories.map(category => `'${category}'`).join(',')})`);
        }
      }

      if (selection.lifeStatuses.length > 0) {
        const sanitizedLifeStatuses = selection.lifeStatuses.map(status => sanitizeInput(status)).filter(status => status.length > 0);
        if (sanitizedLifeStatuses.length > 0) {
          demandPlanWhereClauseList.push(`i.lifestatus IN (${sanitizedLifeStatuses.map(status => `'${status}'`).join(',')})`);
        }
      }

      if (selection.specifications.length > 0) {
        const sanitizedSpecifications = selection.specifications.map(spec => sanitizeInput(spec)).filter(spec => spec.length > 0);
        if (sanitizedSpecifications.length > 0) {
          demandPlanWhereClauseList.push(`i.productspecification IN (${sanitizedSpecifications.map(spec => `'${spec}'`).join(',')})`);
        }
      }

      // Check if we have any conditions, if not, add a default condition
      if (demandPlanWhereClauseList.length === 0) {
        demandPlanWhereClauseList.push('1=1'); // Always true condition as a placeholder
      }

      const demandPlanWhereClause = demandPlanWhereClauseList.join(' AND ');
      const demandPlanQuery = `
        SELECT
          FORMAT_DATE('%Y-%m-%d', d.date) as date,
          d.upc as upc,
          d.forecast_node as forecast_node,
          SUM(d.qty) as total_quantity,
          i.producttype as product,
          i.color as color,
          i.size as size,
          i.productfamily as family,
          i.productcategory as category,
          i.productdivision as division,
          i.lifestatus as lifestatus,
          i.productspecification as specification,
          'forecast' as source
        FROM \`hj-reporting.forecast.demand_plan\` as d
        LEFT JOIN \`hj-reporting.items.items_netsuite\` i ON d.upc = i.upc
        WHERE ${demandPlanWhereClause}
        GROUP BY d.date, d.upc, d.forecast_node, i.producttype, i.color, i.size, i.productfamily, i.productcategory, i.productdivision, i.lifestatus, i.productspecification
        ORDER BY d.date ASC, d.upc ASC, d.forecast_node ASC
      `;

      console.log('Demand plan query:', demandPlanQuery);
      const dataProms = [];
      dataProms.push(api.bigQueryRunQueryOnCall({
          options: { query }
      }));
      if (selection.includeForecast) {
        dataProms.push(api.bigQueryRunQueryOnCall({
          options: { query: demandPlanQuery }
        }));
      }
      const allData = (await Promise.all(dataProms))
        .map(prom => prom.data)
        .flat()
        .filter(row => row.total_quantity > 0)
        .sort((a, b) => new Date(a.date) - new Date(b.date))
        .map(row => {
          row.month = dayjs(row.date).format('MM'); // Use MM for numeric month (01-12)
          row.year = dayjs(row.date).format('YYYY');
          row.week = dayjs(row.date).startOf('week').format('YYYY-MM-DD');
          return row;
      });
      console.log('allData', allData.length, JSON.stringify(allData.slice(0, 10)));
      debugger;
      setSalesData(allData || []);

      // Transform data for chart
      transformDataForChart(allData || []);   
    } catch (error) {
      console.error('Error fetching sales data:', error);

      // Provide user-friendly error messages
      let errorMessage = 'Failed to fetch sales data';
      if (error.message.includes('Invalid date format')) {
        errorMessage = 'Invalid date format. Please check your date selection.';
      } else if (error.message.includes('Too many items')) {
        errorMessage = error.message;
      } else if (error.message.includes('No valid filter conditions')) {
        errorMessage = 'No valid filter conditions. Please check your selections.';
      } else if (error.message.includes('ambiguous')) {
        errorMessage = 'Database query error: Column name is ambiguous. Please contact support.';
      } else if (error.message.includes('BigQuery')) {
        errorMessage = 'Database error. Please try again later.';
      }

      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [selection, lastFetchTime, getDateRange]);

  // Assign fetchSalesData to ref to avoid circular dependency
  useEffect(() => {
    fetchSalesDataRef.current = fetchSalesData;
  }, [fetchSalesData]);

  // Helper function to format time period for display
  const formatTimePeriodForDisplay = useCallback((period, type) => {
    switch (type) {
      case 'week':
        return `Week of ${period}`;
      case 'month':
        return period; // Already formatted as YYYY-MM
      case 'year':
        return period;
      case 'date':
      default:
        return period;
    }
  }, []);

  // Helper function to get next 12 months starting from current month
  const getNext12Months = useCallback(() => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const currentMonth = new Date().getMonth(); // 0-based
    const result = [];

    for (let i = 0; i < 12; i++) {
      const monthIndex = (currentMonth + i) % 12;
      result.push(months[monthIndex]);
    }

    return result;
  }, []);

  // Transform data for year-over-year charting with grouping
  const transformDataForYoY = useCallback((data) => {
    if (!data || data.length === 0) {
      setChartData([]);
      return;
    }

    // Validate data structure and sanitize inputs
    const sanitizedData = data.filter(row => {
      return row &&
        typeof row.date === 'string' &&
        typeof row.upc === 'string' &&
        typeof row.forecast_node === 'string' &&
        typeof row.total_quantity === 'number' &&
        row.total_quantity >= 0;
    });

    if (sanitizedData.length === 0) {
      setChartData([]);
      return;
    }
    
    console.log('Processing data for YoY chart with time period:', grouping.timeperiod);
    
    // Helper function to get the group key
    const getGroupKey = (row) => {
      const primaryValue = row[grouping.primary] || 'Unknown';
      if (grouping.secondary === 'none') {
        return primaryValue;
      }
      const secondaryValue = row[grouping.secondary] || 'Unknown';
      return `${primaryValue} ${secondaryValue}`.trim();
    };

    // Helper function to get the period label (without year)
    const getPeriodLabel = (row) => {
      switch (grouping.timeperiod) {
        case 'date': {
          const date = new Date(row.date);
          return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        }
        case 'week': {
          const weekDate = new Date(row.week);
          const weekOfYear = Math.ceil(((weekDate - new Date(weekDate.getFullYear(), 0, 1)) / 86400000 + 1) / 7);
          return `Week ${weekOfYear}`;
        }
        case 'month': {
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          const monthNum = parseInt(row.month) - 1;
          if (monthNum < 0 || monthNum > 11) {
            console.warn('Invalid month number:', row.month, 'in row:', row);
            return 'Unknown';
          }
          return monthNames[monthNum];
        }
        case 'year': {
          return 'Annual';
        }
        default: {
          const date = new Date(row.date);
          return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        }
      }
    };
    
    // Create a map to aggregate data by period, group, source, and year
    // This is more flexible and handles all time periods consistently
    const aggregatedData = new Map();
    
    // First pass: aggregate quantities by period, group, source, and year
    sanitizedData.forEach(row => {
      const periodLabel = getPeriodLabel(row);
      const year = parseInt(row.year);
      const groupKey = getGroupKey(row);
      const source = row.source;
      const qty = row.total_quantity;
      
      // Create a key that uniquely identifies this combination
      const aggregateKey = `${periodLabel}|${groupKey}|${source}|${year}`;
      
      if (!aggregatedData.has(aggregateKey)) {
        aggregatedData.set(aggregateKey, {
          periodLabel,
          groupKey,
          source,
          year,
          qty: 0
        });
      }
      
      // Add this row's quantity to the aggregate
      const aggregate = aggregatedData.get(aggregateKey);
      aggregate.qty += qty;
    });
    
    console.log('Aggregated data size:', aggregatedData.size);
    
    // Second pass: For each period and group, find the most recent data by source
    const finalData = new Map();
    
    aggregatedData.forEach((data) => {
      const { periodLabel, groupKey, source, year, qty } = data;
      const key = `${periodLabel}|${groupKey}`;
      
      if (!finalData.has(key)) {
        finalData.set(key, {
          periodLabel,
          groupKey,
          historical: { qty: 0, year: 0 },
          forecast: { qty: 0, year: 0 }
        });
      }
      
      const entry = finalData.get(key);
      
      if (source === 'sales') {
        // For historical data, use the most recent year's data
        if (year > entry.historical.year) {
          entry.historical.qty = qty;
          entry.historical.year = year;
        }
      } else if (source === 'forecast') {
        // For forecast data, use the most recent year's data
        if (year > entry.forecast.year) {
          entry.forecast.qty = qty;
          entry.forecast.year = year;
        }
      }
    });

    // Create chart data structure based on time period
    const getPeriodsForDisplay = () => {
      switch (grouping.timeperiod) {
        case 'month': {
          // For month view, use next 12 months starting from current month
          return getNext12Months();
        }
        case 'week': {
          // Generate weeks 1-52
          const weeks = [];
          for (let i = 1; i <= 52; i++) {
            weeks.push(`Week ${i}`);
          }
          return weeks;
        }
        case 'date': {
          // Generate month-day combinations for the current year
          const monthDays = [];
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          const daysInMonth = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]; // Including leap year Feb

          monthNames.forEach((month, monthIndex) => {
            for (let day = 1; day <= daysInMonth[monthIndex]; day++) {
              monthDays.push(`${String(monthIndex + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`);
            }
          });
          return monthDays;
        }
        case 'year':
          return ['Annual'];
        default:
          return getNext12Months();
      }
    };

    const periodsForDisplay = getPeriodsForDisplay();
    const chartDataArray = [];
    
    // Create a map to store data for each period
    const periodDataMap = new Map();
    
    // Initialize data for each period
    periodsForDisplay.forEach(periodLabel => {
      // Calculate sort index based on period type
      let sortIndex;
      if (periodLabel.startsWith('Week')) {
        sortIndex = parseInt(periodLabel.replace('Week ', ''));
      } else if (grouping.timeperiod === 'month') {
        // For months, calculate index based on current month position
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const currentMonth = new Date().getMonth(); // 0-based
        const monthIndex = months.indexOf(periodLabel);
        
        // Calculate position relative to current month (0 = current month, 1 = next month, etc.)
        sortIndex = (monthIndex - currentMonth + 12) % 12;
      } else {
        // For other period types, use standard ordering
        sortIndex = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].indexOf(periodLabel);
      }
      
      periodDataMap.set(periodLabel, {
        periodLabel: periodLabel,
        _metadata: new Map(),
        sortIndex: sortIndex
      });
    });
    
    // Process the final aggregated data and add to chart data
    finalData.forEach((data) => {
      const { periodLabel, groupKey, historical, forecast } = data;
      
      // Skip invalid periods or groups
      if (!periodDataMap.has(periodLabel) || !groupKey || groupKey === 'Unknown' || groupKey.trim() === '') {
        return;
      }
      
      const periodData = periodDataMap.get(periodLabel);
      
      // Add historical series if data exists
      if (historical.qty > 0) {
        const histKey = `${groupKey}_historical`;
        periodData[histKey] = historical.qty;
        periodData._metadata.set(histKey, {
          groupKey,
          source: 'Historical',
          year: historical.year,
          timePeriodType: grouping.timeperiod
        });
      }
      
      // Add forecast series if data exists
      if (forecast.qty > 0) {
        const forecastKey = `${groupKey}_forecast`;
        periodData[forecastKey] = forecast.qty;
        periodData._metadata.set(forecastKey, {
          groupKey,
          source: 'Forecast',
          year: forecast.year,
          timePeriodType: grouping.timeperiod
        });
      }
    });
    
    // Convert map to array and sort by the sortIndex
    chartDataArray.push(...Array.from(periodDataMap.values()));
    
    // Sort based on the time period
    if (grouping.timeperiod === 'month') {
      // For months, sort by the calculated sortIndex which is based on current month
      chartDataArray.sort((a, b) => a.sortIndex - b.sortIndex);
      
      // Log the sorted order for debugging
      console.log('Sorted month order:', chartDataArray.map(item => item.periodLabel).join(', '));
    } else if (grouping.timeperiod === 'week') {
      // For weeks, sort numerically
      chartDataArray.sort((a, b) => a.sortIndex - b.sortIndex);
    } else {
      // For other periods, sort by the standard order
      chartDataArray.sort((a, b) => a.sortIndex - b.sortIndex);
    }
    
    console.log('Generated YoY chart data:', chartDataArray.length, 'periods');
    setChartData(chartDataArray);
  }, [grouping, getNext12Months]);

  // Transform data for trendline charting (chronological time-based view)
  const transformDataForTrendline = useCallback((data) => {
    if (!data || data.length === 0) {
      setChartData([]);
      return;
    }

    // Validate data structure and sanitize inputs
    const sanitizedData = data.filter(row => {
      return row &&
        typeof row.date === 'string' &&
        typeof row.upc === 'string' &&
        typeof row.forecast_node === 'string' &&
        typeof row.total_quantity === 'number' &&
        row.total_quantity >= 0;
    });

    if (sanitizedData.length === 0) {
      setChartData([]);
      return;
    }

    // Group data based on selected grouping options
    const groupedDataMap = new Map();

    // Helper function to get the time period value
    const getTimePeriodValue = (row) => {
      switch (grouping.timeperiod) {
        case 'date': return row.date;
        case 'week': return row.week;
        case 'month': {
          // Create YYYY-MM format from year and month
          const year = row.year || '0000';
          const month = row.month || '01';
          return `${year}-${month.toString().padStart(2, '0')}`;
        }
        case 'year': return row.year;
        default: return row.date;
      }
    };

    // Helper function to get the group key
    const getGroupKey = (row) => {
      const primaryValue = row[grouping.primary] || 'Unknown';
      if (grouping.secondary === 'none') {
        return primaryValue;
      }
      const secondaryValue = row[grouping.secondary] || 'Unknown';
      return `${primaryValue} ${secondaryValue}`.trim();
    };

    sanitizedData.forEach(row => {
      const timePeriod = getTimePeriodValue(row);
      const groupKey = getGroupKey(row);
      const compositeKey = `${timePeriod}_${groupKey}`;

      if (!groupedDataMap.has(compositeKey)) {
        groupedDataMap.set(compositeKey, {
          timePeriod,
          groupKey,
          salesQty: 0,
          forecastQty: 0,
          // Store metadata for the group
          metadata: {
            timePeriodType: grouping.timeperiod,
            groupName: groupKey,
            forecast_nodes: new Set(),
            sources: new Set()
          }
        });
      }

      const groupData = groupedDataMap.get(compositeKey);

      // Aggregate quantities by source
      if (row.source === 'sales') {
        groupData.salesQty += row.total_quantity;
        groupData.metadata.sources.add('sales');
      } else if (row.source === 'forecast') {
        groupData.forecastQty += row.total_quantity;
        groupData.metadata.sources.add('forecast');
      }

      // Track forecast nodes for this group
      groupData.metadata.forecast_nodes.add(row.forecast_node);
    });

    // Convert grouped data to chart format
    const chartDataArray = [];

    // Group by time period for chart data structure
    const timePeriodMap = new Map();

    Array.from(groupedDataMap.values()).forEach(groupData => {
      const { timePeriod, groupKey, salesQty, forecastQty, metadata } = groupData;

      if (!timePeriodMap.has(timePeriod)) {
        timePeriodMap.set(timePeriod, {
          timePeriod,
          displayDate: formatTimePeriodForDisplay(timePeriod, grouping.timeperiod),
          _metadata: new Map()
        });
      }

      const timeData = timePeriodMap.get(timePeriod);

      // Store sales data if exists
      if (salesQty > 0) {
        timeData[`${groupKey}_sales`] = salesQty;
        timeData._metadata.set(`${groupKey}_sales`, {
          groupName: groupKey,
          source: 'Sales',
          timePeriodType: grouping.timeperiod
        });
      }

      // Store forecast data if exists
      if (forecastQty > 0) {
        timeData[`${groupKey}_forecast`] = forecastQty;
        timeData._metadata.set(`${groupKey}_forecast`, {
          groupName: groupKey,
          source: 'Forecast',
          timePeriodType: grouping.timeperiod
        });
      }
    });

    // Convert to array and sort
    const sortedData = Array.from(timePeriodMap.values()).sort((a, b) => {
      // Sort by time period
      if (grouping.timeperiod === 'date') {
        return new Date(a.timePeriod) - new Date(b.timePeriod);
      }
      return a.timePeriod.localeCompare(b.timePeriod);
    });

    chartDataArray.push(...sortedData);

    console.log('Generated trendline chart data:', chartDataArray.length, 'periods');
    setChartData(chartDataArray);
  }, [grouping, formatTimePeriodForDisplay]);

  // Main transform function that delegates to the appropriate view
  const transformDataForChart = useCallback((data) => {
    if (chartView === 'yoy') {
      transformDataForYoY(data);
    } else {
      transformDataForTrendline(data);
    }
  }, [chartView, transformDataForYoY, transformDataForTrendline]);

  // Handle form submission
  const handleSubmit = useCallback(() => {
    if (dateRange === 'custom' && (!customDateRange || customDateRange.length !== 2)) {
      message.warning('Please select a valid custom date range');
      return;
    }
    // Since we're not using form validation for the dynamic fields, just fetch data
    fetchSalesData();
  }, [dateRange, customDateRange, fetchSalesData]);

  // Handle date range change
  const handleDateRangeChange = useCallback((value) => {
    setDateRange(value);
    if (value !== 'custom') {
      setCustomDateRange(null);
    }
  }, []);

  // Handle clear selection - clear only filter fields, keep UPCs and forecast nodes
  const handleClearSelection = useCallback(() => {
    setSelection(prev => ({
      ...prev,
      products: [],
      divisions: [],
      colors: [],
      sizes: [],
      categories: [],
      lifeStatuses: [],
      specifications: []
    }));
  }, []); // No dependencies needed

  // Handle reset all - clear everything including UPCs and forecast nodes
  const handleResetAll = useCallback(() => {
    setSelection({
      upcs: [],
      forecastNodes: [],
      dateRange: null,
      products: [],
      divisions: [],
      colors: [],
      sizes: [],
      categories: [],
      lifeStatuses: [],
      specifications: [],
      includeForecast: true
    });
    setDateRange('90');
    setCustomDateRange(null);
  }, []); // No dependencies needed

  // Memoized onChange handlers
  const handleUpcsChange = useCallback((values) => {
    setSelection(prev => ({ ...prev, upcs: values }));
  }, []);

  const handleForecastNodesChange = useCallback((values) => {
    setSelection(prev => ({ ...prev, forecastNodes: values }));
  }, []);

  const handleProductsChange = useCallback((values) => {
    setSelection(prev => ({ ...prev, products: values }));
  }, []);

  const handleDivisionsChange = useCallback((values) => {
    setSelection(prev => ({ ...prev, divisions: values }));
  }, []);

  const handleColorsChange = useCallback((values) => {
    setSelection(prev => ({ ...prev, colors: values }));
  }, []);

  const handleSizesChange = useCallback((values) => {
    setSelection(prev => ({ ...prev, sizes: values }));
  }, []);

  const handleCategoriesChange = useCallback((values) => {
    setSelection(prev => ({ ...prev, categories: values }));
  }, []);

  const handleLifeStatusesChange = useCallback((values) => {
    setSelection(prev => ({ ...prev, lifeStatuses: values }));
  }, []);

  const handleSpecificationsChange = useCallback((values) => {
    setSelection(prev => ({ ...prev, specifications: values }));
  }, []);

  const handleCustomDateRangeChange = useCallback((dates) => {
    setCustomDateRange(dates);
  }, []);

  // Grouping handlers
  const handleTimePeriodChange = useCallback((value) => {
    setGrouping(prev => ({ ...prev, timeperiod: value }));
  }, []);

  const handlePrimaryGroupChange = useCallback((value) => {
    setGrouping(prev => ({
      ...prev,
      primary: value,
      secondary: prev.secondary === value ? 'none' : prev.secondary
    }));
  }, []);

  const handleSecondaryGroupChange = useCallback((value) => {
    setGrouping(prev => ({ ...prev, secondary: value }));
  }, []);

  const handleChartViewChange = useCallback((value) => {
    setChartView(value);
  }, []);

  // Generate consistent colors using HSL
  const generateColor = useCallback((seriesName, index) => {
    // Handle undefined or null seriesName
    if (!seriesName || typeof seriesName !== 'string') {
      seriesName = 'default';
    }

    // Use a simple hash function for consistency
    let hash = 0;
    for (let i = 0; i < seriesName.length; i++) {
      hash = seriesName.charCodeAt(i) + ((hash << 5) - hash);
    }
    const hue = Math.abs(hash) % 360;
    return `hsl(${hue}, 70%, 50%)`;
  }, []);

  // Helper function to get time period label for tooltip
  const getTimePeriodLabel = useCallback((timePeriodType) => {
    switch (timePeriodType) {
      case 'week': return 'Week';
      case 'month': return 'Month';
      case 'year': return 'Year';
      case 'date': return 'Date';
      default: return 'Period';
    }
  }, []);

  // Chart options for year-over-year display
  const getYoYChartOptions = useMemo(() => {
    const series = [];

    if (chartData.length > 0) {
      // Get all unique series keys
      const allKeys = new Set();
      chartData.forEach(dataPoint => {
        Object.keys(dataPoint).forEach(key => {
          if (key !== 'periodLabel' && key !== '_metadata') {
            allKeys.add(key);
          }
        });
      });

      console.log('YoY Chart - Found series keys:', Array.from(allKeys));
      console.log('YoY Chart - Sample data point:', chartData[0]);

      // Group keys by group, maintaining historical/forecast separation
      const seriesMap = new Map();
      Array.from(allKeys).forEach(key => {
        // Key format: group_source (e.g., "TravelBottle_historical")
        const parts = key.split('_');
        if (parts.length >= 2) {
          const source = parts[parts.length - 1]; // Last part is source
          const group = parts.slice(0, -1).join('_'); // Everything else is group

          if (!seriesMap.has(group)) {
            seriesMap.set(group, { historical: null, forecast: null });
          }

          if (source === 'historical') {
            seriesMap.get(group).historical = key;
          } else if (source === 'forecast') {
            seriesMap.get(group).forecast = key;
          }
        }
      });

      // Create series for each group
      seriesMap.forEach((sourcePair, groupName) => {
        // Ensure groupName is valid
        if (!groupName || groupName === '') {
          console.warn('Invalid groupName found:', groupName);
          return;
        }

        // Generate consistent color for this group
        const seriesColor = generateColor(groupName);

        // Create historical series if data exists
        if (sourcePair.historical) {
          const historicalData = chartData
            .filter(dataPoint => dataPoint[sourcePair.historical] != null)
            .map(dataPoint => {
              const metadata = dataPoint._metadata.get(sourcePair.historical);
              return {
                period: dataPoint.periodLabel,
                value: dataPoint[sourcePair.historical],
                groupName,
                source: 'Historical',
                year: metadata ? metadata.year : 'Unknown',
                actualPeriod: metadata ? `${dataPoint.periodLabel} ${metadata.year}` : dataPoint.periodLabel
              };
            });

          if (historicalData.length > 0) {
            series.push({
              type: 'line',
              data: historicalData,
              xKey: 'period',
              yKey: 'value',
              yName: groupName,
              stroke: seriesColor,
              strokeWidth: 2,
              strokeOpacity: 1, // Full opacity for historical
              lineDash: [], // Solid line for historical
              marker: {
                enabled: true,
                size: 4,
                fill: seriesColor,
                fillOpacity: 1,
                stroke: '#ffffff',
                strokeWidth: 1
              },
              tooltip: {
                renderer: function(params) {
                  const point = params.datum;
                  return (
                    '<div style="padding: 8px; background: white; border: 1px solid #ccc; border-radius: 4px;">' +
                    'Group: ' + point.groupName + '<br/>' +
                    'Period: ' + point.actualPeriod + '<br/>' +
                    'Qty: ' + parseInt(point.value).toLocaleString() + '<br/>' +
                    'Source: Historical' +
                    '</div>'
                  );
                }
              }
            });
          }
        }

        // Create forecast series if data exists
        if (sourcePair.forecast) {
          const forecastData = chartData
            .filter(dataPoint => dataPoint[sourcePair.forecast] != null)
            .map(dataPoint => {
              const metadata = dataPoint._metadata.get(sourcePair.forecast);
              return {
                period: dataPoint.periodLabel,
                value: dataPoint[sourcePair.forecast],
                groupName,
                source: 'Forecast',
                year: metadata ? metadata.year : 'Unknown',
                actualPeriod: metadata ? `${dataPoint.periodLabel} ${metadata.year}` : dataPoint.periodLabel
              };
            });

          if (forecastData.length > 0) {
            series.push({
              type: 'line',
              data: forecastData,
              xKey: 'period',
              yKey: 'value',
              yName: `${groupName} (Forecast)`,
              stroke: seriesColor,
              strokeWidth: 2,
              strokeOpacity: 0.7, // 70% opacity for forecast
              lineDash: [5, 5], // Dashed line for forecast
              showInLegend: false, // Hide forecast from legend to reduce clutter
              marker: {
                enabled: true,
                size: 4,
                fill: seriesColor,
                fillOpacity: 0.7,
                stroke: '#ffffff',
                strokeWidth: 1
              },
              tooltip: {
                renderer: function(params) {
                  const point = params.datum;
                  return (
                    '<div style="padding: 8px; background: white; border: 1px solid #ccc; border-radius: 4px;">' +
                    'Group: ' + point.groupName + '<br/>' +
                    'Period: ' + point.actualPeriod + '<br/>' +
                    'Qty: ' + parseInt(point.value).toLocaleString() + '<br/>' +
                    'Source: Forecast' +
                    '</div>'
                  );
                }
              }
            });
          }
        }
      });
    }

    return {
    title: {
        text: `Historical Sales - Year over Year`,
      fontSize: 18
    },
    subtitle: {
        text: 'Historical vs Forecast compared by period',
      fontSize: 14,
      color: '#666'
    },
      data: chartData,
      series,
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: {
            text: (() => {
              switch (grouping.timeperiod) {
                case 'month': return 'Month';
                case 'week': return 'Week';
                case 'date': return 'Date';
                case 'year': return 'Year';
                default: return 'Period';
              }
            })()
          },
          // Ensure the x-axis maintains the order of the data
          categoryKey: 'period',
          categoryAxis: {
            label: {
              autoRotate: true,
              autoRotateAngle: 45
            }
          }
        },
        {
          type: 'number',
          position: 'left',
          title: {
            text: 'Quantity'
          }
        }
      ],
      legend: {
        position: 'bottom',
        item: {
          marker: {
            padding: 6,
            shape: 'circle'
          }
        },
        enabled: true
      },
      tooltip: {
        enabled: true,
        tracking: true
      }
    };
  }, [chartData, generateColor]);

  // Chart options for trendline display
  const getTrendlineChartOptions = useMemo(() => {
    const series = [];

    if (chartData.length > 0) {
      // Get metadata from the first data point to create series
      const metadata = chartData[0]._metadata;

      // Get all unique keys except time period fields and metadata
      const allKeys = new Set();
      chartData.forEach(dataPoint => {
        Object.keys(dataPoint).forEach(key => {
          if (key !== 'timePeriod' && key !== 'displayDate' && key !== '_metadata') {
            allKeys.add(key);
          }
        });
      });

      console.log('Found trendline series keys:', Array.from(allKeys));

      // Group keys by series (group name combination)
      const seriesGroups = new Map();
      Array.from(allKeys).forEach(key => {
        const isSales = key.includes('_sales');
        const isForecast = key.includes('_forecast');

        if (isSales || isForecast) {
          // Extract the base series key (without _sales or _forecast)
          const baseKey = key.replace(/_sales$|_forecast$/, '');

          if (!seriesGroups.has(baseKey)) {
            seriesGroups.set(baseKey, { sales: null, forecast: null });
          }

          if (isSales) {
            seriesGroups.get(baseKey).sales = key;
          } else if (isForecast) {
            seriesGroups.get(baseKey).forecast = key;
          }
        }
      });

      // Create separate sales and forecast series for each grouped item
      seriesGroups.forEach((group, baseKey) => {
        // Get metadata for this series from the first data point that has this key
        const seriesMetadata = metadata ? metadata.get(group.sales || group.forecast) : null;

        if (seriesMetadata) {
          // Use the group name as display name
          const displayName = seriesMetadata.groupName;

          // Generate consistent color for this group
          const seriesColor = generateColor(displayName);

          // Create sales data array
          const salesData = chartData
            .filter(dataPoint => group.sales && dataPoint[group.sales] != null)
            .map(dataPoint => ({
              date: new Date(dataPoint.timePeriod), // Convert to Date for chart
              value: dataPoint[group.sales],
              groupName: displayName,
              timePeriod: dataPoint.displayDate,
              source: 'Sales'
            }));

          // Create forecast data array  
          const forecastData = chartData
            .filter(dataPoint => group.forecast && dataPoint[group.forecast] != null)
            .map(dataPoint => ({
              date: new Date(dataPoint.timePeriod), // Convert to Date for chart
              value: dataPoint[group.forecast],
              groupName: displayName,
              timePeriod: dataPoint.displayDate,
              source: 'Forecast'
            }));

          // Add sales series if data exists
          if (salesData.length > 0) {
            series.push({
        type: 'line',
              data: salesData,
        xKey: 'date',
              yKey: 'value',
              yName: displayName,
              stroke: seriesColor,
        strokeWidth: 2,
              strokeOpacity: 1, // Full opacity for sales
              lineDash: [], // Solid line for sales
        marker: {
                enabled: true,
                size: 4,
                fill: seriesColor,
                fillOpacity: 1, // Full opacity for sales markers
                stroke: '#ffffff',
                strokeWidth: 1
              },
              tooltip: {
                renderer: function(params) {
                  const point = params.datum;
                  const timePeriodLabel = getTimePeriodLabel(seriesMetadata.timePeriodType);
                  return (
                    '<div style="padding: 8px; background: white; border: 1px solid #ccc; border-radius: 4px;">' +
                    'Group: ' + point.groupName + '<br/>' +
                    'Qty: ' + parseInt(point.value) + '<br/>' +
                    timePeriodLabel + ': ' + point.timePeriod +
                    '</div>'
                  );
                }
              }
            });
          }

          // Add forecast series if data exists
          if (forecastData.length > 0) {
            series.push({
              type: 'line',
              data: forecastData,
              xKey: 'date',
              yKey: 'value',
              yName: displayName, // Same name as sales to group in legend
              stroke: seriesColor,
              strokeWidth: 2,
              strokeOpacity: 0.7, // 70% opacity for forecast
              lineDash: [5, 5], // Dashed line for forecast
              showInLegend: false, // Hide forecast series from legend
              marker: {
                enabled: true,
                size: 4,
                fill: seriesColor,
                fillOpacity: 0.7, // 70% opacity for forecast markers
                stroke: '#ffffff',
                strokeWidth: 1
              },
              tooltip: {
                renderer: function(params) {
                  const point = params.datum;
                  const timePeriodLabel = getTimePeriodLabel(seriesMetadata.timePeriodType);
                  return (
                    '<div style="padding: 8px; background: white; border: 1px solid #ccc; border-radius: 4px;">' +
                    'Group: ' + point.groupName + '<br/>' +
                    'Qty: ' + parseInt(point.value).toLocaleString('en-US', {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                      style: 'decimal'
                    }) + '<br/>' +
                    timePeriodLabel + ': ' + point.timePeriod +
                    '</div>'
                  );
                }
              }
            });
          }
        }
      });
    }

    // Get the time period label for axis title
    const getAxisTitle = () => {
      switch (grouping.timeperiod) {
        case 'month': return 'Month';
        case 'week': return 'Week';
        case 'date': return 'Date';
        case 'year': return 'Year';
        default: return 'Period';
      }
    };

    return {
      title: {
        text: `Historical Sales - Trendline`,
        fontSize: 18
      },
      subtitle: {
        text: 'Historical sales (solid lines) vs Current demand plan (dashed lines)',
        fontSize: 14,
        color: '#666'
      },
      data: chartData,
      series,
    axes: [
      {
        type: 'time',
        position: 'bottom',
        title: {
          text: 'Date'
        }
      },
      {
        type: 'number',
        position: 'left',
        title: {
          text: 'Quantity'
        }
      }
    ],
    legend: {
        position: 'bottom',
        item: {
          marker: {
            padding: 6,
            shape: 'circle'
          }
        },
        // Group series with same names
      enabled: true
      },
      tooltip: {
        enabled: true,
        tracking: true
      }
    };
  }, [chartData, grouping, generateColor, getTimePeriodLabel]);

  // Main chart options that delegates to the appropriate view
  const chartOptions = useMemo(() => {
    if (chartView === 'yoy') {
      return getYoYChartOptions;
    } else {
      return getTrendlineChartOptions;
    }
  }, [chartView, getYoYChartOptions, getTrendlineChartOptions]);

  // Column definitions for the data table
  const columnDefs = useMemo(() => [
    {
      field: 'source',
      headerName: 'Source',
      sortable: true,
      filter: true,
      width: 120,
      valueFormatter: params => {
        // make the value proper case
        if (params.value == null) return '';
        return params.value.charAt(0).toUpperCase() + params.value.slice(1).toLowerCase();
      },
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'year',
      headerName: 'Year',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'month',
      headerName: 'Month',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'week',
      headerName: 'Week',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'date',
      headerName: 'Date',
      sortable: true,
      filter: true,
      width: 120,
      filter: 'agTextColumnFilter', // Use text filter for grouped dates
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'upc',
      headerName: 'UPC',
      sortable: true,
      filter: true,
      width: 150,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'forecast_node',
      headerName: 'Forecast Node',
      sortable: true,
      filter: true,
      width: 200,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'product',
      headerName: 'Product',
      sortable: true,
      filter: true,
      width: 150,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'color',
      headerName: 'Color',
      sortable: true,
      filter: true,
      width: 100,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'size',
      headerName: 'Size',
      sortable: true,
      filter: true,
      width: 80,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'family',
      headerName: 'Family',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'category',
      headerName: 'Category',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'division',
      headerName: 'Division',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'lifestatus',
      headerName: 'Life Status',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'specification',
      headerName: 'Specification',
      sortable: true,
      filter: true,
      width: 150,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'total_quantity',
      headerName: 'Quantity',
      sortable: true,
      filter: true,
      width: 100,
      aggFunc: 'sum',
      type: 'numericColumn',
      valueFormatter: params => {
        if (params.value == null) return '';
        return new Intl.NumberFormat('en-US', {
          style: 'decimal',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        }).format(params.value);
      }
    },
    {
      field: 'total_amount',
      headerName: 'Amount',
      sortable: true,
      filter: true,
      width: 120,
      aggFunc: 'sum',
      type: 'numericColumn',
      valueFormatter: params => {
        if (params.value == null) return '';
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(params.value);
      }
    }
  ], []);

  // Initialize selection from initialSelection - only run once on mount
  useEffect(() => {
    if (initialSelection) {
      // Validate and sanitize initial selection
      const sanitizeArray = (arr) => {
        if (!Array.isArray(arr)) return [];
        return arr.filter(item =>
          typeof item === 'string' &&
          item.length > 0 &&
          item.length <= 50
        ).slice(0, 100); // Limit to 100 items
      };

      setSelection({
        upcs: sanitizeArray(initialSelection.upcs),
        forecastNodes: sanitizeArray(initialSelection.forecastNodes),
        dateRange: initialSelection.dateRange || null,
        products: [],
        divisions: [],
        colors: [],
        sizes: [],
        categories: [],
        lifeStatuses: [],
        specifications: []
      });
    }
  }, []); // Empty dependency array - only run once on mount

  // Auto-fetch data when component mounts with initial selection (only for page, not modal)
  useEffect(() => {
    if (!isModal && initialSelection?.upcs?.length > 0 && initialSelection?.forecastNodes?.length > 0) {
      fetchSalesData();
    }
  }, [isModal]); // Only depend on isModal - run once when component mounts

  // Auto-search functionality for modal mode - only run once
  useEffect(() => {
    if (autoSearch && isModal && !autoSearchExecuted.current && selection.upcs.length > 0 && selection.forecastNodes.length > 0) {
      autoSearchExecuted.current = true;
      fetchSalesData();
    }
  }, [autoSearch, isModal, selection.upcs, selection.forecastNodes]);

  // Load user's last used views when not in modal mode
  useEffect(() => {
    if (!isModal && userData?.id && savedChartViews.length > 0 && savedTableViews.length > 0) {
      const loadLastUsedViews = async () => {
        try {
          // Load last used chart view
          const lastChartViewId = userData.lastHistoricalSalesChartView;
          if (lastChartViewId) {
            const chartViewExists = savedChartViews.some(view => view.id === lastChartViewId);
            if (chartViewExists) {
              await handleLoadChartView(lastChartViewId);
            } else {
              // View no longer exists, clear the reference
              await setDoc(doc(db, 'users', userData.id), {
                lastHistoricalSalesChartView: null
              }, { merge: true });
            }
          }
        } catch (error) {
          console.error('Error loading last used views:', error);
        }
      };

      loadLastUsedViews();
    }
  }, [isModal, userData, savedChartViews, savedTableViews, handleLoadChartView]);

  // Re-transform chart data when grouping or view changes (without fetching new data)
  useEffect(() => {
    if (salesData.length > 0) {
      transformDataForChart(salesData);
    }
  }, [grouping, chartView, transformDataForChart, salesData]);

  // Handle save view modal
  const handleOpenSaveViewModal = useCallback((mode) => {
    setSaveViewMode(mode);
    setNewViewName('');
    setNewViewIsPublic(false);
    setSaveViewModalVisible(true);
  }, []);

  const handleSaveViewSubmit = useCallback(async () => {
    if (!newViewName.trim()) {
      message.warning('Please enter a view name');
      return;
    }

    try {
      setSaveViewModalVisible(false);

      // Only table view saving is supported now
      await handleSaveTableView(newViewName, newViewIsPublic);
    } catch (error) {
      console.error('Error saving view:', error);
    }
  }, [newViewName, newViewIsPublic, handleSaveTableView]);

  // Tab items configuration
  const tabItems = useMemo(() => [
    {
      key: 'chart',
      label: 'Chart View',
      children: (
        <div style={{ height: 'calc(100vh - 200px)', width: '100%', minHeight: '600px' }}>
          {/* Grouping Controls and Saved Views */}
          <Row gutter={[16, 8]} style={{ marginBottom: 16, padding: '0 8px' }}>


            <Col>
              <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Chart View:</label>
              <Select
                value={chartView}
                onChange={handleChartViewChange}
                options={chartViewOptions}
                style={{ width: '140px' }}
              />
            </Col>
            <Col>
              <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Time Period:</label>
              <Select
                value={grouping.timeperiod}
                onChange={handleTimePeriodChange}
                options={timePeriodOptions}
                style={{ width: '120px' }}
              />
            </Col>
            <Col>
              <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Group By:</label>
              <Select
                value={grouping.primary}
                onChange={handlePrimaryGroupChange}
                options={groupByOptions}
                style={{ width: '140px' }}
              />
            </Col>
            <Col>
              <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Then By:</label>
              <Select
                value={grouping.secondary}
                onChange={handleSecondaryGroupChange}
                options={secondaryGroupOptions}
                style={{ width: '200px' }}
                allowClear={false}
              />
            </Col>
          </Row>

          {/* Chart Container */}
          <div style={{ height: 'calc(100vh - 300px)', width: '100%', minHeight: '500px' }}>
          {loading ? (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Spin size="large" />
            </div>
          ) : chartData.length > 0 ? (
              <AgCharts
                style={{ height: '100%', width: '100%' }}
                options={chartOptions}
                key={`chart-${JSON.stringify(chartData)}`} />
          ) : (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', color: '#666' }}>
                No data to display. Please select filters and click &quot;Search&quot;.
            </div>
          )}
          </div>
        </div>
      )
    },
    {
      key: 'table',
      label: 'Table View',
      children: (
        <div style={{ height: 'calc(100vh - 370px)', width: '100%', minHeight: '600px' }}>
          {loading ? (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Spin size="large" />
            </div>
          ) : (
            <AgGridReact
              ref={gridRef}
              enableRowGroup={true}
              enablePivot={true}
              columnDefs={columnDefs}
                cellSelection={true}
              rowData={salesData}
              pagination={true}
              paginationPageSize={50}
              animateRows={false}
              theme={themeBalham}
              sideBar={{
                toolPanels: [
                  {
                    id: 'columns',
                    labelDefault: 'Columns',
                    labelKey: 'columns',
                    iconKey: 'columns',
                    toolPanel: 'agColumnsToolPanel',
                  },
                  {
                    id: 'filters',
                    labelDefault: 'Filters',
                    labelKey: 'filters',
                    iconKey: 'filter',
                    toolPanel: 'agFiltersToolPanel',
                  }
                ],
              }}
              rowClassRules={{
                'forecast-row': (params) => {
                  return params?.data?.source === 'forecast';
                },
                'sales-row': (params) => {
                  return params?.data?.source === 'sales';
                }
              }}
              defaultColDef={{
                resizable: true,
                sortable: true,
                filter: true
              }}
            />
          )}
        </div>
      )
    }
  ], [loading, chartData, chartOptions, columnDefs, salesData]);

  return (
    <div style={{ padding: '20px' }}>
      {/* Page Title */}
        <div style={{ marginBottom: '24px' }}>
        <Row gutter={[8, 16]}>
          <Col>
          <h1 style={{ margin: 0, fontSize: '24px', fontWeight: '600' }}>Historical Sales Analysis</h1>
          <p style={{ margin: '8px 0 0 0', color: '#666' }}>
            Analyze historical sales data and compare with current demand plans
          </p>
          </Col>
          < Col style={{ marginLeft: 'auto', display: 'flex', gap: 8 }}>
            <Button
              type="primary"
              onClick={handleSubmit}
              loading={loading}
              disabled={Object.values(selection).every(value => value?.length === 0)}
              style={{ marginTop: 24, width: '80px' }}
            >
              Search
            </Button>
            <Button
              onClick={handleResetAll}
              style={{ marginTop: 24, width: '80px' }}
            >
              Reset All
            </Button>
            {activeTab === 'chart' && (
              <Select
                value={selectedChartViewId}
                onChange={handleLoadChartView}
                placeholder="Select saved view"
                allowClear
                loading={applyingView}
                style={{ width: '200px' }}
                options={savedChartViews.map(view => ({
                  value: view.id,
                  label: view.name
                }))}
                dropdownRender={menu => (
                  <div>
                    {savedChartViews.length === 0 && <div style={{ padding: 8, color: '#888' }}>No saved chart views</div>}
                    {savedChartViews.map(view => (
                      <div key={view.id} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '4px 12px', cursor: 'pointer' }}
                        onClick={() => handleLoadChartView(view.id)}
                      >
                        <span style={{ flex: 1 }}>
                          {view.name} {view.isPublic ? <EyeOutlined style={{ color: '#1890ff' }} /> : <EyeInvisibleOutlined style={{ color: '#666' }} />}
                          {view.createdBy !== userData?.id && <span style={{ fontSize: '11px', color: '#666' }}> (by {view.createdByEmail || view.createdBy})</span>}
                        </span>
                        {(view.createdBy === userData?.id || userData?.isAdmin) && (
                          <Popconfirm title="Delete this chart view?" onConfirm={(e) => {
                            e.stopPropagation();
                            handleDeleteChartView(view.id);
                          }}>
                            <DeleteOutlined
                              style={{ color: '#ff4d4f', marginLeft: 8 }}
                              onClick={(e) => e.stopPropagation()}
                            />
                          </Popconfirm>
                        )}
                      </div>
                    ))}
        </div>
      )}
              />
            )}
            {activeTab === 'table' && (
              <Select
                value={selectedTableViewId}
                onChange={handleLoadTableView}
                placeholder="Select saved table view"
                allowClear
                loading={applyingView}
                style={{ width: '200px' }}
                options={savedTableViews.map(view => ({
                  value: view.id,
                  label: view.name
                }))}
                dropdownRender={menu => (
                  <div>
                    {savedTableViews.length === 0 && <div style={{ padding: 8, color: '#888' }}>No saved table views</div>}
                    {savedTableViews.map(view => (
                      <div key={view.id} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '4px 12px', cursor: 'pointer' }}
                        onClick={() => handleLoadTableView(view.id)}
                      >
                        <span style={{ flex: 1 }}>
                          {view.name} {view.isPublic ? <EyeOutlined style={{ color: '#1890ff' }} /> : <EyeInvisibleOutlined style={{ color: '#666' }} />}
                          {view.createdBy !== userData?.id && <span style={{ fontSize: '11px', color: '#666' }}> (by {view.createdByEmail || view.createdBy})</span>}
                        </span>
                        {(view.createdBy === userData?.id || userData?.isAdmin) && (
                          <Popconfirm title="Delete this table view?" onConfirm={(e) => {
                            e.stopPropagation();
                            handleDeleteTableView(view.id);
                          }}>
                            <DeleteOutlined
                              style={{ color: '#ff4d4f', marginLeft: 8 }}
                              onClick={(e) => e.stopPropagation()}
                            />
                          </Popconfirm>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              />
            )}
            {activeTab === 'table' && (
              <Tooltip title="Save current table configuration (filters, columns, sorting) as a reusable view that you can quickly load later">
                <Button
                  icon={<SaveOutlined />}
                  type="primary"
                  onClick={() => handleOpenSaveViewModal('table')}
                  disabled={applyingView || loading}
                  style={{ marginTop: 24 }}
                >
                  Save View
                </Button>
              </Tooltip>
            )}
          </Col>
        </Row>
      </div>


      {/* Filters Section - All in One Row */}
      <Row gutter={[8, 16]} style={{ marginBottom: 20 }}>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>UPCs</label>
          <Select
            mode="multiple"
            style={{ width: '200px' }}
            placeholder="Select UPCs"
            value={selection.upcs}
            onChange={handleUpcsChange}
            options={filterOptions.upcs.map(([value, label]) => ({ value, label }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No UPCs found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Forecast Nodes</label>
          <Select
            mode="multiple"
            style={{ width: '180px' }}
            placeholder="Select Nodes"
            value={selection.forecastNodes}
            onChange={handleForecastNodesChange}
            options={filterOptions.forecastNodes.map(node => ({ value: node, label: node }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No nodes found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Date Range</label>
          <Select
            value={dateRange}
            onChange={handleDateRangeChange}
            options={dateRangeOptions}
            style={{ width: '120px' }}
          />
        </Col>
        <Col>
          {dateRange === 'custom' && (
            <>
              <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Custom Dates</label>
              <RangePicker
                value={customDateRange}
                onChange={handleCustomDateRangeChange}
                style={{ width: '200px' }}
              />
            </>
          )}
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Product</label>
          <Select
            mode="multiple"
            style={{ width: '140px' }}
            placeholder="Products"
            value={selection.products}
            onChange={handleProductsChange}
            options={filterOptions.products.map(product => ({ value: product, label: product }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No products found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Division</label>
          <Select
            mode="multiple"
            style={{ width: '120px' }}
            placeholder="Divisions"
            value={selection.divisions}
            onChange={handleDivisionsChange}
            options={filterOptions.divisions.map(division => ({ value: division, label: division }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No divisions found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Color</label>
          <Select
            mode="multiple"
            style={{ width: '100px' }}
            placeholder="Colors"
            value={selection.colors}
            onChange={handleColorsChange}
            options={filterOptions.colors.map(color => ({ value: color, label: color }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No colors found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Size</label>
          <Select
            mode="multiple"
            style={{ width: '100px' }}
            placeholder="Sizes"
            value={selection.sizes}
            onChange={handleSizesChange}
            options={filterOptions.sizes.map(size => ({ value: size, label: size }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No sizes found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Category</label>
          <Select
            mode="multiple"
            style={{ width: '120px' }}
            placeholder="Categories"
            value={selection.categories}
            onChange={handleCategoriesChange}
            options={filterOptions.categories.map(category => ({ value: category, label: category }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No categories found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Life Status</label>
          <Select
            mode="multiple"
            style={{ width: '120px' }}
            placeholder="Status"
            value={selection.lifeStatuses}
            onChange={handleLifeStatusesChange}
            options={filterOptions.lifeStatuses.map(status => ({ value: status, label: status }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No statuses found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Specification</label>
          <Select
            mode="multiple"
            style={{ width: '120px' }}
            placeholder="Specs"
            value={selection.specifications}
            onChange={handleSpecificationsChange}
            options={filterOptions.specifications.map(spec => ({ value: spec, label: spec }))}
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No specs found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Include Forecast</label>
          <Checkbox
            checked={selection.includeForecast}
            onChange={(e) => setSelection(prev => ({ ...prev, includeForecast: e.target.checked }))}
          />
        </Col>
      </Row>

      {/* Data Display Tabs */}
      <Tabs
        defaultActiveKey="chart"
        size="large"
        items={tabItems}
        onChange={setActiveTab}
      />

      {/* Save View Modal */}
      <Modal
        title="Save Table View"
        open={saveViewModalVisible}
        onOk={handleSaveViewSubmit}
        onCancel={() => setSaveViewModalVisible(false)}
        okText="Save"
        cancelText="Cancel"
      >
        <div style={{ marginBottom: 16 }}>
          <label style={{ fontWeight: 500, marginBottom: 8, display: 'block' }}>View Name:</label>
          <Input
            value={newViewName}
            onChange={(e) => setNewViewName(e.target.value)}
            placeholder="Enter a name for this view"
            maxLength={50}
          />
    </div>
        <div style={{ marginBottom: 16 }}>
          <label style={{ fontWeight: 500, marginBottom: 8, display: 'block' }}>Visibility:</label>
          <Switch
            checked={newViewIsPublic}
            onChange={setNewViewIsPublic}
            checkedChildren="Public"
            unCheckedChildren="Private"
          />
          <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
            {newViewIsPublic
              ? 'Public views can be seen by all users but only deleted by you'
              : 'Private views are only visible to you'
            }
          </div>
        </div>
      </Modal>
    </div >
  );
};

export default HistoricalSalesContent;
