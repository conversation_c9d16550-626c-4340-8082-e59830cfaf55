/* eslint-disable react/jsx-key */
/* eslint-disable react/no-unknown-property */
/* eslint-disable guard-for-in */
/* eslint-disable no-unused-vars */
/* eslint-disable array-callback-return */
import React, { useEffect, useState, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { db, api, auth } from '../firebase';
import dayjs from 'dayjs';
import { Typography, Layout, Input, Select, Table, DatePicker, Row, Col, Dropdown, Flex, Space, Button, message, Form, InputNumber, Tooltip, Popconfirm, Empty, ConfigProvider, Skeleton, Tag, Collapse } from 'antd';
import CSVImport from '../../components/pim/CsvImport';
import { EyeOutlined, EditOutlined, DeleteOutlined, DownOutlined, CheckOutlined, LineOutlined, EyeInvisibleOutlined, ExportOutlined, PictureOutlined, PlusOutlined, PlusCircleOutlined, DatabaseOutlined, TagOutlined, UploadOutlined, ClearOutlined, FilterOutlined } from '@ant-design/icons';
import { arrayRemove, getDocs, writeBatch, query, where, or } from 'firebase/firestore';
import { collection } from 'firebase/firestore';
import SavedView from '../../components/pim/SavedView';
import { defaultProductViews, defaultVariantViews, dummyProducts, dummyVariants, PRODUCT_TABLE, VARIANTS_TABLE } from '../../components/pim/template';
import * as XLSX from 'xlsx';
import FieldView from '../../components/pim/FieldView';
import ImportImages from '../../components/pim/ImportImages';
import { useAuthState } from 'react-firebase-hooks/auth';
const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const getProductsAsOptions = async () => {
  const query = `SELECT id, name FROM ${PRODUCT_TABLE}`;
  const { data: productList } = await api.runQueryOnCall({ options: { query } });
  return productList.map(product => ({ id: product.id, label: product.name }));
};

const removeColumnsAndFiltersFromViews = async (valueToRemove, viewType) => {
  const querySnapshot = await getDocs(collection(db, `pim${viewType}Views`));
  const batch = writeBatch(db);

  querySnapshot.forEach((doc) => {
    const data = doc.data();
    if (data.columns && data.columns.includes(valueToRemove)) {
      const docRef = doc.ref;
      batch.update(docRef, {
        columns: arrayRemove(valueToRemove)
      });
    }
    if (data.filters && data.filters.includes(valueToRemove)) {
      const docRef = doc.ref;
      batch.update(docRef, {
        filters: arrayRemove(valueToRemove)
      });
    }
  });

  await batch.commit();
  // console.log(`Removed ${valueToRemove} from all matching documents in pim${viewType}Views`);
};

const removeDefaultAndRelatedFields = async (valueToRemove, viewType) => {
  const querySnapshot = await getDocs(collection(db, `pim${viewType}Fields`));
  const batch = writeBatch(db);

  querySnapshot.forEach((doc) => {
    const data = doc.data();
    if (data.defaultField === valueToRemove) {
      const docRef = doc.ref;
      batch.update(docRef, {
        defaultField: ""
      });
    }
    if (data.relatedField === valueToRemove) {
      const docRef = doc.ref;
      batch.update(docRef, {
        relatedField: ""
      });
    }
  });

  await batch.commit();
  // console.log(`Removed ${valueToRemove} from all matching documents in pim${viewType}Fields`);
};

const PimProductTable = ({ userObj, viewType = 'Product' }) => {
  const navigate = useNavigate();
  const [user, authLoading] = useAuthState(auth);
  const [productsLoading, setProductsLoading] = useState(true);
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [viewList, setViewList] = useState([]);
  const [openModal, setOpenModal] = useState('');
  const [currentSavedView, setCurrentSavedView] = useState(
    viewType === 'Product'
      ? userObj?.currentProductView || defaultProductViews[0]
      : userObj?.currentVariantView || defaultVariantViews[0]
  );
  const [fields, setFields] = useState([]);
  const [platforms, setPlatforms] = useState([]);
  const [filterValues, setFilterValues] = useState({});
  const [tableHeight, setTableHeight] = useState(600);
  const contentRef = useRef(null);
  const [filterForm] = Form.useForm();

  useEffect(() => {
    if (authLoading) {
      return;
    }
    if (!user) navigate('/login');
  }, [user, authLoading]);

  useEffect(() => {
    const updateTableHeight = () => {
      if (contentRef.current) {
        const contentTop = contentRef.current.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        const newHeight = windowHeight - contentTop - 185;
        setTableHeight(Math.max(newHeight, 200));
      }
    };

    // Only calculate height once loading is complete
    if (!productsLoading) {
      updateTableHeight();
    }

    // Handle window resizing
    const debouncedUpdateHeight = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateTableHeight, 100);
    };

    let timeoutId;
    window.addEventListener('resize', debouncedUpdateHeight);

    return () => {
      window.removeEventListener('resize', debouncedUpdateHeight);
      clearTimeout(timeoutId);
    };
  }, [productsLoading]);

  useEffect(() => {
    if (!userObj || !userObj.userPermissions) return;
    if (openModal !== '') return; // Only re-fetch when they close the modal
    const fetchData = async () => {
      const productFields = await getDocs(collection(db, `pim${viewType}Fields`));
      const platforms = await getDocs(collection(db, `pimPlatforms`));
      const fetchedProductFields = productFields.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      const fetchedPlatforms = platforms.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      // console.log('fetched product fields', fetchedProductFields);
      const productIdField = fetchedProductFields.findIndex(field => field.fieldId === 'productId');
      if (viewType === 'Variant' && productIdField !== -1) {
        fetchedProductFields[productIdField].options = await getProductsAsOptions();
      };
      setFields(fetchedProductFields);
      setPlatforms(fetchedPlatforms);

      fetchProducts(fetchedProductFields.filter(field => currentSavedView.columns.includes(field.fieldId) || currentSavedView.filters.includes(field.fieldId)));
      fetchViews();
    };
    fetchData();
  }, [userObj, openModal]);

  const fetchProducts = async (fields) => {
    const CHUNK_SIZE = 2000;
    let allProducts = [];

    try {
      setProductsLoading(true);

      let processedFields = viewType === 'Variant'
        ? fields.map(field => field?.relatedField?.startsWith('product.') ? field.relatedField : `v.${field.fieldId}`)
        : fields.map(field => field.fieldId);

      let query = '';

      if (fields.length > 100) { // Chunking rows for large number of fields
        // console.log('Chunking rows for large number of fields');
        const countQuery = viewType === 'Variant'
          ? `SELECT COUNT(*) as count FROM ${VARIANTS_TABLE}`
          : `SELECT COUNT(*) as count FROM ${viewType == 'Product' ? PRODUCT_TABLE : VARIANTS_TABLE}`;

        const { data: countResult } = await api.runQueryOnCall({ options: { query: countQuery } });
        const totalRows = countResult[0].count;

        for (let offset = 0; offset < totalRows; offset += CHUNK_SIZE) {
          query = '';
          if (viewType === 'Variant') {
            query = `SELECT ${processedFields.join(',')} 
                  FROM ${PRODUCT_TABLE} product 
                  RIGHT JOIN ${VARIANTS_TABLE} v ON product.id = v.productId
                  LIMIT ${CHUNK_SIZE} OFFSET ${offset}`;
          } else {
            query = `SELECT ${processedFields.join(',')} 
                  FROM ${viewType == 'Product' ? PRODUCT_TABLE : VARIANTS_TABLE}
                  LIMIT ${CHUNK_SIZE} OFFSET ${offset}`;
          }

          const { data: chunkProducts } = await api.runQueryOnCall({ options: { query } });
          allProducts = [...allProducts, ...chunkProducts];
        }
      } else {
        if (viewType === 'Variant') {
          query = `SELECT ${processedFields.join(',')} 
                  FROM ${PRODUCT_TABLE} product 
                  RIGHT JOIN ${VARIANTS_TABLE} v ON product.id = v.productId`;
        } else {
          query = `SELECT ${processedFields.join(',')} 
                  FROM ${viewType == 'Product' ? PRODUCT_TABLE : VARIANTS_TABLE}`;
        }

        const { data: productList } = await api.runQueryOnCall({ options: { query } });
        allProducts = productList;
      }

      // console.log('productList', allProducts);
      setProducts(allProducts);
      setFilteredProducts(allProducts);
    } catch (error) {
      console.error('Error fetching products:', error);
      message.error('Failed to fetch all product data. Please try again.');
    } finally {
      setProductsLoading(false);
    }
  };
  const fetchViews = async () => {
    // Get user's private views + all public views
    const q = query(
      collection(db, `pim${viewType}Views`),
      or(
        where('createdBy', '==', userObj.id), // User's own views (private)
        where('public', '==', true) // All public views (note: PIM uses 'public' not 'isPublic')
      )
    );
    const viewsRef = await getDocs(q);
    setViewList(viewsRef.docs.map((doc) => ({ id: doc.id, ...doc.data() })));
  };
  const fetchProductsAsOptions = async () => {
    const query = `SELECT id, name, netsuite_id FROM ${PRODUCT_TABLE}`;
    const { data: productList } = await api.runQueryOnCall({ options: { query } });
    return productList.map(product => ({ id: product.id, label: product.name, netsuiteId: product.netsuite_id }));
  };

  const handleEditProduct = (product) => {
    navigate(`/pim/${viewType.toLowerCase()}s/edit/${product.id}`);
  };
  const handleViewProduct = (product) => {
    navigate(`/pim/${viewType.toLowerCase()}s/view/${product.id}`);
  };
  const handleHideProduct = async (product) => {
    // console.log('Hide', product);
    product.hidden = !product.hidden;
    const query = `UPDATE ${viewType == 'Product' ? PRODUCT_TABLE : VARIANTS_TABLE} SET hidden = ${product.hidden} WHERE id = ${product.id}`;
    try {
      await api.bigQueryRunQueryOnCall({ query });
      const updatedProducts = products.map((p) => (p.id === product.id ? product : p));
      setProducts(updatedProducts);
      filterProducts(updatedProducts, filterValues);
    } catch (error) {
      console.error('Error hiding product:', error);
    }
  };
  const handleDeleteProduct = (product) => {
    // console.log('Delete', product);
    const query = `DELETE FROM ${viewType == 'Product' ? PRODUCT_TABLE : VARIANTS_TABLE} WHERE id = '${product.id}'`;
    api.bigQueryRunQueryOnCall({ options: { query } }).then(() => {
      const updatedProducts = products.filter((p) => p.id !== product.id);
      setProducts(updatedProducts);
      filterProducts(updatedProducts, filterValues);
    });
  };

  const handleDeleteColumn = (column) => {
    setCurrentSavedView({ ...currentSavedView, columns: currentSavedView.columns.filter((col) => col !== column) });
    removeColumnsAndFiltersFromViews(column, viewType);
    removeDefaultAndRelatedFields(column, viewType);
    api.bigQueryDeleteColumnOnCall({ datasetId: 'items', tableId: viewType.toLowerCase() + 's', columnName: column });

    const queryList = [
      { key: 'fieldId', operator: '==', val: column },
    ];

    api.getFirebaseQueryDataOnCall({ collectionName: `pim${viewType}Fields`, queryList: queryList }).then((docs) => {
      // console.log(docs.data[0]);
      api.deleteDocOnCall({ collectionName: `pim${viewType}Fields`, docId: docs.data[0].id });
    });
  };

  const handleSyncToNetsuite = async (product) => {
    // Check that the product has all the required fields for NS
    // Remove "netsuite_"/etc. from field names
    // product.custitem28 = viewType === 'Product'; // Product or Variant
    // Product type -- name*, inactive, sku code, weight (in oz)

    const netsuiteData = {};
    Object.keys(product).forEach((field) => {
      if (field.startsWith('netsuite_')) {
        netsuiteData[field.split('netsuite_')[1]] = product[field];
      }
    });

    // console.log(netsuiteData);

    const createResp = await api.createNetSuiteRecordOnCall({ recordType: 'customrecord_product_type', recordData: netsuiteData });
    // console.log(createResp);

    if (!createResp.data.status) {
      message.error('Error creating record in NetSuite');
      return;
    }
    // Required fields for Inventory Item:
    // Item Name/Number
    // Subitem Of ? get ID from above?
    // Tax Schedule -- Taxable / Not Taxable
    // Product Type
    // Reserve Inventory Quantity
    // Vendors.Vendor?
    // Preferred Bin Numbers.Location?
    // Shopify US Sales Price
    // Shopify UK Sales Price
    // Communication Files?
    // Check response
    // Show success or error message
  };

  const handleViewMenuClick = (e) => {
    const view = viewList.find(view => view.id === e.key);
    // console.log(view);
    api.modifyDocOnCall({
      collectionName: 'users', docId: user.uid, info: {
        [viewType === 'Product' ? 'currentProductView' : 'currentVariantView']: view
      }
    });
    handleClearFilters();
    setCurrentSavedView(view);
    fetchProducts(fields.filter(field => view.columns.includes(field.fieldId) || view.filters.includes(field.fieldId)));
  };

  const handleFilterChange = (fieldId, value) => {
    const updatedFilterValues = {
      ...filterValues
    };

    // Remove the filter if value is empty, undefined, null, or an empty array
    if (value === '' || value === undefined || value === null ||
      (Array.isArray(value) && value.length === 0)) {
      delete updatedFilterValues[fieldId];
    } else {
      updatedFilterValues[fieldId] = value;
    }

    setFilterValues(updatedFilterValues);
    filterProducts(products, updatedFilterValues);
  };
  const filterProducts = (products, filterValues) => { // TODO: Double check filter logic
    setFilteredProducts(products.filter((product) => {
      return Object.keys(filterValues).every((fieldId) => {
        const value = filterValues[fieldId];
        if (value === '' || value === undefined) return true;

        const field = fields.find(field => field.fieldId === fieldId);
        if (!field) return true;

        if (field.fieldType === 'text' || field.fieldType === 'longtext') {
          return product[fieldId]?.toLowerCase().includes(value.toLowerCase());
        }
        if (field.fieldType === 'select') {
          return value.includes(product[fieldId]);
        }
        if (field.fieldType === 'date') {
          return (product[fieldId] >= value[0] && product[fieldId] <= value[1]);
        }
        if (['currency', 'integer', 'decimal', 'percent'].includes(field.fieldType)) {
          return (product[fieldId] >= value.from && product[fieldId] <= value.to);
        }
        if (field.fieldType === 'checkbox') {
          return (value === 'yes' && product[fieldId]) || (value === 'no' && !product[fieldId]);
        }
        return true;
      });
    }));
  };
  const handleClearFilters = () => {
    filterForm.resetFields();
    setFilterValues({});
    filterProducts(products, {});
  };

  const exportToExcel = async ({ itemList }) => {
    if (!itemList.length) {
      message.error('No items to export');
      return;
    }
    const data = [];
    itemList.map(item => {
      let temData = {
        ID: item.id
      };
      currentSavedView.columns.map(column => {
        const field = fields.find(field => field.fieldId === column);

        if (field.fieldType) {
          if (field.fieldType === 'currency') {
            temData[field.label] = (item[column]) ? '$' + item[column] : '';
          } else if (field.fieldType === 'percent') {
            temData[field.label] = (item[column]) ? item[column] + '%' : '';
          } else if (field.fieldType === 'checkbox') {
            temData[field.label] = (item[column]) ? 'Yes' : 'No';
          } else if (field.fieldType === 'date') {
            temData[field.label] = (item[column]) ? dayjs((item[column].value) ? new Date(item[column].value) : item[column]).format('MM/DD/YYYY') : '';
          } else if (field.fieldType === 'select') {
            temData[field.label] = ((field.options.find(opt => opt.id === item[column]))) ? (field.options.find(opt => opt.id === item[column])).label : '';
          } else {
            temData[field.label] = item[column];
          }
        }
      });
      data.push(temData);
    });
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(data);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Items');
    XLSX.writeFile(workbook, 'items.xlsx');
  };

  const actionColumn = {
    title: 'Actions',
    render: (_, record) => (
      <Flex wrap gap="small">
        <Tooltip title="View">
          <Button onClick={() => handleViewProduct(record)} icon={<EyeOutlined />}></Button>
        </Tooltip>
        <Tooltip title="Edit">
          <Button onClick={() => handleEditProduct(record)} icon={<EditOutlined />}></Button>
        </Tooltip>
        <Tooltip title="Toggle Hide">
          <Button onClick={() => handleHideProduct(record)} icon={<EyeInvisibleOutlined />}></Button>
        </Tooltip>
        {/* <Tooltip title="Sync to Netsuite">
          <Button onClick={() => handleSyncToNetsuite(record)} icon={<CloudUploadOutlined />}></Button>
        </Tooltip> */}
        {userObj.userPermissions.filter(permission => permission.hasAccess).map(permission => permission.technicalName).includes(`edit:${viewType.toLowerCase()}s`) &&
          <Tooltip title="Delete">
            <Popconfirm title="Are you sure you want to delete this item?" onConfirm={() => handleDeleteProduct(record)}>
              <Button danger icon={<DeleteOutlined />} ></Button>
            </Popconfirm>
          </Tooltip>
        }
      </Flex>
    ),
    dataIndex: 'id',
  };

  const transformColumns = (columns) => {
    return columns.map(column => {
      const field = fields.find(field => field.fieldId === column);
      const columnConfig = {
        title: (
          <Space align='center'>
            <Tooltip title={field.fieldId}>
              <Text strong>{fields.find((field) => field.fieldId === column)?.label || column}</Text>
            </Tooltip>
            {field.platform !== 'PRIMARY' && userObj.userPermissions.filter(permission => permission.hasAccess).map(permission => permission.technicalName).includes(`edit:${viewType.toLowerCase()}s`) &&
              <Tooltip title="Delete">
                <Popconfirm title="Are you sure you want to delete this column?" onConfirm={() => handleDeleteColumn(column)}>
                  <Button danger shape='circle' type='text' icon={<DeleteOutlined />}></Button>
                </Popconfirm>
              </Tooltip>
            }
          </Space>
        ),
        dataIndex: column,
        key: column,
        render: (value, record) => {
          if (field.fieldId === 'productId') {
            return <Link to={`/pim/products/view/${value}`}>{fields.find(field => field.fieldId === 'productId').options.find(opt => opt.id === value)?.label || value}</Link>;
          }
          let num;
          switch (field.fieldType) {
            case 'currency':
              num = Number(value);
              if (!isNaN(num)) {
                return '$' + num.toFixed(2);
              }
              return value;
            case 'date':
              if (!value || !value.value || value.value === 'null') return '';
              return dayjs(value.value).format('MM/DD/YYYY');
            case 'url':
              return <Link to={value} target="_blank">{value}</Link>;
            case 'image':
              if (!record[column] || record[column] === 'null') {
                return <Skeleton.Image style={{ width: '100px', height: '100px' }} />;
              }
              return (
                <img
                  src={record[column]}
                  style={{
                    height: '100px',
                    width: '100px',
                    objectFit: 'contain'
                  }}
                  loading="lazy"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='; // transparent placeholder
                  }}
                />
              );
            case 'multiimage':
              if (!record[column]) {
                return <Skeleton.Image style={{ width: '100px', height: '100px' }} />;
              }
              return record[column].map((img, index) => (
                <img
                  key={index}
                  src={img}
                  style={{
                    height: '100px',
                    width: '100px',
                    objectFit: 'contain',
                    marginRight: '4px'
                  }}
                  loading="lazy"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='; // transparent placeholder
                  }}
                />
              ));
            case 'checkbox':
              return record[column] ? 'Yes' : 'No';
            case 'percent':
              num = Number(value);
              if (!isNaN(num)) {
                return num.toFixed(2) + '%';
              }
              return value;
            default: // text, longtext, integer, decimal, select
              return value;
          }
        },
      };
      if (!field.fieldType.includes('image')) {
        columnConfig.sorter = (a, b) => {
          // Handle null/undefined values
          if (!a[column] && !b[column]) return 0;
          if (!a[column]) return -1;
          if (!b[column]) return 1;

          switch (field.fieldType) {
            case 'currency':
            case 'integer':
            case 'decimal':
            case 'percent':
              return Number(a[column]) - Number(b[column]);
            case 'date':
              return new Date(a[column]) - new Date(b[column]);
            case 'checkbox':
              return (a[column] === b[column]) ? 0 : a[column] ? 1 : -1;
            case 'select':
              const optA = field.options.find(opt => opt.id === a[column])?.label || '';
              const optB = field.options.find(opt => opt.id === b[column])?.label || '';
              return optA.localeCompare(optB);
            default: // text, longtext, url, etc.
              return (a[column]?.toString() || '').localeCompare(b[column]?.toString() || '');
          }
        };
      };
      return columnConfig;
    });
  };

  if (productsLoading) {
    return <div>Loading {viewType}s...</div>;
  }
  return (
    <Layout style={{ minHeight: "95vh", maxHeight: "95vh" }}>
      <Flex align="center" justify="space-between" style={{ marginBottom: 16 }}>
        <Space>
          {viewType === 'Product' ? (
            <DatabaseOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          ) : (
            <TagOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          )}
          <Space direction="vertical" size={0}>
            <Typography.Title level={4} style={{ margin: 0 }}>
              {viewType} Management
            </Typography.Title>
            <Typography.Text type="secondary">
              {filteredProducts.length} {viewType.toLowerCase()}
              {filteredProducts.length === 1 ? '' : 's'} • {currentSavedView.name}
            </Typography.Text>
          </Space>
        </Space>

        <Flex wrap gap="small">
          <Space wrap>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate(`/pim/${viewType.toLowerCase()}s/edit/new`)}
            >
              Add {viewType}
            </Button>
          </Space>
        </Flex>
      </Flex>

      <Flex justify="space-between" align="center" style={{ marginBottom: 16 }}>
        <Dropdown.Button
          menu={{
            items: viewList.map((view) => ({
              label: (
                <div style={{
                  maxWidth: '200px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  {view.id === currentSavedView.id ? <CheckOutlined /> : <LineOutlined />}
                  <span>{view.name}</span>
                </div>
              ),
              key: view.id,
              title: view.name
            })),
            onClick: handleViewMenuClick
          }}
          placement="bottomLeft"
          icon={<DownOutlined />}
          onClick={() => setOpenModal('savedView')}
        >
          <div style={{
            maxWidth: '200px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {currentSavedView.name || 'Select View'}
          </div>
        </Dropdown.Button>
        <Space size="middle" wrap={false}>
          <Button icon={<ExportOutlined />} onClick={() => exportToExcel({ itemList: filteredProducts })}>
            Export
          </Button>
          <CSVImport
            fields={fields}
            products={products}
            setProducts={setProducts}
            fetchProductsAsOptions={fetchProductsAsOptions}
            viewType={viewType}
          />
          <Button icon={<PictureOutlined />} onClick={() => setOpenModal('importImages')}>
            Import Images
          </Button>
          <Button icon={<PlusCircleOutlined />} onClick={() => setOpenModal('fieldView')}>
            Add Field
          </Button>
        </Space>
      </Flex>
      {currentSavedView.filters.length > 0 && (
        <Collapse
          ghost
          style={{ marginBottom: 16 }}
          defaultActiveKey={Object.keys(filterValues).length > 0 ? ['filters'] : []}
        >
          <Collapse.Panel
            key="1"
            header={
              <Space>
                <FilterOutlined />
                <Link
                  onClick={(e) => {
                    e.stopPropagation();
                    setOpenModal('savedView');
                  }}
                >
                  Filters
                </Link>
                {Object.keys(filterValues).length > 0 && (
                  <Tag color="blue">
                    {Object.keys(filterValues).length} active
                  </Tag>
                )}
                {Object.keys(filterValues).length > 0 && (
                  <Button
                    danger
                    size="small"
                    icon={<ClearOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClearFilters();
                    }}
                  >
                    Clear All
                  </Button>
                )}
              </Space>
            }
          >
            <Form
              form={filterForm}
              layout="inline"
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '16px',
              }}
            >
              {
                currentSavedView.filters.map((filterField) => {
                  const field = fields.find(field => field.fieldId === filterField);

                  return (
                    <Form.Item
                      label={field.label}
                      key={field.fieldId}
                      name={field.fieldId}
                      style={{ marginBottom: '10px' }}
                    >
                      {['text', 'longtext'].includes(field.fieldType) && (
                        <Input
                          placeholder={`Search ${field.label}`}
                          allowClear
                          initialValue={filterValues[field.fieldId] || ''}
                          onChange={(e) => {
                            handleFilterChange(field.fieldId, e.target.value);
                          }}
                        />
                      )}
                      {['url'].includes(field.fieldId) && (
                        <Input
                          placeholder={`Search ${field.label}`}
                          allowClear
                          initialValue={filterValues[field.fieldId] || ''}
                          rules={[{ type: 'url', warningOnly: true }]}
                          onChange={(e) => {
                            handleFilterChange(field.fieldId, e.target.value);
                          }}
                        />
                      )}
                      {['currency', 'integer', 'decimal', 'percent'].includes(field.fieldType) && (
                        <>
                          <InputNumber
                            placeholder="Min"
                            prefix={field.fieldType === 'currency' ? '$' : ''}
                            suffix={(field.fieldType === 'percent') ? '%' : ''}
                            initialValue={(filterValues[field.fieldId] && filterValues[field.fieldId].from) ? filterValues[field.fieldId].from : ''}
                            onChange={(val) => {
                              handleFilterChange(field.fieldId, { from: parseFloat(val), to: ((filterValues[field.fieldId] && filterValues[field.fieldId].to) ? filterValues[field.fieldId].to : '') });
                            }}
                          />
                          <span> - </span>
                          <InputNumber
                            placeholder="Max"
                            name={field.fieldId}
                            prefix={field.fieldType === 'currency' ? '$' : ''}
                            suffix={(field.fieldType === 'percent') ? '%' : ''}
                            initialValue={(filterValues[field.fieldId] && filterValues[field.fieldId].to) ? filterValues[field.fieldId].to : ''}
                            onChange={(val) => {
                              handleFilterChange(field.fieldId, { from: ((filterValues[field.fieldId] && filterValues[field.fieldId].from) ? filterValues[field.fieldId].from : ''), to: parseFloat(val) });
                            }}
                          />
                        </>
                      )}
                      {field.fieldType === 'date' && (
                        <RangePicker
                          format="MM/DD/YYYY"
                          initialValue={filterValues[field.fieldId] || ''}
                          onChange={(val) => {
                            handleFilterChange(field.fieldId, val);
                          }}
                        />
                      )}
                      {field.fieldType === 'select' && (
                        <Select
                          mode="multiple"
                          allowClear
                          name={field.fieldId}
                          placeholder="Select..."
                          style={{ width: '200px' }}
                          initialValue={filterValues[field.fieldId] || []}
                          showSearch
                          filterOption={(input, option) =>
                            (option?.key ?? '').toLowerCase().includes(input.toLowerCase()) ||
                            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                          }
                          onChange={(val) => {
                            handleFilterChange(field.fieldId, val);
                          }}
                        >
                          {
                            field.options.map((option) => {
                              return (
                                <Option key={option.id} value={option.id} label={option.label}>{option.label}</Option>
                              );
                            })
                          }
                        </Select>
                      )}
                      {field.fieldType === 'checkbox' && (
                        <Select
                          allowClear
                          name={field.fieldId}
                          placeholder="All"
                          style={{ width: '80px' }}
                          initialValue={filterValues[field.fieldId] || []}
                          onChange={(val) => {
                            handleFilterChange(field.fieldId, val);
                          }}
                        >
                          <Option value="yes">Yes</Option>
                          <Option value="no">No</Option>
                        </Select>
                      )}
                    </Form.Item>
                  );
                })
              }
            </Form>
          </Collapse.Panel>
        </Collapse>
      )
      }
      <SavedView
        fields={fields}
        savedViewData={currentSavedView}
        setCurrentSavedView={setCurrentSavedView}
        savedViewModalIsOpen={openModal === 'savedView'}
        setOpenModal={setOpenModal}
        handleClearFilters={handleClearFilters}
        views={viewList}
        setViews={setViewList}
        user={userObj}
        viewType={viewType}
      />
      <FieldView
        fieldViewModalIsOpen={openModal === 'fieldView'}
        setOpenModal={setOpenModal}
        fields={fields}
        platformList={platforms}
        currentFormView={currentSavedView.id}
        viewType={viewType}
      />
      <ImportImages
        setOpenModal={setOpenModal}
        importImagesModalIsOpen={openModal === 'importImages'}
      />
      <div ref={contentRef}>
        <ConfigProvider renderEmpty={() => <Empty description={`No ${viewType}s found`} />}>
          <Table
            bordered
            style={{ maxWidth: '99%', overflow: 'scroll', margin: '0 auto' }}
            columns={[...transformColumns(currentSavedView.columns), actionColumn]}
            dataSource={filteredProducts}
            pagination={{
              defaultPageSize: 50,
              responsive: true,
              showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
            showSorterTooltip={{ target: 'sorter-icon' }}
            scroll={{ x: (currentSavedView.columns.length + 1) * 300, y: tableHeight }}
          />
        </ConfigProvider>
      </div>
    </Layout >
  );
};

export default PimProductTable;